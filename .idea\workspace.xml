<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="fbc7cb23-f43b-4a80-b1c6-2c59c8b629e8" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ComposerConfigs">
    <option name="configs">
      <option value="$PROJECT_DIR$/common/components/PDFMerge/TCPDF/composer.json" />
    </option>
  </component>
  <component name="ComposerSettings" synchronizationState="SYNCHRONIZE">
    <pharConfigPath>$PROJECT_DIR$/composer.json</pharConfigPath>
    <execution>
      <phar pharPath="D:\composer\composer.phar" interpreterId="28a25113-7429-4eb3-82b5-28cc5f93c548" />
    </execution>
  </component>
  <component name="PhpWorkspaceProjectConfiguration" interpreter_name="D:\Program Files\php\php-5.6.40-Win32-VC11-x64\php.exe">
    <include_path>
      <path value="$PROJECT_DIR$/vendor/Excel/PHPExcel" />
      <path value="$PROJECT_DIR$/vendor/PHPExcel/CachedObjectStorage" />
      <path value="$PROJECT_DIR$/vendor/PHPExcel/CalcEngine" />
      <path value="$PROJECT_DIR$/vendor/PHPExcel/Calculation" />
      <path value="$PROJECT_DIR$/vendor/PHPExcel/Cell" />
      <path value="$PROJECT_DIR$/vendor/PHPExcel/Chart" />
      <path value="$PROJECT_DIR$/vendor/PHPExcel/Reader" />
      <path value="$PROJECT_DIR$/vendor/PHPExcel/RichText" />
      <path value="$PROJECT_DIR$/vendor/PHPExcel/Shared" />
      <path value="$PROJECT_DIR$/vendor/PHPExcel/Style" />
      <path value="$PROJECT_DIR$/vendor/PHPExcel/Worksheet" />
      <path value="$PROJECT_DIR$/vendor/PHPExcel/Writer" />
      <path value="$PROJECT_DIR$/vendor/PHPExcel/locale" />
      <path value="$PROJECT_DIR$/vendor/PHPWord/Section" />
      <path value="$PROJECT_DIR$/vendor/PHPWord/Shared" />
      <path value="$PROJECT_DIR$/vendor/PHPWord/Style" />
      <path value="$PROJECT_DIR$/vendor/PHPWord/Writer" />
      <path value="$PROJECT_DIR$/vendor/PHPWord/_staticDocParts" />
      <path value="$PROJECT_DIR$/vendor/bower-asset/bootstrap" />
      <path value="$PROJECT_DIR$/vendor/bower-asset/jquery" />
      <path value="$PROJECT_DIR$/vendor/bower-asset/jquery.inputmask" />
      <path value="$PROJECT_DIR$/vendor/bower-asset/punycode" />
      <path value="$PROJECT_DIR$/vendor/bower-asset/typeahead.js" />
      <path value="$PROJECT_DIR$/vendor/bower-asset/yii2-pjax" />
      <path value="$PROJECT_DIR$/vendor/bower/bootstrap" />
      <path value="$PROJECT_DIR$/vendor/bower/jquery" />
      <path value="$PROJECT_DIR$/vendor/bower/jquery.inputmask" />
      <path value="$PROJECT_DIR$/vendor/bower/punycode" />
      <path value="$PROJECT_DIR$/vendor/bower/typeahead.js" />
      <path value="$PROJECT_DIR$/vendor/bower/yii2-pjax" />
      <path value="$PROJECT_DIR$/vendor/cboden/ratchet" />
      <path value="$PROJECT_DIR$/vendor/cebe/markdown" />
      <path value="$PROJECT_DIR$/vendor/composer" />
      <path value="$PROJECT_DIR$/vendor/evenement/evenement" />
      <path value="$PROJECT_DIR$/vendor/ezyang/htmlpurifier" />
      <path value="$PROJECT_DIR$/vendor/fzaninotto/faker" />
      <path value="$PROJECT_DIR$/vendor/guzzlehttp/psr7" />
      <path value="$PROJECT_DIR$/vendor/laminas/laminas-escaper" />
      <path value="$PROJECT_DIR$/vendor/laminas/laminas-zendframework-bridge" />
      <path value="$PROJECT_DIR$/vendor/paragonie/constant_time_encoding" />
      <path value="$PROJECT_DIR$/vendor/paragonie/random_compat" />
      <path value="$PROJECT_DIR$/vendor/pclzip/pclzip" />
      <path value="$PROJECT_DIR$/vendor/php-amqplib/php-amqplib" />
      <path value="$PROJECT_DIR$/vendor/phpdiff/Diff" />
      <path value="$PROJECT_DIR$/vendor/phpoffice/common" />
      <path value="$PROJECT_DIR$/vendor/phpoffice/phpword" />
      <path value="$PROJECT_DIR$/vendor/phpseclib/phpseclib" />
      <path value="$PROJECT_DIR$/vendor/phpspec/php-diff" />
      <path value="$PROJECT_DIR$/vendor/psr/http-message" />
      <path value="$PROJECT_DIR$/vendor/ralouphie/getallheaders" />
      <path value="$PROJECT_DIR$/vendor/ratchet/rfc6455" />
      <path value="$PROJECT_DIR$/vendor/react/cache" />
      <path value="$PROJECT_DIR$/vendor/react/dns" />
      <path value="$PROJECT_DIR$/vendor/react/event-loop" />
      <path value="$PROJECT_DIR$/vendor/react/promise" />
      <path value="$PROJECT_DIR$/vendor/react/socket" />
      <path value="$PROJECT_DIR$/vendor/react/stream" />
      <path value="$PROJECT_DIR$/vendor/spout-2.7.3/src" />
      <path value="$PROJECT_DIR$/vendor/swiftmailer/swiftmailer" />
      <path value="$PROJECT_DIR$/vendor/symfony/http-foundation" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-mbstring" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-php70" />
      <path value="$PROJECT_DIR$/vendor/symfony/routing" />
      <path value="$PROJECT_DIR$/vendor/tcpdf/config" />
      <path value="$PROJECT_DIR$/vendor/tcpdf/examples" />
      <path value="$PROJECT_DIR$/vendor/tcpdf/fonts" />
      <path value="$PROJECT_DIR$/vendor/tcpdf/include" />
      <path value="$PROJECT_DIR$/vendor/tcpdf/tools" />
      <path value="$PROJECT_DIR$/vendor/textalk/websocket" />
      <path value="$PROJECT_DIR$/vendor/yiisoft/yii2" />
      <path value="$PROJECT_DIR$/vendor/yiisoft/yii2-bootstrap" />
      <path value="$PROJECT_DIR$/vendor/yiisoft/yii2-codeception" />
      <path value="$PROJECT_DIR$/vendor/yiisoft/yii2-composer" />
      <path value="$PROJECT_DIR$/vendor/yiisoft/yii2-debug" />
      <path value="$PROJECT_DIR$/vendor/yiisoft/yii2-faker" />
      <path value="$PROJECT_DIR$/vendor/yiisoft/yii2-gii" />
      <path value="$PROJECT_DIR$/vendor/yiisoft/yii2-mongodb" />
      <path value="$PROJECT_DIR$/vendor/yiisoft/yii2-swiftmailer" />
    </include_path>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="2vOHUyibgeXdQOgKCRTkYy6khVf" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;PHPUnit.integle_ineln.executor&quot;: &quot;Debug&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;disable.download.node.interpreter.editor.notification&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/integle2025/integle_ineln&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;reference.webide.settings.project.settings.php&quot;,
    &quot;ts.external.directory.path&quot;: &quot;D:\\Program Files\\JetBrains\\PhpStorm 2024.1.4\\plugins\\javascript-plugin\\jsLanguageServicesImpl\\external&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;DatabaseDriversLRU&quot;: [
      &quot;postgresql&quot;
    ]
  }
}</component>
  <component name="RunManager">
    <configuration name="integle_ineln" type="PHPUnitRunConfigurationType" factoryName="PHPUnit" temporary="true">
      <TestRunner directory="$PROJECT_DIR$" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="PHPUnit.integle_ineln" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-1d06a55b98c1-0b3e54e931b4-JavaScript-PS-241.18034.69" />
        <option value="bundled-php-predefined-ba97393d7c68-48a1a656d44e-com.jetbrains.php.sharedIndexes-PS-241.18034.69" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="SvnConfiguration" cleanupOnStartRun="true">
    <configuration>C:\Users\<USER>\AppData\Roaming\Subversion</configuration>
    <supportedVersion>125</supportedVersion>
    <option name="runUnderTerminal" value="true" />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="fbc7cb23-f43b-4a80-b1c6-2c59c8b629e8" name="Changes" comment="" />
      <created>1744009224356</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1744009224356</updated>
      <workItem from="1744009224837" duration="18000" />
      <workItem from="1744009251911" duration="766000" />
      <workItem from="1744010032616" duration="68000" />
      <workItem from="1744010106519" duration="67000" />
      <workItem from="1744010180405" duration="3285000" />
      <workItem from="1744014440704" duration="577000" />
      <workItem from="1744015028551" duration="1097000" />
      <workItem from="1744016141784" duration="2692000" />
      <workItem from="1744076064628" duration="4922000" />
      <workItem from="1744094453345" duration="77159000" />
      <workItem from="1744599965121" duration="3915000" />
      <workItem from="1744683853130" duration="39592000" />
      <workItem from="1744969216511" duration="191000" />
      <workItem from="1744969424740" duration="837000" />
      <workItem from="1744970281178" duration="361000" />
      <workItem from="1744970741065" duration="42000" />
      <workItem from="1744970938898" duration="318000" />
      <workItem from="1745198818027" duration="98000" />
      <workItem from="1745201817723" duration="221000" />
      <workItem from="1745202565009" duration="90000" />
      <workItem from="1745216502832" duration="142000" />
      <workItem from="1745218947189" duration="645000" />
      <workItem from="1745220017844" duration="69096000" />
      <workItem from="1745717399309" duration="16442000" />
      <workItem from="1746495143708" duration="5418000" />
      <workItem from="1747013806228" duration="2022000" />
      <workItem from="1747120100011" duration="10613000" />
      <workItem from="1747309718775" duration="1861000" />
      <workItem from="1747619770670" duration="2557000" />
      <workItem from="1747967875116" duration="2373000" />
      <workItem from="1748223107603" duration="1175000" />
      <workItem from="1748225970063" duration="10331000" />
      <workItem from="1748914337618" duration="10186000" />
      <workItem from="1749019378555" duration="89000" />
      <workItem from="1749705023216" duration="12517000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="php">
          <url>file://$PROJECT_DIR$/frontend/views/setting/instruments_manage_page.php</url>
          <option name="timeStamp" value="1" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/frontend/web/js/instrument.js</url>
          <option name="timeStamp" value="2" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>