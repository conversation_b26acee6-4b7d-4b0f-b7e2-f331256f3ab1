<?php

namespace frontend\widget\experiment\modules\indraw;

use frontend\models\SubstrateModel;
use yii\helpers\ArrayHelper;

/**
 * 物料表物质对象. stoichiometry substance.
 * <AUTHOR>
 * @date   2023/12/21
 */
class StoiSubs
{

    /**
     * 从表格数据中提取用户输入的字段
     * @param array $substanceData
     * @return array
     * <AUTHOR>
     * @date   2024/4/29
     */
    static function getUserInputFields($substanceData) {
        $userInputFds = ArrayHelper::getValue($substanceData, ['user_input_fields']);
        return array_values(array_filter(explode(',', $userInputFds)));
    }

    /**
     * 构造用于页面显示的产物产率字符串
     * @param string $prdYld
     * @return string
     * <AUTHOR>
     * @date   2024/4/29
     */
    static function fmtPrdYldField($prdYld) {
        $companyDecimalConf = CompanyDecimalConf::getDecimalConf();
        $stoiSubs = (new StoiSubs())
            ->setSubsType('product')
            ->setSubsValues(['yield' => $prdYld])///手动构造需要格式的数据格式
            ->setCompanyDecimalConf($companyDecimalConf)
            ->setUserInputFdArr([]);
        $fmtPrdYld = $stoiSubs->getValueByFmtFn('yield', [StoiSubs::class, 'getFmtValue'], ['default_val' => 0]);
        return $fmtPrdYld;
    }

    /**
     * 构造产物产率的展示字符串
     * @param string $yldStr
     * @return string
     * <AUTHOR>
     * @date   2024/4/29
     */
    static function fmtPrdYldViewStr($yldStr) {
        return "{$yldStr}%";
    }

    /**
     * 构造支持多个产率
     * @param string $prdYldStr
     * @param array|null $config
     * @return array
     * <AUTHOR>
     * @date   2024/4/29
     */
    static function fmtMultiPrdYldField($prdYldStr, $config = []) {
        if (empty(trim($prdYldStr))) return [];
        $config = array_merge([
            'explode_separator' => ' ',
            'fmt_fun' => [StoiSubs::class, "fmtPrdYldViewStr"],
        ], $config);

        $explodeSep = $config['explode_separator'];
        $sPrdYldArr1 = array_values(array_filter(explode($explodeSep, $prdYldStr)));
        return array_map(function ($sPrdYield) use ($config) {
            $sPrdYld = StoiSubs::fmtPrdYldField($sPrdYield);
            return call_user_func_array($config['fmt_fun'], ['yldStr' => $sPrdYld]);
        }, $sPrdYldArr1);
    }

    /**
     * 根据substrate表part字段类型获取对于的物料类别
     * @param $materialPart
     * @return string
     * @throws \Exception
     */
    static function getSubstanceTypeByMaterialPart($materialPart) {
        switch ($materialPart) {
            case SubstrateModel::PartRct:
            case strval(SubstrateModel::PartRct):
                return 'reactant';
            case SubstrateModel::PartRgn:
            case strval(SubstrateModel::PartRgn):
                return 'reagent';
            case SubstrateModel::PartSol:
            case strval(SubstrateModel::PartSol):
                return 'solvent';
            case SubstrateModel::PartCnd:
            case strval(SubstrateModel::PartCnd):
                return 'condition';

        }
        throw new \Exception('未知的物料类型:' . $materialPart);
    }

    /**
     * 通过物料类型和字段名称获取物料字段值的小数位数设置
     * @param $substanceType
     * @param $fdKey
     * @return string|int
     */
    public static function getChemDecimalBySubstanceTypeAndFdKey($substanceType, $fdKey) {
        $_companyDecimalConfig = CompanyDecimalConf::getDecimalConf();
        return ArrayHelper::getValue($_companyDecimalConfig, [$substanceType, $fdKey]);
    }

    /** @var array 物料表字段名和小数点设置中的字段名映射 */
    const DecimalFdNameConf = [
        'reactant' => [
            'equivalent' => 'eq',
            'nb' => 'n',
            'molweight' => 'mw',
            'mass' => 'mass',
            'density' => 'd',
            'volume' => 'v',
            'temperature' => 'temperature',
            'pressure' => 'pressure',
        ],

        'reagent' => [
            'equivalent' => 'eq',
            'nb' => 'n',
            'molweight' => 'mw',
            'mass' => 'mass',
            'density' => 'd',
            'volume' => 'v',
            'temperature' => 'temperature',
            'pressure' => 'pressure',
        ],

        'solvent' => [
            'volume' => 'b',
            'mass' => 'mass',
            'density' => 'd',
            'bp' => 'bp',
            'ratio' => 'ratio',
        ],
        'product' => [
            'nb' => 'n',
            'molweight' => 'mw',
            'theo' => 'theo_mass',
            'yield' => 'yield',
            'mass' => 'mass',
            'purity' => 'purity',
            'product_mass_yield' => 'yield', //质量收率和收率共享小数点配置, 按照老版设置
        ],
    ];

    /**
     * @var array 字段在数据库中的名称 => 前端记录的用户输入字段的名称
     */
    const FdName2UserFdName = [
        'reactant' => [
            'equivalent' => 'eq',
            'nb' => 'nb',
            'molweight' => 'molweight',
            'mass' => 'mass',
            'density' => 'density',
            'volume' => 'volume',
            'temperature' => 'temperature',
            'pressure' => 'pressure',
        ],

        'reagent' => [
            'equivalent' => 'eq',
            'nb' => 'nb',
            'molweight' => 'molweight',
            'mass' => 'mass',
            'density' => 'density',
            'volume' => 'volume',
            'temperature' => 'temperature',
            'pressure' => 'pressure',
        ],

        'solvent' => [
            'volume' => 'volume',
            'mass' => 'mass',
            'density' => 'density',
            'bp' => 'bp',
            'ratio' => 'ratio',
        ],
        'product' => [
            'nb' => 'nb',
            'molweight' => 'molweight',
            'theo' => 'theo',
            'yield' => 'yield',
            'mass' => 'mass',
            'purity' => 'purity',
        ],
    ];

    /** @var string 物质类型=('reactant' | 'reagent' | 'solvent' | 'condition' | 'product') */
    private $_subsType;

    /** @var array 此物质各个字段值 */
    private $_subsValues;

    /** @var array 企业小数点设置 */
    private $_companyDecimalConf;

    /** @var string[] 用户输入的字段 */
    private $_userInputFdArr = [];


    //region 物质属性值的 getter/setters
    /** 物质类型 setter
     * @param string $subsType=('reactant' | 'reagent' | 'solvent' | 'product')
     * @return $this
     */
    public function setSubsType($subsType) {
        $this->_subsType = $subsType;
        return $this;
    }

    /** 物质字段值 setter
     * @param array $subsValues
     * @return $this;
     */
    public function setSubsValues($subsValues) {
        $defineValues = ArrayHelper::getValue($subsValues, 'define_value', []);
        $defineValues = is_string($defineValues) ? json_decode($defineValues, true) : $defineValues;
        $defineValuesMap = !empty($defineValues)?ArrayHelper::map($defineValues, 'name', 'value'):[];

        $subsValues = !empty($defineValuesMap)?array_merge($subsValues, $defineValuesMap):$subsValues;

        $this->_subsValues = $subsValues;
        return $this;
    }

    /** 企业物料表小数点设置 setter
     * @param mixed $companyDecimalConf
     * @return $this
     */
    public function setCompanyDecimalConf($companyDecimalConf) {
        $this->_companyDecimalConf = $companyDecimalConf;
        return $this;
    }

    /** 企业用户输入字段的setter
     * @param string[] $userInputFdArr
     * @return $this
     */
    public function setUserInputFdArr($userInputFdArr) {
        $this->_userInputFdArr = $userInputFdArr;
        return $this;
    }

    /**
     * 根据substrate表的信息设置物料信息
     * @param $substrate - substrate表物料
     * @param callable(array $substrate):array | null $propGetterCallback - 根据物料类型获取对应的物料值属性名
     * @return $this
     * @throws \Exception
     */
    public function setupBySubstrateModel($substrate, callable $propGetterCallback = null) {
        $props = [
            'subs_type' => self::getSubstanceTypeByMaterialPart(ArrayHelper::getValue($substrate, 'part', 1)),
            'subs_values' => $substrate,
            'user_inp_fields' => explode(',', ArrayHelper::getValue($substrate, 'user_input_fields', '')),
            'company_decimal_config' => CompanyDecimalConf::getDecimalConf(),
        ];
        if (isset($propGetterCallback)) {
            $newSubstrateProps = call_user_func($propGetterCallback, $substrate);
            $props = array_merge($props, $newSubstrateProps);
        }
        $this->setSubsType($props['subs_type'])
            ->setSubsValues($props['subs_values'])
            ->setCompanyDecimalConf($props['company_decimal_config'])
            ->setUserInputFdArr($props['user_inp_fields'])
        ;
        return $this;
    }

    //endregion


    /** 获取字段原始小数值
     * @param double $value - 字段原始小数字符串
     * @return string
     * <AUTHOR>
     * @date   2023/12/21
     */
    static function getCalValue($value) {
        return strval($value) ;
    }

    /** 获取字段按指定小数位数format后的值
     * @param double $value - 字段原始小数字符串
     * @param int $decimalConf - 物料表小数点设置
     * @return string
     * <AUTHOR>
     * @date   2023/12/21
     */
    static function getFmtValue($value, $decimalConf) {
        return number_format($value, $decimalConf, '.', '');
    }

    /** 获取默认值的选项的默认配置 */
    const DefFmtOptions = [
        'default_val' => 0, //! 当字段为0值时时的默认值
        'empty_val' => '', //! 当值为空时展示在表格的值
    ];

    /** 根据指定的format 方法获取物质字段的值
     * @param string|string[] $fdName - 字段名称
     * @param callable $fmtFn         - 对小数进行format的方法
     * @param array $options          - 获取名称的相关配置
     * @return string
     * <AUTHOR>
     * @date   2023/12/21
     */
    public function getValueByFmtFn($fdName, $fmtFn, $options = []) {
        $options2 = array_merge(StoiSubs::DefFmtOptions, $options);

        $_substanceName = $this->_subsType;
        /** @var string $_fdDecimalConfig 企业小数点设置中前端使用的字段名 */
        $_fdDecimalConfig = ArrayHelper::getValue(StoiSubs::DecimalFdNameConf, [$_substanceName, $fdName]);
        /** @var string $_decimalConfig 企业小数点位数设置 */
        $_decimalConfig = ArrayHelper::getValue($this->_companyDecimalConf, [$_substanceName, $_fdDecimalConfig]);

        /** @var string $fdValue 数据库中保存的值 */
        $fdValue = ArrayHelper::getValue($this->_subsValues, $fdName);

        //******************** 数据库里为空值|通过创建下一步反应创建的实验 //********************
        if ('' === $fdValue) {
            return $options2['empty_val'];
        }
        //******************** 数据库的值有异常,不能被解析为数字,显示空值 //********************
        if (!is_numeric($fdValue)) {
            return $options2['empty_val'];
        }

        //******************** 如果当前的格式化回调是获取计算用的值,直接返回数据库原始值 //********************
        $fmtCalcValueFn = [StoiSubs::class, 'getCalValue'];
        if ($fmtFn[0] === $fmtCalcValueFn[0] && $fmtFn[1] === $fmtCalcValueFn[1]) {
            return $fdValue;
        }

        //******************** 当前字段是用户输入的值,直接返回用户输入的值,不进行舍入 //********************
        $fdNameForUser = ArrayHelper::getValue(StoiSubs::FdName2UserFdName, [$_substanceName, $fdName]);
        if (in_array($fdNameForUser, $this->_userInputFdArr)) {
            return $fdValue;
        }

        //******************** 当前格式化值是为了获取显示在单元格中的值,进行小数舍入 //********************
        $dbFdValue = doubleval($fdValue);

        // 对小数按照指定的位数进行舍入
        /** @var string $fmtValue - 按照企业小数点设置格式化好的小数 */
        $fmtValue = call_user_func_array($fmtFn, ['value' => $dbFdValue, 'decimalConf' => $_decimalConfig]);
        // 如果格式化后是零值,单元格的值默认为0
        if (bc_comp_num_to_fixed($fmtValue, 0, $_decimalConfig) === 0) {
            return StoiSubs::getFmtValue(0, $_decimalConfig);
        }

        return $fmtValue;
    }

    /**
     * 根据默认配置获取物料表显示值
     * @param string|string[] $fdName
     * @return string
     */
    public function defaultGetFmtValueByFdName($fdName) {
        return $this->getValueByFmtFn($fdName, [StoiSubs::class, 'getFmtValue'], ['default_val' => 0]);
    }

    /**
     * Notes: 有前缀的字段(类似于'substrates_equivalent')，获取其值
     * User: YSJ
     * DateTime: 2024/1/31
     * @param $fdName
     * @param $prefix
     * @return mixed|string
     */
    public function getValueWithPrefix($fdName, $prefix) {
        $originalFdName = str_replace($prefix . '_', '', $fdName);
        $_fdFrontName = ArrayHelper::getValue(StoiSubs::DecimalFdNameConf, [$this->_subsType, $originalFdName]);

        if ($_fdFrontName) {
            $this->_subsValues[$originalFdName] = $this->_subsValues[$fdName];
            return $this->getValueByFmtFn($originalFdName, [StoiSubs::class, 'getFmtValue'], ['default_val' => 0]);
        }
        else {
            return $this->_subsValues[$fdName];
        }
    }

    /**
     * 根据设置修改数据库中查询出的物料表数据的小数点配置
     * @param array $substanceData
     * @return array
     * <AUTHOR>
     * @date   2024/4/29
     */
    public function fmtDBSubstanceData(&$substanceData) {
        $substanceFields = ['equivalent', 'nb', 'molweight', 'mass', 'volume', 'ratio'];

        foreach ($substanceFields as $subFdName) {
            $fdVal = ArrayHelper::getValue($substanceData, [$subFdName]);
            if (!empty($fdVal)) {
                $fmtFdVal = $this->getValueByFmtFn($subFdName, [StoiSubs::class, 'getFmtValue'], []);
                $substanceData[$subFdName] = $fmtFdVal;
            }
        }
        return $substanceData;
    }

    /**
     * 根据设置修改数据库中查询得到的物料表产物数据小数点配置
     * @param array $productRowData
     * @return mixed
     */
    public function fmtDBProductRowData(&$productRowData) {
        $productFields = ['nb', 'molweight', 'mass', 'theo', 'yield'];
        foreach ($productFields as $_prdFdName) {
            $fdValue = ArrayHelper::getValue($productRowData, [$_prdFdName]);
            if (!empty($fdValue)) {
                $fmtFdValue = $this->getValueByFmtFn($_prdFdName, [StoiSubs::class, 'getFmtValue'], []);
                $productRowData[$_prdFdName] = $fmtFdValue;
            }
        }
        return $productRowData;
    }
}