<?php
namespace frontend\controllers;

use common\components\Encryption;
use frontend\core\CommonServer;
use frontend\interfaces\CenterInterface;
use frontend\interfaces\PMInterface;
use frontend\models\BookModel;
use frontend\models\CompanyAuth;
use frontend\models\CompanyCheckUser;
use frontend\models\CompanyDecimal;
use frontend\models\CompanyDictUpload;
use frontend\models\CompanyDictValue;
use frontend\models\CompanyRoleModel;
use frontend\models\CompanySettingModel;
use frontend\models\ExpAuthChangeLog;
use frontend\models\ExpChangeNotebookGroupLog;
use frontend\models\ExperimentCoauthorModel;
use frontend\models\ExperimentModel;
use frontend\models\ExperimentRelayModel;
use frontend\models\ShareModel;
use frontend\models\ShareTopModel;
use frontend\models\StructdataHistory;
use frontend\models\TmpPowerModel;
use frontend\models\UserGroupRole;
use frontend\models\UseStatic;
use frontend\models\XSheetModel;
use frontend\services\ApprovalServer;
use frontend\services\BookServer;
use frontend\services\CompanyAuthServer;
use frontend\services\CompanyServer;
use frontend\services\ElnRoleServer;
use frontend\services\ExperimentServer;
use frontend\services\ExportServer;
use frontend\services\GroupSettingServer;
use frontend\services\InTableServer;
use frontend\services\IntegleTableServer;
use frontend\services\modules\BioModuleServer;
use frontend\services\modules\DefineTableModuleServer;
use frontend\services\modules\IndrawModuleServer;
use frontend\services\modules\RefModuleServer;
use frontend\services\ShareServer;
use frontend\services\ShareSettingServer;
use frontend\services\SignServer;
use frontend\services\TempleServer;
use PHPExcel;
use yii;
use yii\helpers\ArrayHelper;
use yii\helpers\Curl;

/**
 * eln鹰群的设置
 *
 * <AUTHOR> @copyright 2016-11-12
 */
class GroupSettingController extends MyController {

    /**
     * 进入设置界面，并且切换鹰群
     * 获取鹰群下的用户,以及是否是ELN用户
     * /?r=group-setting/group-user-list
     *
     * <AUTHOR> @copyright 2017-4-6
     * @param group_id 设置的鹰群id
     * @param options 输入的内容搜索
     * @param page 页码
     * @param limit 一页多少
     * @param only_apply 1表示只查看eln的用户
     * @param is_get_group 1表示获取鹰群列表 如果不需要再次获取鹰群，则只需要传递0即可
     */
    public function actionGroupUserList() {
        $currGroupId = \Yii::$app->getRequest()->post('group_id');
        $isGetGroup = \Yii::$app->getRequest()->post('is_get_group', 1);

        if (!empty($isGetGroup)) {
            //第一步 获取是鹰群群主或者是管理员的鹰群列表
            $groupList = CompanyAuthServer::getHasGroupAuthGroupList($this->userinfo->id,1);

            //鹰群列表按照鹰群名排序
            $sortArr = array();
            foreach($groupList as $key=>$v){
                $sortArr[$key]['group_name'] = $v['group_name'];
            }
            array_multisort($sortArr,SORT_ASC,$groupList);
            $tempKey = array_column($groupList, 'id');
            $groupList = array_combine($tempKey, $groupList);

            if (empty($groupList)) {
                return $this->fail(\Yii::t('setting', 'no_rights'), 'JSON', 'no_rights');
            }

            if (empty($currGroupId)) {
                $copy = $groupList;
                $currGroupId = array_shift($copy)['group_id'];
            }
        }

        if (is_array($currGroupId)) {
            $currGroupId = current($currGroupId);
        }

        if (empty($currGroupId)) {
            return $this->fail(\Yii::t('base', 'select_group'));
        }

        $page = \Yii::$app->getRequest()->post('page', 1);
        $limit = \Yii::$app->getRequest()->post('limit', \Yii::$app->params['default_page_size']);
        $options = \Yii::$app->getRequest()->post('input_value');
        $onlyApply = \Yii::$app->getRequest()->post('only_apply', 0);
        $onlyHistory = \Yii::$app->getRequest()->post('only_history', 0);
        $applyId = !empty($onlyApply) ? Yii::$app->params['eln_apply_id'] : null;

        $userRole = (new CenterInterface())->getUserRole(['user_id' => $this->userinfo->id, 'group_id' => $currGroupId]);
        $userList = (new CenterInterface())->userListAndElnUsers($currGroupId, $page, $limit, $options, $applyId, $onlyApply, $onlyHistory);
        foreach ($userList['data'] as $key => $user) {
            $role_ids = []; // 用户角色数组
            $role_ids[] = 5; // 普通成员
            if (!$onlyHistory) {
                if ($user['role_flag'] == 3) {
                    $role_ids[] = 2; // 群主
                }
                if ($user['role_flag'] == 2) {
                    $role_ids[] = 4; // 群管理员
                }
            }

            $userGroupRole = new UserGroupRole();
            $userGroupRoleInfo = $userGroupRole->find()->where(['user_id' => $user['user_id'], 'group_id' => $currGroupId])->asArray()->one();
            $customRole = explode(',', $userGroupRoleInfo['role_id']);
            $group_role_ids = array_merge($role_ids, $customRole);

            $roleInfoList = CompanyRoleModel::find()->where(['id' => $group_role_ids,'is_group_role' => 1,  'status' => 1])->asArray()->all();
            $userList['data'][$key]['role_list'] = $roleInfoList;
        }

        // 获取群内权限
        $groupAuth = CompanyAuthServer::getGroupAuthByUserIdAndGroupId($this->userinfo->id, $currGroupId);

        $settingRes = (new CompanyServer())->getCompanySetting( [
            'EMPOWER_BY_AUTH',
        ]);
        $general_setting = @getVar($settingRes['data'], []);


        if (empty($isGetGroup)) {
            $file = $this->renderAjax('/setting/eln_setting_temp.php', [
                'currGroupId' => $currGroupId,
                'groupList' => $groupList,
                'groupUserList' => $userList,
                'onlyApply' => $onlyApply,
                'onlyHistory' => $onlyHistory,
                'limit' => $limit,
                'userRole' => $userRole,
                'groupAuth' => $groupAuth,
                'general_setting'=>$general_setting,
            ]);
            return $this->success([
                'file' => $file,
                'groupList' => $groupList,
            ]);
        }

        $file = $this->renderAjax('/setting/eln_setting.php', [
            'currGroupId' => $currGroupId,
            'groupList' => $groupList,
            'groupUserList' => $userList,
            'onlyApply' => $onlyApply,
            'onlyHistory' => $onlyHistory,
            'limit' => $limit,
            'userRole' => $userRole,
            'groupAuth' => $groupAuth,
            'general_setting'=>$general_setting,
        ]);

        return $this->success([
            'contentHtml' => $file,
            'groupList' => $groupList,
        ]);
    }

    /**
     * 打印页面设置
     * ?r=group-setting/set-print-page
     */
    public function actionSetPrintPage(){

        $file = $this->renderAjax('/setting/print_page_temp.php');

        return $this->success([
            'file' => $file
            ]);
    }

    /**
     * 设置eln的管理员
     * ?r=group-setting/set-eln-admin
     *
     * <AUTHOR> @copyright 2016-11-12
     * @return \common\controllers\json
     */
    public function actionSetElnAdmin() {
        $postData = \Yii::$app->getRequest()->post();
        if (empty($postData['group_id']) || empty($postData['user_id'])) {
            return $this->fail(\Yii::t('setting', 'select_group_user'));
        }

        $userId = $postData['user_id'];

        $userDetail = (new CenterInterface())->getUserByUserId($userId);
        if (empty($userDetail)) {
            return $this->fail(\Yii::t('setting', 'input_user'));
        }

        $result = (new ElnRoleServer())->addRole($postData['group_id'], $userId, \Yii::$app->params['role_flag']['admin']);

        if (isset($result['status']) && (1 == $result['status'])) {
            return $this->success(\Yii::t('base', 'success'));
        }

        return $this->fail($result['info']);
    }

    /**
     * 获取分享设置
     * ?r=group-setting/get-share-set
     *
     * <AUTHOR> @copyright 2016-10-29
     * post
     * @param int $group_id 鹰群id
     * @param int|null $user_id 如果是针对鹰群设置的无需传递
     * @param boolean is_group 1表示是group 0表示是用户
     */
    public function actionGetShareSet() {
        $postData = \Yii::$app->getRequest()->post();
        if (empty($postData['group_id'])) {
            return $this->fail(\Yii::t('base', 'select_group'));
        }
        if (empty($postData['user_id'])) {
            return $this->fail(\Yii::t('base', 'select_user'));
        }

        $groupId = $postData['group_id'];
        $userId = !empty($postData['user_id']) ? $postData['user_id'] : NULL;

        $settingRes = (new CompanyServer())->getCompanySetting([
            'EMPOWER_BY_AUTH',
        ]);
        $setting = @getVar($settingRes['data'], []);
        if(isset($setting['EMPOWER_BY_AUTH']) && $setting['EMPOWER_BY_AUTH']['value']!=1) {
            return $this->fail(\Yii::t('base', 'no_power_by_auth'));
        }

        // 验证权限
        $hasAuth = CompanyAuthServer::checkGroupSettingAuth($groupId, $this->userinfo->id, 'set_share_config');
        if (!$hasAuth) {
            return $this->fail(\Yii::t('base', 'error_for_share_auth'));
        }

        $data = (new ShareSettingServer())->getShareSet($groupId, $userId);
        $data['shareAccess']['share_with_approval'] = !empty($data['shareAccess']['approver']) ? 1 : 0;

        // 所有群，计算 name
        $data['all_group_List'] = (new CenterInterface()) -> getGroupsListByCompanyId(1, '', 1, 0, 0);
        $data['group_List'] = CompanyAuthServer::getHasGroupAuthGroupList($this->userinfo->id);

        // END

        $groupInfo = (new CenterInterface())->getGroupByGroupId($groupId);
        $data['groupInfo'] = $groupInfo;

        $company_id = \Yii::$app->view->params['curr_company_id'];
        if(!$company_id){
            return $this->fail(\Yii::t('base', 'select_user'));
        }
        $userList = (new CenterInterface())->getUserListByCompanyId($company_id);
        $data['user_list'] = isset($userList['list']) ? $userList['list'] : [];
        $projectTree = (new PMInterface())->getUserProjectsTree($this->userinfo);
        $data['project_tree'] = $projectTree;
        $company_id = \Yii::$app->view->params['curr_company_id'];
        $userRes = (new CenterInterface())->getUserListByCompanyId($company_id);
        $userList = ArrayHelper::index($userRes['list'], 'id');
        $userNames = [];
        foreach ($userList as $user) {
            $userNames[$user['id']] = $user['real_name'] . '(' . $user['name'] . ')';
        }
        $groupRes = (new CenterInterface())->getGroupsListByCompanyId($company_id);
        $groupList = ArrayHelper::index($groupRes, 'id');
        $groupNames = [];
        foreach ($groupList as $group) {
            $groupNames[$group['id']] = $group['group_name'];
        }
        $data['group_names'] = $groupNames;
        $data['user_names'] = $userNames;

        $html = $this->renderAjax('/setting/share_temp.php', $data);

        return $this->success([
            'file' => $html
        ]);
    }

    /**
     * ?r=group-setting/get-share-set-of-group
     * 获取鹰群分享设置
     *
     * <AUTHOR> @copyright 2017-4-14
     *
     * get
     * @param group_id
     *
     * @return \common\controllers\json
     */
    public function actionGetShareSetOfGroup() {
        $postData = \Yii::$app->getRequest()->get();
        if (empty($postData['user_id'])) {
            return $this->fail(\Yii::t('base', 'select_user'));
        }
        if (empty($postData['group_id'])) {
            return $this->fail(\Yii::t('base', 'select_group'));
        }

        $postUsers = $postData['user_id'];
        $groupId = $postData['group_id'];

        // 验证权限
        $hasAuth = CompanyAuthServer::checkGroupSettingAuth($groupId, $this->userinfo->id, 'set_share_config');
        if (!$hasAuth) {
            return $this->fail(\Yii::t('base', 'error_for_share_auth'));
        }

        if(!is_array($postUsers)){
        	$postUsers = [$postUsers];
        }

        $userData = [];
        foreach ($postUsers as $user) {
            $userData[$user] = (new ShareSettingServer())->getShareSet($groupId, $user);
        }


        // 默认分享的共有用户
        $shareDefaultUserIds = [];
        $shareDefaultUserNames = [];
        foreach ($userData as $data){
            $userList = $data['shareSetting']['user_ids'];

            if(empty($userList)){ // 如果第一个用户的数据已经为空，则无需进行继续循环
                $shareDefaultUserIds = [];
                break;
            }

            $dataUserIds = $userList;
            if(empty($shareDefaultUserIds)){
                $shareDefaultUserIds = $dataUserIds;
            } else {
                $shareDefaultUserIds = array_intersect($shareDefaultUserIds, $dataUserIds);
                if(empty($shareDefaultUserIds)){
                    break; // 如果合并之后仍是空，则终止循环
                }
            }
        }

        if(!empty($shareDefaultUserIds)){
            $shareDefaultUsers = (new CenterInterface())->userDetailsByUserIds($shareDefaultUserIds);
            foreach($shareDefaultUsers as $user) {
                $shareDefaultUserNames[] = CommonServer::displayUserName($user);
            }
        }

        // BEGIN add by hk 2019/10/11 获取共有的默认分享鹰群
        $shareDefaultGroupIds = [];
        $shareDefaultGroupNames= [];
        foreach ($userData as $data){
            $groupList = $data['shareSetting']['group_ids'];
            if(empty($groupList)){ // 如果第一个用户的数据已经为空，则无需进行继续循环
                $shareDefaultGroupIds = [];
                break;
            }
            $dataGroupIds = $groupList;
            if(empty($shareDefaultGroupIds)){
                $shareDefaultGroupIds = $dataGroupIds;
            } else {
                $shareDefaultGroupIds = array_intersect($shareDefaultGroupIds, $dataGroupIds);
                if(empty($shareDefaultGroupIds)){
                    break; // 如果合并之后仍是空，则终止循环
                }
            }
        }
        if(!empty($shareDefaultGroupIds)){ //10/11
            $shareDefaultGroups =(new CenterInterface())->groupByGroupIds($shareDefaultGroupIds);
            foreach($shareDefaultGroups as $group) {
                $shareDefaultGroupNames[] = $group['group_name'];
            }
        }


        // 按项目分享合并
        $projectTaskSettngStr = [];
        foreach ($userData as $data) {
            $projectTaskSettng = @getVar($data['shareSetting']['project_task_setting'], []);
            if (empty($projectTaskSettng)) {
                break;
            }
            $projectTaskSettngStr[] = json_encode($projectTaskSettng, true);
        }
        $projectTaskSetting = count(array_unique($projectTaskSettngStr)) == 1 ? json_decode($projectTaskSettngStr[0], true) : [];
        //END

        $shareAccess['share_to_this_group'] = [];
        $shareAccess['share_to_my_group'] = [];
        $shareAccess['share_to_user'] = [];
        $shareAccess['share_to_other_group'] = [];
        $shareAccess['share_with_approval'] = [];
        $shareAccess['send_approval_email'] = [];
        $shareAccess['approver'] = [];
        $shareAccess['share_to_public'] = [];
        $shareAccess['shareDefaultGroup'] = [];
        $shareAccess['by_project_task'] = [];
        $coauthorBookSetting = []; // 允许选择群内人员进行整本合著设置

        foreach ($userData as $data) { // 默认勾选
            // 分享单选
            $shareAccess['share_to_this_group'][] = !empty($data['shareAccess']['share_to_this_group']) ? $data['shareAccess']['share_to_this_group'] : 0;
            $shareAccess['share_to_my_group'][] = !empty($data['shareAccess']['share_to_my_group']) ? $data['shareAccess']['share_to_my_group'] : 0;
            $shareAccess['share_to_user'][] = !empty($data['shareAccess']['share_to_user']) ? $data['shareAccess']['share_to_user'] : 0;
            $shareAccess['share_to_other_group'][] = !empty($data['shareAccess']['share_to_other_group']) ? $data['shareAccess']['share_to_other_group'] : 0;
            $shareAccess['share_with_approval'][] = !empty($data['shareAccess']['approver']) ? 1 : 0;
            $shareAccess['send_approval_email'][] = !empty($data['shareAccess']['send_approval_email']) ? 1 : 0;
            $shareAccess['approver'][] = !empty($data['shareAccess']['approver']) ? $data['shareAccess']['approver'] : '';
            $shareAccess['share_to_public'][] = !empty($data['shareAccess']['share_to_public']) ? $data['shareAccess']['share_to_public'] : 0;
            $shareAccess['shareDefaultGroup'][] = !empty($data['shareDefaultGroup']) ? 1 : 0;// 默认分享的鹰群  hkk2019/10/11 已经不用
            $shareAccess['by_project_task'][] = !empty($data['shareSetting']['by_project_task']) ? $data['shareSetting']['by_project_task'] : 0;
            $coauthorBookSetting[] = !empty($data['shareSetting']['coauthor_book']) ? $data['shareSetting']['coauthor_book'] : 0;
            $coauthorGroupBookSetting[] = !empty($data['shareSetting']['coauthor_group_book']) ? $data['shareSetting']['coauthor_group_book'] : 0;
        }

        $showData['share_to_this_group'] = $this->_showData($shareAccess['share_to_this_group'], 0);
        $showData['share_to_my_group'] = $this->_showData($shareAccess['share_to_my_group'], 0);
        $showData['share_to_user'] = $this->_showData($shareAccess['share_to_user'], 0);
        $showData['share_to_other_group'] = $this->_showData($shareAccess['share_to_other_group'], 0);
        $showData['share_with_approval'] = $this->_showData($shareAccess['share_with_approval'], 0);
        $showData['send_approval_email'] = $this->_showData($shareAccess['send_approval_email'], 0);
        $showData['share_to_public'] = $this->_showData($shareAccess['share_to_public'], 0);
        $showData['shareDefaultGroup'] = $this->_showData($shareAccess['shareDefaultGroup']); //  hkk2019/10/11 已经不用
        $showData['by_project_task'] = $this->_showData($shareAccess['by_project_task'], 0);
        $showData['coauthor_book'] = $this->_showData($coauthorBookSetting, 0);
        $showData['coauthor_group_book'] = $this->_showData($coauthorGroupBookSetting, 0);

        $shareAccess['approver'] = array_unique($shareAccess['approver']);
        if (count($shareAccess['approver']) === 1) {
            $showData['approver'] = $shareAccess['approver'][0];
        } else {
            $showData['approver'] = '[]';
        }

        //根据userID 获取 企业下所有用户的信息
        $company_id = \Yii::$app->view->params['curr_company_id'];
        if(!$company_id){
            return $this->fail(\Yii::t('base', 'select_user'));
        }
        $userListResult = (new CenterInterface())->getUserListByCompanyId($company_id);
        $userList = isset($userListResult['list']) ? $userListResult['list'] : [];

        $returnData = [
            'shareAccess' => $showData,
            'shareDefaultUserIds' => $shareDefaultUserIds,
            'shareDefaultUserNames' => $shareDefaultUserNames,
            'shareDefaultGroup' => $showData['shareDefaultGroup'],// delete by hkk 2019/10/11 不用，改为下面两行
            'user_list' => $userList,
        ];

        $groupInfo = (new CenterInterface())->getGroupByGroupId($groupId);
        $returnData['groupInfo'] = $groupInfo;

        //根据userID 获取设置人员所在鹰群信息 add by hkk 2019/10/10
        // modified by szq 2020/6/3 获取是管理员和群主的鹰群
        /*$group_List = (new CenterInterface())->elnGroupListByUserId($this->userinfo->id); //根据userID 获取设置人员所在鹰群信息 add by hkk 2019/10/10
        $returnData['group_List'] = [];
        foreach ($group_List as $item) {
            if ($item['master_id'] == $this->userinfo->id) {
                array_push($data['group_List'], $item);
            } else {
                foreach ($item['manager'] as $value) {
                    if ($value['user_id'] == $this->userinfo->id) {
                        array_push($data['group_List'], $item);
                    }
                }
            }
        }*/
        $returnData['group_List'] = (new CommonServer())->adminOfGroup($this->userinfo->id, $this->userinfo->current_company_id);
        $returnData['shareDefaultGroupIds'] = $shareDefaultGroupIds;
        $returnData['shareDefaultGroupNames'] = $shareDefaultGroupNames;
        $returnData['all_group_List'] = (new CenterInterface()) -> getGroupsListByCompanyId(1, '', 1, 0, 0);

        $company_id = \Yii::$app->view->params['curr_company_id'];
        if(!$company_id){
            return $this->fail(\Yii::t('base', 'select_user'));
        }
        $userList = (new CenterInterface())->getUserListByCompanyId($company_id);
        $data['user_list'] = isset($userList['list']) ? $userList['list'] : [];
        $projectTree = (new PMInterface())->getUserProjectsTree($this->userinfo);
        $data['project_tree'] = $projectTree;
        $company_id = \Yii::$app->view->params['curr_company_id'];
        $userRes = (new CenterInterface())->getUserListByCompanyId($company_id);
        $userList = ArrayHelper::index($userRes['list'], 'id');
        static $userNames = [];
        foreach ($userList as $user) {
            $userNames[$user['id']] = $user['real_name'] . '(' . $user['name'] . ')';
        }
        $groupRes = (new CenterInterface())->getGroupsListByCompanyId($company_id);
        $groupList = ArrayHelper::index($groupRes, 'id');
        $groupNames = [];
        foreach ($groupList as $group) {
            $groupNames[$group['id']] = $group['group_name'];
        }
        $returnData['group_names'] = $groupNames;
        $returnData['user_names'] = $userNames;

        $returnData['shareSetting'] = [
            'by_project_task' => $showData['by_project_task'],
            'group_ids' => $shareDefaultGroupIds,
            'user_ids' => $shareDefaultUserIds,
            'project_task_setting' => $projectTaskSetting,
            'coauthor_book' => $showData['coauthor_book'],
            'coauthor_group_book' => $showData['coauthor_group_book']
        ];
        $returnData['project_tree'] = $projectTree;
        $html = $this->renderAjax('/setting/share_temp.php', $returnData);

        return $this->success([
            'file' => $html,
            'showData' => $showData
        ]);
    }

    /**
     * 设置分享
     * ?r=group-setting/set-share-set
     *
     * <AUTHOR> @copyright 2017-4-11
     * post
     * @param int $group_id 鹰群id
     * @param int|null $user_id 如果是针对鹰群设置的无需传递
     * @param boolean is_group 1表示是group 0表示是用户
     * @param number def_share_group 允许分享给自己的鹰群
     * @param number share_to_my_group 允许分享到所加入的鹰群
     * @param number share_to_other_group 允许分享给其他鹰群
     * @param number share_to_user 允许分享给指定用户
     */
    public function actionSetShareSet() {
        $postData = \Yii::$app->getRequest()->post();

        if (empty($postData['group_id'])) {
            return $this->fail(\Yii::t('base', 'select_group'));
        }

        $isGroup = !empty($postData['is_group']) ? $postData['is_group'] : 0;
        if (empty($postData['user_id']) && !$isGroup) {
            return $this->fail(\Yii::t('base', 'select_user'));
        }

        $groupId = $postData['group_id'];
        $group = (new CenterInterface())->getGroupByGroupId($groupId);
        $data = [];

        $share_to_user = isset($postData['share_to_user']) ? $postData['share_to_user'] : 0;
        if(in_array($share_to_user, [0,1])){
            $data['share_to_user'] = $share_to_user;
        }

        $share_to_this_group = @getVar($postData['share_to_this_group'], 0);
        if(in_array($share_to_this_group, [0,1])){
            $data['share_to_this_group'] = $share_to_this_group;
        }

        $share_to_my_group = isset($postData['share_to_my_group']) ? $postData['share_to_my_group'] : 0;
        if(in_array($share_to_my_group, [0,1])){
            $data['share_to_my_group'] = $share_to_my_group;
        }

        $share_to_other_group = isset($postData['share_to_other_group']) ? $postData['share_to_other_group'] : 0;
        if(in_array($share_to_other_group, [0,1])){
            $data['share_to_other_group'] = $share_to_other_group;
        }

        $share_with_approval = isset($postData['share_with_approval']) ? $postData['share_with_approval'] : 0;
        if (in_array($share_with_approval, [0, 1])) {
            $data['share_with_approval'] = $share_with_approval;
            $data['approver'] = isset($postData['approver']) ? json_encode($postData['approver']) : '';
            $data['send_approval_email'] = @getVar($postData['send_approval_email']);
        }

        // add by hkk 2019/10/11 更改默认分享鹰群，不用$def_share_group
        /*$def_share_group = isset($postData['def_share_group']) ? $postData['def_share_group'] : 0;
        if(in_array($def_share_group, [0,1])){
            $data['def_share_group'] = $def_share_group;
        }*/
        $data['to_share_groups'] = !empty($postData['group_ids']) ? explode(',', $postData['group_ids']) : [];

        $data['to_share_users'] = !empty($postData['user_ids']) ? explode(',', $postData['user_ids']) : [];


        $data['project_task_setting'] = @getVar($postData['project_task_setting'], []);

        $data['by_project_task'] = $postData['by_project_task'];

        $coauthor_book = @getVar($postData['coauthor_book'], 0);
        if (in_array($coauthor_book, [0, 1])) {
            $data['coauthor_book'] = $coauthor_book;
        }

        $coauthor_group_book = @getVar($postData['coauthor_group_book'], 0);
        if (in_array($coauthor_group_book, [0, 1])) {
            $data['coauthor_group_book'] = $coauthor_group_book;
        }

        // 设置用户的
        $userId = $postData['user_id'];
        if(!is_array($userId)){
            $userId = [$userId];
        }

        $userInfo = (new CenterInterface())->userDetailsByUserIds($userId);
        $userInfo = array_column($userInfo, 'real_name', 'user_id');

        $aftRows = TRUE;
        $enableTxt = \Yii::t('base', 'enable');
        $disableTxt = \Yii::t('base', 'disable');
        foreach ($userId as $uid) {
            $setAftRows = (new ShareSettingServer())->setShareSet($groupId, $uid, $data);
            $doUserInfo = \Yii::t('base', 'user') . " [" . $userInfo[$uid] . "] ";
            // 日志记录内容
            $operate_desc = sprintf('[%1$s] %2$s ', \Yii::t('base', 'group'), $group['group_name']) . "[" . \Yii::t('base', 'new_group_setting') . "] [" . \Yii::t('views/applysetting', 'share setting') . "]-" .
                \Yii::t('base', 'user') . " " . $this->userinfo->real_name . " " .
                str_replace(['1', '0'], [$enableTxt, $disableTxt], $share_to_user) . " " . $doUserInfo . Yii::t('views/applysetting', 'allow share to member') . "; " .
                str_replace(['1', '0'], [$enableTxt, $disableTxt], $share_to_this_group) . " " . $doUserInfo . Yii::t('views/applysetting', 'allow share to this group') . "; " .
                str_replace(['1', '0'], [$enableTxt, $disableTxt], $share_to_my_group) . " " . $doUserInfo . Yii::t('views/applysetting', 'allow share to group') . "; " .
                str_replace(['1', '0'], [$enableTxt, $disableTxt], $share_to_other_group) . " " . $doUserInfo . Yii::t('views/applysetting', 'allow share to other group') . "; ".
                str_replace(['1', '0'], [$enableTxt, $disableTxt], $coauthor_book) . " " . $doUserInfo . Yii::t('views/applysetting', 'allow_select_member_coauthor_book') . ";";

            $role_arr = [];
            $system_master = 0;
            if (@getVar($this->userinfo->role_ids)) {
                $role_arr = explode(',', $this->userinfo->role_ids);
            }
            if (in_array('1', $role_arr)) {
                $system_master = 1;
            }

            $expAuthChangeLogAR = new ExpAuthChangeLog();
            $expAuthChangeLogAR->setAttributes([
                'user_id' => $this->userinfo->id,
                'operate_desc' => $operate_desc,
                'log_ip' => $_SERVER['REMOTE_ADDR'],
                'system_master' => $system_master,
            ]);
            $expAuthChangeLogAR->save();

            if (empty($setAftRows['status'])) {
                $aftRows |= FALSE;
            }
        }

        // 启动后台任务执行默认分享操作
        $userIdStr = implode(',', $userId);
        CommonServer::startBackendTask('yii deal-data/do-default-share ' . $groupId . ' "' . $userIdStr . '"');

        return $this->success(\Yii::t('base', 'success'));
    }

    /**
     * 获取打印设置弹出界面
     * ?r=group-setting/get-print-set
     *
     * <AUTHOR> @copyright 2016-10-29
     * post
     * @param int group_id
     * @param int user_id
     * @param boolean is_group 1表示是group 0表示是用户
     * @return Ambigous <object, \yii\di\mixed, mixed, multitype:>
     */
    public function actionGetPrintSet() {
        $postData = \Yii::$app->getRequest()->post();
        if (empty($postData['group_id'])) {
            return $this->fail(\Yii::t('base', 'select_group'));
        }
        if (empty($postData['user_id'])) {
            return $this->fail(\Yii::t('base', 'select_user'));
        }
        $settingRes = (new CompanyServer())->getCompanySetting([
            'EMPOWER_BY_AUTH',
        ]);
        $setting = @getVar($settingRes['data'], []);
        if(isset($setting['EMPOWER_BY_AUTH']) && $setting['EMPOWER_BY_AUTH']['value']!=1) {
            return $this->fail(\Yii::t('base', 'no_power_by_auth'));
        }

        // 验证权限
        $hasAuth = CompanyAuthServer::checkGroupSettingAuth($postData['group_id'], $this->userinfo->id, 'set_export_config');
        if (!$hasAuth) {
            return $this->fail(\Yii::t('base', 'no_power_by_auth'));
        }

        $printSetData = (new GroupSettingServer())->getPrintSet($postData);
        $printSetData = $printSetData['data'];

        $showData = [
            'pdf_single' => [],
            'pdf_book' => [],
            'word_single' => [],
            'word_book' => [],
            'more_page' => [],
            'print_history' => [],
            'print_comment' => [],
            'print_revision_trace' => [],
        ];

        foreach ($printSetData as $print) {
            $showData['pdf_single'][] = $print['pdf_single'];
            $showData['pdf_book'][] = $print['pdf_book'];
            $showData['word_single'][] = $print['word_single'];
            $showData['word_book'][] = $print['word_book'];
            $showData['more_page'][] = $print['more_page'];
            $showData['print_history'][] = $print['print_history'];
            $showData['print_comment'][] = $print['print_comment'];
            $showData['print_revision_trace'][] = $print['print_revision_trace'];
        }

        $showData['pdf_single'] = $this->_showData($showData['pdf_single'], 0);
        $showData['pdf_book'] = $this->_showData($showData['pdf_book'], 0);
        $showData['word_single'] = $this->_showData($showData['word_single'], 0);
        $showData['word_book'] = $this->_showData($showData['word_book'], 0);
        $showData['more_page'] = $this->_showData($showData['more_page'], 0);
        $showData['print_history'] = $this->_showData($showData['print_history'], 1);
        $showData['print_comment'] = $this->_showData($showData['print_comment'], 0);
        $showData['print_revision_trace'] = $this->_showData($showData['print_revision_trace'], 0);

        $file = $this->renderPartial('/setting/print_temp.php', [
            'printSetData' => $printSetData,
            'showData' => $showData,
            'is_group' => $postData['is_group']
        ]);

        return $this->success($file);
    }

    /**
     * 处理前端复选框如何显示
     *
     * <AUTHOR> @copyright 2017-9-22
     * @param array $printSetData
     * @param string $key
     * @param boolean $isDefalut 1表示默认勾选 0表示默认不勾选
     * @return number 1全选 2部分选择 0不选
     */
    private function _showData($data, $isDefalut=1){
        $status = 1;

        if (empty($data) && $isDefalut) {
            return $status;
        }

        $show = array_unique($data);
        $cnt = count($show);

        $show = array_filter($show);

        if (empty($show)) {
            $status = 0;
        }

        if (2 == $cnt) {
            $status = 2;
        }

        return $status;
    }

    /**
     * 设置打印设置
     * ?r=group-setting/set-print-set
     *
     * <AUTHOR> @copyright 2016-10-29
     * @param int group_id
     * @param int user_id
     * @param boolean is_group 1表示是group 0表示是用户
     * @param number pdf_book
     * @param number pdf_single
     * @param number word_book
     * @param number word_single
     * @param number more_page 0表示连续1表示分页
     * @return Ambigous <object, \yii\di\mixed, mixed, multitype:>
     */
    public function actionSetPrintSet() {
        $postData = \Yii::$app->getRequest()->post();

        if (empty($postData['group_id'])) {
            return $this->fail(\Yii::t('base', 'select_group'));
        }

        $data = [
            'group_id' => $postData['group_id'],
        ];
        $group = (new CenterInterface())->getGroupByGroupId($postData['group_id']);

        $pdfBook = isset($postData['pdf_book']) ? $postData['pdf_book'] : 0;
        if(in_array($pdfBook, [0,1])){
            $data['pdf_book'] = $pdfBook;
        }

        $pdfSingle = isset($postData['pdf_single']) ? $postData['pdf_single'] : 0;
        if(($pdfBook == 1) || in_array($pdfSingle, [0,1])){
            $data['pdf_single'] = $pdfBook ? 1 : $pdfSingle;
        }

        $wordBook = isset($postData['word_book']) ? $postData['word_book'] : 0;
        if(in_array($wordBook, [0,1])){
            $data['word_book'] = $wordBook;
        }

        $wordSin = isset($postData['word_single']) ? $postData['word_single'] : 0;
        if(($wordBook == 1) || in_array($wordSin, [0,1])){
            $data['word_single'] = $wordBook ? 1 : $wordSin;
        }

        $morePage = isset($postData['more_page']) ? $postData['more_page'] : 0;
        if(in_array($morePage, [0,1])){
            $data['more_page'] = $morePage;
        }

        $printHis = isset($postData['print_history']) ? $postData['print_history'] : 1;
        if(in_array($printHis, [0,1])){
            $data['print_history'] = $printHis;
        }

        $printComment = isset($postData['print_comment']) ? $postData['print_comment'] : 0;
        if(in_array($printComment, [0,1])){
            $data['print_comment'] = $printComment;
        }

        $printRevisionTrace = isset($postData['print_revision_trace']) ? $postData['print_revision_trace'] : 0;
        if(in_array($printRevisionTrace, [0,1])){
            $data['print_revision_trace'] = $printRevisionTrace;
        }

        if(!is_array($postData['user_id'])){
        	$postData['user_id'] = [$postData['user_id']];
        }

        $aftRows = TRUE;
        foreach ($postData['user_id'] as $userId){
            $data['user_id'] = $userId;
            $userInfo = (new CenterInterface())->userDetailsByUserIds($userId);

            $res = (new GroupSettingServer())->setPrintSet($data);


            $expAuthChangeLog = new ExpAuthChangeLog();
            $expAuthChangeLog->user_id = $this->userinfo->id;
            $expAuthChangeLog->operate_desc = sprintf('%1$s %2$s ', \Yii::t('base', 'group'), $group['group_name']) . '['.\Yii::t('base', 'new_group_setting').'] ['.\Yii::t('base', 'export_setting').']-'.\Yii::t('base', 'user').' '.(strlen(trim($this->userinfo->name))>0?$this->userinfo->name:$this->userinfo->real_name).'  '.str_replace(["1", '0'], [\Yii::t('base', 'enable'),\Yii::t('base', 'disable')], $pdfBook). " ".\Yii::t('base', 'user')." [".(!empty($userInfo[0]['nick_name'])?$userInfo[0]['nick_name']:$userInfo[0]['real_name'])."] PDF ".\Yii::t('base', 'whole_batch').";".str_replace(["1", '0'], [\Yii::t('base', 'enable'),\Yii::t('base', 'disable')], $pdfSingle)." ".\Yii::t('base', 'user')." [".(!empty($userInfo[0]['nick_name'])?$userInfo[0]['nick_name']:$userInfo[0]['real_name'])."] PDF ".\Yii::t('base', 'export').";".str_replace(["1", '0'], [\Yii::t('base', 'enable'),\Yii::t('base', 'disable')], $wordBook). " ".\Yii::t('base', 'user')." [".(!empty($userInfo[0]['nick_name'])?$userInfo[0]['nick_name']:$userInfo[0]['real_name'])."] WORD ".\Yii::t('base', 'whole_batch')." ;".str_replace(["1", '0'], [\Yii::t('base', 'enable'),\Yii::t('base', 'disable')], $wordSin). " ".\Yii::t('base', 'user')." [".(!empty($userInfo[0]['nick_name'])?$userInfo[0]['nick_name']:$userInfo[0]['real_name'])."] WORD ".\Yii::t('base', 'export')." ;".str_replace(["1", '0'], [\Yii::t('base', 'enable'),\Yii::t('base', 'disable')], $printHis). " ".\Yii::t('base', 'user')." [".(!empty($userInfo[0]['nick_name'])?$userInfo[0]['nick_name']:$userInfo[0]['real_name'])."] ".\Yii::t('views/applysetting', 'print_mark')." ;";
            $expAuthChangeLog->log_ip = $_SERVER[ 'REMOTE_ADDR' ] ;
            $role_arr=[];
            $system_master = 0;
            if(@getVar($this->userinfo->role_ids)){
                $role_arr = explode(',',$this->userinfo->role_ids);
            }
            if(in_array('1',$role_arr)){
                $system_master = 1;
            }
            $expAuthChangeLog->system_master = $system_master ;
            $expAuthChangeLog->save();

            if (empty($res['status'])) {
                $aftRows |= FALSE;
            }
        }




        return $this->success(\Yii::t('base', 'success'));
    }

    /**
     * 获取鹰群复核设置弹出框
     * ?r=group-setting/set-eln-admin
     *
     * <AUTHOR> @copyright 2017-4-11
     * 方式：get
     * @param int group_id
     * @return \common\controllers\json
     */
    public function actionGetGroupSignSet() {
        // 群id
        $groupId = \Yii::$app->getRequest()->post('group_id');
        if (empty($groupId)) {
            return $this->fail(\Yii::t('base', 'select_group'));
        }

        // 验证权限
        $hasAuth = CompanyAuthServer::checkGroupAuth($this->userinfo->current_company_id, $groupId, $this->userinfo->id, 'group_witness');
        if (!$hasAuth) {
            return $this->fail(\Yii::t('base', 'witnessSetNoAuthTip'));
        }

        $data = (new SignServer())->getSignGroup($groupId);

        $file = $this->renderPartial('/setting/group_sign_temp.php', [
            'sign_data' => $data
        ]);
        return $this->success([
            'file' => $file
        ]);
    }

    /**
     * 设置鹰群复核设置
     *
     * <AUTHOR>
     * @copyright 2017-04-17
     */
    public function actionSetGroupSignSet() {
        $postData = \Yii::$app->getRequest()->post();
        if (empty($postData['group_id'])) {
            $this->fail(\Yii::t('base', 'select_group'));
        }

        $emailCreator = empty($postData['email_creator']) ? 0 : intval($postData['email_creator']);
        $planDay = empty($postData['plan_day']) ? 0 : intval($postData['plan_day']);
        if ($emailCreator && !$planDay) {
            $this->fail(\Yii::t('setting', 'input_plan_day'));
        }

        $data = [
            //实验复核后不能被修改 1是 0否
            'un_allow_edit' => empty($postData['un_allow_edit']) ? 0 : intval($postData['un_allow_edit']),
            //实验创建后计划天数后未签名提醒本人 0不提醒
            'plan_day' => $planDay,
            //复核签名时必须选择复核人 1是0否
            'force_sign' => empty($postData['sign_force_select']) ? 0 : 1,
            'sign_force_select' => empty($postData['sign_force_select']) ? 0 : intval($postData['sign_force_select']),
            //邮件提醒复核人待复核的实验 1是0否
            'remind_review' => empty($postData['remind_review']) ? 0 : intval($postData['remind_review']),
            //邮件提醒被复核人实验的复核结果 1是0否
            'remind_review_result' => empty($postData['remind_review_result']) ? 0 : intval($postData['remind_review_result']),
            //是否发送邮件到创建人邮箱 1是0否
            'email_creator' => $emailCreator,
        ];

        $result = (new SignServer())->addSignGroup($postData['group_id'], $data);

        //添加日志
        $expAuthChangeLog = new ExpAuthChangeLog();
        $expAuthChangeLog->user_id = $this->userinfo->id;
        $expAuthChangeLog->operate_desc = '['.\Yii::t('base', 'group_setting').'] ['.\Yii::t('views/applysetting', 'group check setting').']-'.\Yii::t('base', 'user').' '.(strlen(trim($this->userinfo->name))>0?$this->userinfo->name:$this->userinfo->real_name).'  '.str_replace(["1", '0'], [\Yii::t('base', 'enable'),\Yii::t('base', 'disable')], $data['force_sign'])." [".\Yii::t('views/applysetting', 'select_do_checker_must')."] ;".str_replace(["1", '0'], [\Yii::t('base', 'enable'),\Yii::t('base', 'disable')], $data['remind_review'])." [".\Yii::t('views/applysetting', 'remind_witness_by_email')."] ;".str_replace(["1", '0'], [\Yii::t('base', 'enable'),\Yii::t('base', 'disable')], $data['remind_review_result'])." [".\Yii::t('views/applysetting', 'remind_the_result_of_witness_by_email')."] ;".str_replace(["1", '0'], [\Yii::t('base', 'enable'),\Yii::t('base', 'disable')], $data['email_creator'])." [".\Yii::t('views/applysetting', 'is_send_creator')."] ; [".\Yii::t('views/applysetting', 'notice_day',[$data['plan_day']])."] ;";
        $expAuthChangeLog->log_ip = $_SERVER[ 'REMOTE_ADDR' ] ;
        $role_arr=[];
        $system_master = 0;
        if(@getVar($this->userinfo->role_ids)){
            $role_arr = explode(',',$this->userinfo->role_ids);
        }
        if(in_array('1',$role_arr)){
            $system_master = 1;
        }
        $expAuthChangeLog->system_master = $system_master ;
        $expAuthChangeLog->save();

        if (isset($result['status']) && (1 == $result['status'])) {
            $this->success($result['data']);
        }

        $this->fail($result['info']);
    }

    /**
     * 设置成员复核设置
     *
     * <AUTHOR>
     * @copyright 2017-04-17
     * @type    post
     * @param   user_ids
     * @param   group_id
     * @param   emails
     * @param   sign_user_ids
     * @param   plan_day
     */
    public function actionSetMemberSignSet() {
        //用户id
        $userIds = \Yii::$app->getRequest()->post('user_ids');
        //群id
        $groupId = \Yii::$app->getRequest()->post('group_id');
        //接收消息的email 数组
        $postEmails = \Yii::$app->getRequest()->post('emails');
        //指定复核人的用户id 数组
        $signUserIds = \Yii::$app->getRequest()->post('sign_user_ids');
        //天数
        $planDay = \Yii::$app->getRequest()->post('plan_day');

        if (!$userIds || !$groupId) {
            $this->fail(\Yii::t('setting', 'select_group_user'));
        }

        if (!$planDay && $postEmails) {
            $this->fail(\Yii::t('setting', 'input_plan_day'));
        }

        if(!empty($planDay) && empty($postEmails)){
            $this->fail(\Yii::t('setting', 'input_email'));
        }

        if(!empty($postEmails)){
            $postEmails = str_replace('；', ';', $postEmails);
            $postEmails = str_replace(' ', '', $postEmails);
            $postEmails = str_replace(array("\r\n", "\r", "\n"), '', $postEmails);

            $postEmails = explode(';', $postEmails);

            $postEmails = array_filter($postEmails);
            $postEmails = array_unique($postEmails);

        }


        //添加日志
        $user_info = (new CenterInterface())->userDetailsByUserIds($signUserIds);
        $user_names = array_column($user_info, 'nick_name');

        $expAuthChangeLog = new ExpAuthChangeLog();
        $expAuthChangeLog->user_id = $this->userinfo->id;
        $expAuthChangeLog->operate_desc = '['.\Yii::t('base', 'group_setting').'] ['.\Yii::t('views/applysetting', 'check setting').']-'.\Yii::t('base', 'user').' '.(strlen(trim($this->userinfo->name))>0?$this->userinfo->name:$this->userinfo->real_name).'  '.\Yii::t('base', 'send sign message',[$planDay]).' '.\Yii::$app->getRequest()->post('emails').', '.\Yii::t('base', 'set singer by').' '.join(',',$user_names).']';;
        $expAuthChangeLog->log_ip = $_SERVER[ 'REMOTE_ADDR' ] ;
        $role_arr=[];
        $system_master = 0;
        if(@getVar($this->userinfo->role_ids)){
            $role_arr = explode(',',$this->userinfo->role_ids);
        }
        if(in_array('1',$role_arr)){
            $system_master = 1;
        }
        $expAuthChangeLog->system_master = $system_master ;
        $expAuthChangeLog->save();



        if(!empty($postEmails)){

            $users = (new CenterInterface())->userDetailsByEmails($postEmails);
            $emails = array_column($users, 'email');

            if (empty($emails)) {
                $this->fail(\Yii::t('setting', 'input_correct_email'));
            }

            $data = [
                'plan_day' => empty($planDay) ? '' : $planDay,
                'emails' => empty($emails) ? '' : implode(';', $emails),
                'sign_user_ids' => $signUserIds ? implode(',', $signUserIds) : '',
            ];

            foreach($userIds as $userId){

                $result = (new GroupSettingServer())->AddGroupMemberSign($userId, $groupId, $data);
            }

            if ($result['status']) {
                $this->success(\Yii::t('base', 'success'));
            } else {
                $this->fail($result['info']);
            }
        }else{

            foreach($userIds as $userId){
                $result = (new GroupSettingServer())->getGroupMemberSign($userId, $groupId);
                if(isset($result['emails'])) {
                    //刪除
                    (new GroupSettingServer())->delGroupMemberSign($userId, $groupId);
                }
            }

            $data = [
                'plan_day' => empty($planDay) ? '' : $planDay,
                'emails' => empty($emails) ? '' : implode(';', $emails),
                'sign_user_ids' => $signUserIds ? implode(',', $signUserIds) : '',
            ];

            foreach($userIds as $userId){
                $result = (new GroupSettingServer())->AddGroupMemberSign($userId, $groupId, $data);
            }

            if ($result['status']) {
                $this->success(\Yii::t('base', 'success'));
            } else {
                $this->fail($result['info']);
            }
        }

    }

    /**
     * 添加鹰群成员
     *
     * <AUTHOR>
     * @copyright 2017-04-11
     */
    public function actionAddGroupMember() {
        // 鹰群id
        $groupId = \Yii::$app->request->post('group_id');
        // 被添加者账号 数组格式
        $account = \Yii::$app->request->post('account');

        $data = [
            'group_id' => $groupId,
            'account' => $account,
            'user_id' => $this->userinfo->id
        ];
        $result = (new CenterInterface())->addGroupMember($data);

        if (isset($result['status']) && $result['status']) {

            $this->success([]);
        } else {
            $this->fail(isset($result['message']) ? $result['message'] : \Yii::t('base', 'failed'));
        }
    }

    /**
     * 分配应用
     *
     * <AUTHOR>
     * @copyright 2017-04-12
     */
    public function actionDispatchApp() {
        // 被操作者t_group_member表的id集合，数组格式
        $memberIds = \Yii::$app->request->post('member_ids');
        if (empty($memberIds)) {
            $this->fail(\Yii::t('setting', 'select_do_user'));
        }
        // 管理员之间不能取消操作
        $memberIds = $this->_filterAdminMembers($memberIds);
        $data = [
            'dispatcher_id' => $this->userinfo->id,
            'member_ids' => $memberIds,
            'app_id' => \Yii::$app->params['eln_apply_id'] ,
            'jump_error' => 1,//跳过已分配的错误提示
        ];
        $result = (new CenterInterface())->dispatchApp($data);

        if (isset($result['status']) && $result['status']) {
            $this->success([]);
        } else {
            $this->fail(isset($result['message']) ? $result['message'] : \Yii::t('base', 'failed'));
        }
    }

    /**
     * 取消分配应用
     *
     * <AUTHOR>
     * @copyright 2017-04-12
     */
    public function actionUndispatchApp() {
        // 被操作者t_group_member表id集合，数组格式
        $memberIds = \Yii::$app->request->post('member_ids');

        // 管理员之间不能取消操作
        $memberIds = $this->_filterAdminMembers($memberIds);

        $data = [
            'undispatcher_id' => $this->userinfo->id,
            'member_ids' => $memberIds,
            'app_id' => \Yii::$app->params['eln_apply_id']
        ];
        $result = (new CenterInterface())->undispatchApp($data);

        if (isset($result['status']) && $result['status']) {
            $this->success([]);
        } else {
            $this->fail(isset($result['message']) ? $result['message'] : \Yii::t('base', 'failed'));
        }
    }

    /**
     * 根据t_group_member的id 过滤eln的管理员
     * @param $memberIds
     * @return array
     */
    protected function _filterAdminMembers($memberIds)
    {
        $error = '';
        $filterIds = [];
        $memberInfo = (new CenterInterface())->getGroupMemberInfo($memberIds);
        if (empty($memberInfo)) {
            $this->fail(\Yii::t('setting', 'select_do_user'));
        }
        $groupId = $memberInfo[0]['group_id'];

        // 检查当前用户权限，是否是群主或管理员
        $role = (new CenterInterface())->getUserRole([
            'user_id' => $this->userinfo->id,
            'group_id' => $groupId,
        ]);
        // 群主则直接跳过
        if ($role != \Yii::$app->params['role_flag']['master'] ) {
            foreach($memberInfo as $member) {

                $roleMember = (new CenterInterface())->getUserRole([
                    'user_id' => $member['user_id'],
                    'group_id' => $groupId,
                ]);

                if($member['user_id'] == $this->userinfo->id){
                    $this->fail(\Yii::t('setting', 'select_self'));
                }

                if($roleMember == \Yii::$app->params['role_flag']['master']){
                    $error = \Yii::t('setting', 'no_rights');
                }

                if($role == \Yii::$app->params['role_flag']['master'] ){
                    $filterIds[] = $member['id'];
                }

                if($roleMember == \Yii::$app->params['role_flag']['admin'] && $this->userinfo->id != $member['user_id']){
                    $error = \Yii::t('setting', 'no_rights');
                }else{
                    $filterIds[] = $member['id'];
                }
            }
        }

        if ($error) {
            if (count($memberIds) > 1) {
                // 批量操作则只过滤不提示错误
                $memberIds = $filterIds;
            } else {
                // 单个操作提示错误
                $this->fail($error);
            }
        }

        return $memberIds;
    }

    /**
     * 开通/关闭 管理员权限
     *
     * <AUTHOR>
     * @copyright 2017-04-13
     */
    public function actionSetAdmin() {
        // 群id
        $groupId = \Yii::$app->getRequest()->post('group_id');
        // 用户id
        $userId = \Yii::$app->getRequest()->post('user_id');
        $userInfo = (new CenterInterface())->userDetailsByUserIds($userId);

        // 操作类型 1开通，0关闭
        $type = \Yii::$app->getRequest()->post('type');
        $memberIds = \Yii::$app->request->post('member_ids');

        if (empty($groupId) || empty($userId)) {
            $this->fail(\Yii::t('setting', 'select_group_user'));
        }

        if($userId == $this->userinfo->id){
            $this->fail(\Yii::t('setting', 'select_self'));
        }
        // 检查当前用户权限，是否是群主或管理员
        $role = (new CenterInterface())->getUserRole([
            'user_id' => $this->userinfo->id,
            'group_id' => $groupId
        ]);

        if(\Yii::$app->params['role_flag']['master'] != $role){
            return $this->fail(\Yii::t('setting', 'no_rights'));
        }

        // 检查被操作用户权限，是否是群主或管理员
        $role_operated = (new CenterInterface())->getUserRole([
            'user_id' => $userId,
            'group_id' => $groupId
        ]);

        // 群主eln管理员不能操作
        if(\Yii::$app->params['role_flag']['master'] == $role_operated){
            return $this->fail(\Yii::t('setting', 'no_rights'));
        }

        // 2设置为管理员，0 关闭
        $roleValue = $type ? \Yii::$app->params['role_flag']['admin'] : 0;
        $result = (new ElnRoleServer())->addRole($groupId, $userId, $roleValue);

        $expAuthChangeLog = new ExpAuthChangeLog();
        $expAuthChangeLog->user_id = $this->userinfo->id;
        $expAuthChangeLog->operate_desc = '['.\Yii::t('base', 'group_setting').']-'.\Yii::t('base', 'user').' '.(strlen(trim($this->userinfo->name))>0?$this->userinfo->name:$this->userinfo->real_name).'  '.str_replace(["2", '0'], [\Yii::t('base', 'enable'),\Yii::t('base', 'disable')], $roleValue)." ".\Yii::t('base', 'user')." [".(!empty($userInfo[0]['nick_name'])?$userInfo[0]['nick_name']:$userInfo[0]['real_name'])."] ".\Yii::t('base', 'master')." ";
        $expAuthChangeLog->log_ip = $_SERVER[ 'REMOTE_ADDR' ] ;
        $role_arr=[];
        $system_master = 0;
        if(@getVar($this->userinfo->role_ids)){
            $role_arr = explode(',',$this->userinfo->role_ids);
        }
        if(in_array('1',$role_arr)){
            $system_master = 1;
        }
        $expAuthChangeLog->system_master = $system_master ;
        $expAuthChangeLog->save();

        if (isset($result['status']) && (1 == $result['status'])) {
            $this->success(\Yii::t('base', 'success'));
        } else {
            $this->fail($result['info']);
        }

        /* end */

    }


    /**
     * 分配应用
     *
     * <AUTHOR> huili
     * @copyright 2017-05-05
     */
    public function actionOpenApp($memberIds){

        if (empty($memberIds)) {
            $this->fail(\Yii::t('setting', 'select_do_user'));
        }
        // 管理员之间不能取消操作
        $memberIds = $this->_filterAdminMembers($memberIds);
        $data = [
            'dispatcher_id' => $this->userinfo->id,
            'member_ids' => $memberIds,
            'app_id' => \Yii::$app->params['eln_apply_id'] ,
            'jump_error' => 1, // 跳过已分配的错误提示
        ];
        $result = (new CenterInterface())->dispatchApp($data);

        if (isset($result['status']) && $result['status']) {
            return $result;
        } else {
            $this->fail(isset($result['message']) ? $result['message'] : \Yii::t('base', 'failed'));
        }
    }

    /*
     * 批量关闭应用（级联关闭管理员）
     *
     * <AUTHOR>
     * @copyright 2017-10-24
     * */
    public function actionBatchCloseApp(){
        // 被操作者t_group_member表id集合，数组格式
        $memberIds = \Yii::$app->request->post('member_ids');
        $groupId = \Yii::$app->request->post('group_id', 114883);

        // 管理员之间不能取消操作
        $memberIds = $this->_filterAdminMembers($memberIds);
        $memberInfo = (new CenterInterface())->getGroupMemberInfo($memberIds);
        $data = [
            'undispatcher_id' => $this->userinfo->id,
            'member_ids' => $memberIds,
            'app_id' => \Yii::$app->params['eln_apply_id']
        ];
        $result = (new CenterInterface())->undispatchApp($data);

        if (isset($result['status']) && $result['status']) {
            foreach($memberInfo as $member){

                (new ElnRoleServer())->addRole($groupId, $member['user_id'], 0);
            }
            $this->success([]);
        } else {
            $this->fail(isset($result['message']) ? $result['message'] : \Yii::t('base', 'failed'));
        }

    }

    /**
     * 获取模板权限弹窗
     *
     * @date: 2018年6月28日 上午10:01:12
     * <AUTHOR>
     * @return \common\controllers\json
     */
    public function actionGetUserModuleSet() {
        $userIds = \Yii::$app->request->post('user_ids', []);
        $groupId = \Yii::$app->request->post('group_id', 0);

        // 验证权限
        $hasAuth = CompanyAuthServer::checkGroupAuth($this->userinfo->current_company_id, $groupId, $this->userinfo->id, 'member_template');
        if (!$hasAuth) {
            return $this->fail(\Yii::t('base', 'no_auth'));
        }

        $count = TmpPowerModel::find()->where(['user_id'=>$userIds, 'group_id'=>$groupId, 'status'=>1])->count();

        $tmpPower = 1;
        if ( count($userIds) == $count ){
            $tmpPower = 0;
        }

        $file = $this->renderPartial('/setting/module_setting_temp.php', ['tmpPower'=>$tmpPower]);

        return $this->success($file);
    }

    /**
     * 设置模板权限
     *
     * @date: 2018年6月26日 下午4:47:21
     * <AUTHOR>
     * @return \common\controllers\json
     */
    public function actionSetTmpPower(){
        $groupId = \Yii::$app->request->post('group_id');
        $userIds = \Yii::$app->request->post('user_ids', []);
        $set = \Yii::$app->request->post('set', null);
        $userId = $this->userinfo->id;


        $result = (new ElnRoleServer())->setTmpPower($userId,$groupId,$userIds,$set);

        //添加日志
        $user_info = (new CenterInterface())->userDetailsByUserIds($userIds);
        $user_names = array_column($user_info, 'nick_name');
        $expAuthChangeLog = new ExpAuthChangeLog();
        $expAuthChangeLog->user_id = $this->userinfo->id;
        $expAuthChangeLog->operate_desc = '['.\Yii::t('base', 'group_setting').'] ['.\Yii::t('views/applysetting', 'template setting').']-'.\Yii::t('base', 'user').' '.(strlen(trim($this->userinfo->name))>0?$this->userinfo->name:$this->userinfo->real_name).'  '.str_replace(["1", '0'], [\Yii::t('base', 'enable'),\Yii::t('base', 'disable')], $set)."用户 [".join(',',$user_names)."] ".\Yii::t('base', 'creat_edit_temp_power').";";
        $expAuthChangeLog->log_ip = $_SERVER[ 'REMOTE_ADDR' ] ;
        $role_arr=[];
        $system_master = 0;
        if(@getVar($this->userinfo->role_ids)){
            $role_arr = explode(',',$this->userinfo->role_ids);
        }
        if(in_array('1',$role_arr)){
            $system_master = 1;
        }
        $expAuthChangeLog->system_master = $system_master ;
        $expAuthChangeLog->save();

        if ( isset($result['status']) && $result['status'] ){
            return $this->success([]);
        }
        return $this->fail(isset($result['message'])?$result['message']:$result['info']);

    }

    /**
     * 企业词库
     * ?r=group-setting/set-print-page
     */
    public function actionCompanyDict(){
        // 权限控制 jiangdm 2022/7/13
        if (!\Yii::$app->view->params['is_system_admin'] && \Yii::$app->view->params['company_dict_read'] != 1 && \Yii::$app->view->params['company_dict_write'] != 1) {
            return $this->fail(\Yii::t('base', 'no_auth'));
        }


        $dictUpload = new CompanyDictUpload;
        $uploadInfo = $dictUpload->find()->where(['company_id'=>$this->userinfo->current_company_id])->orderBy('id desc')->one();

        $company_dict_write = \Yii::$app->view->params['company_dict_write'] == 1;
        /*验证完成*/

        //获取初始化的下载地址
        if($uploadInfo){
            $filename ="?r=download/file&type=2&path=".$uploadInfo['dep_path']."&name=".$uploadInfo['save_name'];
        }
        else {
            $filename = '/template/company_dict_template.xlsx';
        }



        $file = $this->renderAjax('/setting/company_dict.php',['filename' => $filename,'company_dict_write'=>$company_dict_write]);

        return $this->success(['contentHtml' => $file]);
    }


    /**
     * 企业词库提交文件
     * <AUTHOR>
     * @date   2024/3/26 11:05:49
     * @return \common\controllers\json
     * @throws \PHPExcel_Reader_Exception
     * @throws yii\db\Exception
     */
    public function actionSubmitCompanyDict() {
        $data = \Yii::$app->request->post();

        //权限判断
        $authData = (new CompanyAuthServer())->getCompanyAuthByUserId($this->userinfo->id, 1);
        if (!@getVar($authData['company_feature']['edit_company_dict'])) {
            return $this->fail(\Yii::t('base', 'no_auth'));
        }
        //print_r($data['dep_path']);exit;
        if (isset($data['dep_path']) && $data['dep_path'] != '') {
            //保存文件名
            $dictUpload = new CompanyDictUpload;
            $dictUpload->company_id = $this->userinfo->current_company_id;
            $dictUpload->dep_path = $data['dep_path'];
            $dictUpload->save_name = $data['save_name'];
            $dictUpload->file_name = $data['file_name'];
            $dictUpload->user_id = $this->userinfo->id;
            $dictUpload->save();
        } else {
            return $this->fail(\Yii::t('base', 'enterprise_dictionary_06'));
        }

        //将旧值设置无效
        $dictValue = new CompanyDictValue();
        $dictValue->updateAll(['status' => 0], 'company_id=:company_id', [':company_id' => $this->userinfo->current_company_id]);


        $filename = \Yii::getAlias('@filepath') . DS . $data['dep_path'] . DS . $data['save_name'];

        $arr = pathinfo($filename);
        $ext = $arr['extension'];
        $insertArray = Array();
        //读取excel文件
        if (in_array($ext, array('xls', 'xlsx'))) {
            // PHPExcel modifies the precision
            // https://stackoverflow.com/questions/44023625/phpexcel-reading-incorrect-decimal-values
            $iniPrecision = ini_get('precision');// 备份可能被phpexcel修改的precision

            //需要手工加载这个类
            require_once dirname(dirname(__DIR__)) . DIRECTORY_SEPARATOR . 'vendor' . DIRECTORY_SEPARATOR . 'Excel' . DIRECTORY_SEPARATOR . 'PHPExcel.php';

            /*$excelFile = Yii::getAlias('@web/www/uploads'.$filename.'');*///获取文件名
            $fileType = \PHPExcel_IOFactory::identify($filename); //文件名自动判断文件类型
            $excelReader = \PHPExcel_IOFactory::createReader($fileType);

            $phpexcel = $excelReader->load($filename)->getSheet(0);//载入文件并获取第一个sheet

            ini_set('precision', $iniPrecision);// 恢复可能被phpexcel修改的precision

            $total_line = $phpexcel->getHighestRow();//总行数
            $total_column = $phpexcel->getHighestColumn();//总列数 $highestColumn = $sheet->getHighestColumn();
            ++$total_column;

            if ($total_line > 1) {
                for ($row = 2; $row <= $total_line; $row++) {
                    $data = array();
                    for ($column = 'A'; $column != $total_column; $column++) {
                        //查找分类ID

                        $data[] = trim($phpexcel->getCell($column . $row)->getValue());
                    }

                    //如果分类名称有效则 继续往下走
                        $temp_data = $data;
                        $parent_id = $temp_data[0];
                        if(in_array($parent_id,\Yii::$app->params['company_dict_category'])){
                            unset($temp_data[0]);
                            unset($temp_data[1]);
                            unset($temp_data[2]);
                            foreach ($temp_data as $value) {
                                if (strlen(trim($value)) > 0) {
                                    $insertValue = Array();
                                    $insertValue['parent_id'] = $parent_id;
                                    $insertValue['company_id'] = $this->userinfo->current_company_id;
                                    //处理下标
                                    if (in_array($parent_id, [13, 19, 24, 25, 26, 27, 35, 46])) {
                                        $value = $this->transNumber($value, 'sub');
                                        //echo $value;
                                    }
                                    //处理上标
                                    if (in_array($parent_id, [37])) {
                                        $value = $this->transNumber($value, 'sup');
                                    }
                                    //优先级只允许数字，如果填非数字则不添加
                                    if (in_array($parent_id, [50])) {
                                        if(!is_numeric($value)) {
                                            continue;
                                        }
                                    }

                                    $insertValue['dict_value'] = $value;
                                    $insertValue['status'] = 1;
                                    array_push($insertArray,$insertValue);
                                }
                            }
                        }



                }
            }
        }
        //改为批量导入，提升导入速度
        $insertKeys = ['parent_id', 'company_id', 'dict_value', 'status'];
        $res = \Yii::$app->db->createCommand()->batchInsert(CompanyDictValue::tableName(), $insertKeys, $insertArray)->execute();
        $dictValue->deleteAll(['status' => 0, 'company_id' => $this->userinfo->current_company_id]);
        //返回结果
        /*添加日志*/
        $expAuthChangeLog = new ExpAuthChangeLog();
        $expAuthChangeLog->user_id = $this->userinfo->id;
        $expAuthChangeLog->operate_desc = '[' . \Yii::t('base', 'company_dict') . ']-' . \Yii::t('base', 'user') . ' ' . (strlen(trim($this->userinfo->name)) > 0 ? $this->userinfo->name : $this->userinfo->real_name) . ' ' . \Yii::t('base', 'upload_dict_company').' ';
        $expAuthChangeLog->log_ip = $_SERVER['REMOTE_ADDR'];
        $role_arr=[];
        $system_master = 0;
        if(@getVar($this->userinfo->role_ids)){
            $role_arr = explode(',',$this->userinfo->role_ids);
        }
        if(in_array('1',$role_arr)){
            $system_master = 1;
        }
        $expAuthChangeLog->system_master = $system_master ;
        $expAuthChangeLog->save();
        /*添加日志完成*/


        return $this->success([]);
    }

    public function actionGetCompanyDictByParentId(){

        $data = \Yii::$app->request->post();


        //将旧值设置无效
        $dictValue = new CompanyDictValue();

        //changed by xieyuxiang 2023.6.9 bug33622 企业词库如果上传了文件，会重复
        $result = $dictValue->find()->where(['parent_id' => $data['parent_id'], 'company_id' => 1])->asArray()->all();
        if (empty($result)) {
            $result = $dictValue->find()->where(['parent_id' => $data['parent_id'], 'company_id' => 0])->asArray()->all();
        }
        //返回结果
        return $this->success($result);

    }

    public function actionDownloadUserTemplate(){

        //避免中文文件名出现检测不到文件名的情况，进行转码utf-8->gbk
        $fname = '企业词库模板（最新）';
        $filename=iconv('utf-8', 'gb2312', $fname);
        $dictUpload = new CompanyDictUpload;
        $uploadInfo = $dictUpload->find(['company_id'=>$this->userinfo->current_company_id])->orderBy('id desc')->one();

        //获取初始化的下载地址
        if($uploadInfo){

            $arr = pathinfo($uploadInfo['save_name']);
            $ext = $arr['extension'];
            $path = \Yii::getAlias('@filepath') . DS . $uploadInfo['dep_path'] . DS . $uploadInfo['save_name'];
        }
        else {
            $path=dirname(\Yii::$app->BasePath)."/frontend/web/template/company_dict_template.xlsx";
            $ext = 'xlsx';
        }

        $filename = $filename.".".$ext;

        $encryption = new Encryption();
        if ($encryption->isHuatuEncrypt()) {
            $toFile = \Yii::getAlias('@svgpath') . DS . 'company_dict_template_' . time() . $ext;
            $path = (new Encryption())->encryptFileApi($toFile, $path); // 调用加密接口加密
        } else {
            $path = $encryption->encryptFileApi($path); // 调用加密接口加密
        }

        $expAuthChangeLog = new ExpAuthChangeLog();
        $expAuthChangeLog->user_id = $this->userinfo->id;
        $expAuthChangeLog->operate_desc = '['.\Yii::t('base', 'company_dict').']-'.\Yii::t('base', 'user').' '.strlen(trim($this->userinfo->name))>0?$this->userinfo->name:$this->userinfo->real_name.' '.\Yii::t('base', 'download_new_dict');
        $expAuthChangeLog->log_ip = $_SERVER[ 'REMOTE_ADDR' ] ;
        $role_arr=[];
        $system_master = 0;
        if(@getVar($this->userinfo->role_ids)){
            $role_arr = explode(',',$this->userinfo->role_ids);
        }
        if(in_array('1',$role_arr)){
            $system_master = 1;
        }
        $expAuthChangeLog->system_master = $system_master ;
        $expAuthChangeLog->save();
        //echo $path;exit;

        if(!file_exists($path)){//检测文件是否存在
            echo \Yii::t('base', 'file_empty');
            die();
        }

        $fp=fopen($path,'r');//只读方式打开
        $filesize=filesize($path);//文件大小

        //返回的文件(流形式)
        header("Content-type: application/octet-stream");
        //按照字节大小返回
        header("Accept-Ranges: bytes");
        //返回文件大小
        header("Accept-Length: $filesize");
        //这里客户端的弹出对话框，对应的文件名
        header("Content-Disposition: attachment; filename=".$filename);
        //================重点====================
        ob_clean();
        flush();
        //=================重点===================
        //设置分流
        $buffer=1024;
        //来个文件字节计数器
        $count=0;
        while(!feof($fp)&&($filesize-$count>0)){
            $data=fread($fp,$buffer);
            $count+=$data;//计数
            echo $data;//传数据给浏览器端
        }

        fclose($fp);

    }

    public function actionDownloadSystemTemplate(){

        $lang = \Yii::$app->language;
        //en-US  zh-CN

        $bodyLang = 'cn';
        if(Yii::$app->params['language_list']['en'] == $lang){
            $bodyLang = 'en';
        }



        //避免中文文件名出现检测不到文件名的情况，进行转码utf-8->gbk
        if($bodyLang == 'en')
        {
            $fname = 'Enterprise_Dictionary.xlsx';
            $filename=iconv('utf-8', 'gb2312', $fname);
            $path=dirname(\Yii::$app->BasePath)."/frontend/web/template/company_dict_template_en.xlsx";
        }
        else
        {
            $fname = '企业词库模板（系统初始）.xlsx';
            $filename=iconv('utf-8', 'gb2312', $fname);
            $path=dirname(\Yii::$app->BasePath)."/frontend/web/template/company_dict_template.xlsx";

        }


        $expAuthChangeLog = new ExpAuthChangeLog();
        $expAuthChangeLog->user_id = $this->userinfo->id;
        $expAuthChangeLog->operate_desc = '['.\Yii::t('base', 'company_dict').']-'.\Yii::t('base', 'user').' '.strlen(trim($this->userinfo->name))>0?$this->userinfo->name:$this->userinfo->real_name.' '.\Yii::t('base', 'download_init_dict');
        $expAuthChangeLog->log_ip = $_SERVER[ 'REMOTE_ADDR' ] ;
        $role_arr=[];
        $system_master = 0;
        if(@getVar($this->userinfo->role_ids)){
            $role_arr = explode(',',$this->userinfo->role_ids);
        }
        if(in_array('1',$role_arr)){
            $system_master = 1;
        }
        $expAuthChangeLog->system_master = $system_master ;
        $expAuthChangeLog->save();
        //echo $path;exit;

        if(!file_exists($path)){//检测文件是否存在
            echo \Yii::t('base', 'file_empty');
            die();
        }

        $fp=fopen($path,'r');//只读方式打开
        $filesize=filesize($path);//文件大小

        //返回的文件(流形式)
        header("Content-type: application/octet-stream");
        //按照字节大小返回
        header("Accept-Ranges: bytes");
        //返回文件大小
        header("Accept-Length: $filesize");
        //这里客户端的弹出对话框，对应的文件名
        header("Content-Disposition: attachment; filename=".$filename);
        //================重点====================
        ob_clean();
        flush();
        //=================重点===================
        //设置分流
        $buffer=1024;
        //来个文件字节计数器
        $count=0;
        while(!feof($fp)&&($filesize-$count>0)){
            $data=fread($fp,$buffer);
            $count+=$data;//计数
            echo $data;//传数据给浏览器端
        }

        fclose($fp);

    }

    public function actionDownloadSystemTemplateCn(){

        $lang = \Yii::$app->language;
        //en-US  zh-CN

        $bodyLang = 'cn';



        //避免中文文件名出现检测不到文件名的情况，进行转码utf-8->gbk
        if($bodyLang == 'en')
        {
            $fname = 'Enterprise_Dictionary.xlsx';
            $filename=iconv('utf-8', 'gb2312', $fname);
            $path=dirname(\Yii::$app->BasePath)."/frontend/web/template/company_dict_template_en.xlsx";
        }
        else
        {
            $fname = '企业词库模板（系统初始）.xlsx';
            $filename=iconv('utf-8', 'gb2312', $fname);
            $path=dirname(\Yii::$app->BasePath)."/frontend/web/template/company_dict_template.xlsx";

        }

        $encryption = new Encryption();
        if ($encryption->isHuatuEncrypt()) {
            $toFile = \Yii::getAlias('@svgpath') . DS . 'Enterprise_Dictionary_' . time() . '.xlsx';
            $path = (new Encryption())->encryptFileApi($toFile, $path); // 调用加密接口加密
        } else {
            $path = $encryption->encryptFileApi($path); // 调用加密接口加密
        }

        $expAuthChangeLog = new ExpAuthChangeLog();
        $expAuthChangeLog->user_id = $this->userinfo->id;
        $expAuthChangeLog->operate_desc = '['.\Yii::t('base', 'company_dict').']-'.\Yii::t('base', 'user').' '.strlen(trim($this->userinfo->name))>0?$this->userinfo->name:$this->userinfo->real_name.' '.\Yii::t('base', 'download_init_dict');
        $expAuthChangeLog->log_ip = $_SERVER[ 'REMOTE_ADDR' ] ;
        $role_arr=[];
        $system_master = 0;
        if(@getVar($this->userinfo->role_ids)){
            $role_arr = explode(',',$this->userinfo->role_ids);
        }
        if(in_array('1',$role_arr)){
            $system_master = 1;
        }
        $expAuthChangeLog->system_master = $system_master ;
        $expAuthChangeLog->save();
        //echo $path;exit;

        if(!file_exists($path)){//检测文件是否存在
            echo \Yii::t('base', 'file_empty');
            die();
        }

        $fp=fopen($path,'r');//只读方式打开
        $filesize=filesize($path);//文件大小

        //返回的文件(流形式)
        header("Content-type: application/octet-stream");
        //按照字节大小返回
        header("Accept-Ranges: bytes");
        //返回文件大小
        header("Accept-Length: $filesize");
        //这里客户端的弹出对话框，对应的文件名
        header("Content-Disposition: attachment; filename=".$filename);
        //================重点====================
        ob_clean();
        flush();
        //=================重点===================
        //设置分流
        $buffer=1024;
        //来个文件字节计数器
        $count=0;
        while(!feof($fp)&&($filesize-$count>0)){
            $data=fread($fp,$buffer);
            $count+=$data;//计数
            echo $data;//传数据给浏览器端
        }

        fclose($fp);

    }

    public function actionDownloadSystemTemplateEn(){

        $lang = \Yii::$app->language;
        //en-US  zh-CN

        $bodyLang = 'en';
        if(Yii::$app->params['language_list']['en'] == $lang){
            $bodyLang = 'en';
        }



        //避免中文文件名出现检测不到文件名的情况，进行转码utf-8->gbk
        if($bodyLang == 'en')
        {
            $fname = 'Enterprise_Dictionary.xlsx';
            $filename=iconv('utf-8', 'gb2312', $fname);
            $path=dirname(\Yii::$app->BasePath)."/frontend/web/template/company_dict_template_en.xlsx";
        }
        else
        {
            $fname = '企业词库模板（系统初始）.xlsx';
            $filename=iconv('utf-8', 'gb2312', $fname);
            $path=dirname(\Yii::$app->BasePath)."/frontend/web/template/company_dict_template.xlsx";

        }

        $encryption = new Encryption();
        if ($encryption->isHuatuEncrypt()) {
            $toFile = \Yii::getAlias('@svgpath') . DS . 'Enterprise_Dictionary_' . time() . '.xlsx';
            $path = (new Encryption())->encryptFileApi($toFile, $path); // 调用加密接口加密
        } else {
            $path = $encryption->encryptFileApi($path); // 调用加密接口加密
        }

        $expAuthChangeLog = new ExpAuthChangeLog();
        $expAuthChangeLog->user_id = $this->userinfo->id;
        $expAuthChangeLog->operate_desc = '['.\Yii::t('base', 'company_dict').']-'.\Yii::t('base', 'user').' '.strlen(trim($this->userinfo->name))>0?$this->userinfo->name:$this->userinfo->real_name.' '.\Yii::t('base', 'download_init_dict');
        $expAuthChangeLog->log_ip = $_SERVER[ 'REMOTE_ADDR' ] ;
        $role_arr=[];
        $system_master = 0;
        if(@getVar($this->userinfo->role_ids)){
            $role_arr = explode(',',$this->userinfo->role_ids);
        }
        if(in_array('1',$role_arr)){
            $system_master = 1;
        }
        $expAuthChangeLog->system_master = $system_master ;
        $expAuthChangeLog->save();
        //echo $path;exit;

        if(!file_exists($path)){//检测文件是否存在
            echo \Yii::t('base', 'file_empty');
            die();
        }

        $fp=fopen($path,'r');//只读方式打开
        $filesize=filesize($path);//文件大小

        //返回的文件(流形式)
        header("Content-type: application/octet-stream");
        //按照字节大小返回
        header("Accept-Ranges: bytes");
        //返回文件大小
        header("Accept-Length: $filesize");
        //这里客户端的弹出对话框，对应的文件名
        header("Content-Disposition: attachment; filename=".$filename);
        //================重点====================
        ob_clean();
        flush();
        //=================重点===================
        //设置分流
        $buffer=1024;
        //来个文件字节计数器
        $count=0;
        while(!feof($fp)&&($filesize-$count>0)){
            $data=fread($fp,$buffer);
            $count+=$data;//计数
            echo $data;//传数据给浏览器端
        }

        fclose($fp);

    }


    // add by wy 2023/3/10 物料表设置-导出物料表数据页面
    /**
     * 物料表设置（生成导出物料表数据页面）
     * ?r=group-setting/get-export-materials-data-page
     */
    public function actionGetExportMaterialsDataPage() {
        // 判断购买应用时是否购买了此功能
        $companyData = (new CenterInterface())->getCompanyInfoByCompanyId(1);
        $elnPaidItems = $companyData['data']['elnPaidItems'];
        $data['canDoExport'] = !empty($elnPaidItems['canExportReactionMaterialsData']);

        // 获取下拉框数据
        $groupList = (new CenterInterface())->elnGroupListByUserId($this->userinfo->id, $this->userinfo->current_company_id);
        $data['groupList'] = $groupList;
        $projectList = (new PMInterface())->getUserProjects($this->userinfo);
        $data['projectList'] = $projectList;
        $groupIds = array_column($groupList, 'id');
        // 获取模板列表
        $params = [
            'user_id' => $this->userinfo->id,
            'group_ids' => $groupIds,
        ];
        $tempListRes = (new TempleServer())->tempForExp($params);
        $tempList = @getVar($tempListRes['data'], []);
        $data['tempList'] = $tempList;
        $file = $this->renderAjax('/setting/export_materials_data.php',$data);
        return $this->success([
            'contentHtml' => $file,
        ]);
    }

    //add by wy 2023/3/10 中英文物料名称设置
    /**
     * 物料表设置（中英文物料名称设置）
     * ?r=group-setting/company-setting-show-iupac
     */
    public function actionCompanySettingShowIupac() {
        //获取用户权限
        $userInfo = (new CenterInterface())->getUserByUserId($this->userinfo->id);
        $role = array_column($userInfo['role_info'],'role_id');

        //判断用户是否有编辑权限
        $isCanWrite = \Yii::$app->view->params['decimal_config_write'] == 1;
        if(in_array(1, $role)) {
            $isCanWrite = true;
        }
        $data['isCanWrite'] = $isCanWrite;

        //获取企业的iupac中英文显示设置
        $company_id = $this->userinfo->current_company_id;
        $companyDecimal = new CompanyDecimal();
        $decimalDetail = $companyDecimal->findOne(['company_id'=>$company_id]);
        $data['iupac_setting'] = $decimalDetail['iupac_setting'];

        $file = $this->renderAjax('/setting/company_setting_show_iupac.php', $data);
        return $this->success([
            'contentHtml' => $file,
        ]);
    }

    /**
     * 企业设置（小数点）
     * ?r=group-setting/company-setting-decimal
     */
    public function actionCompanySettingDecimal(){

        //获取企业的自定义配置
        $company_id = $this->userinfo->current_company_id;
        $companyDecimal = new CompanyDecimal();
        $decimalDetail = $companyDecimal->findOne(['company_id'=>$company_id]);

        $userInfo = (new CenterInterface())->getUserByUserId($this->userinfo->id);
        $role = array_column($userInfo['role_info'],'role_id');

        $authData = (new CompanyAuthServer())->getCompanyAuthByUserId($this->userinfo->id, 1);
        $decimal_config_read = @getVar($authData['company_feature']['view_decimal_config'], 0);
        $decimal_config_write = @getVar($authData['company_feature']['edit_decimal_config'], 0);
        // 权限控制 jiangdm 2022/7/13
        if (!\Yii::$app->view->params['is_system_admin'] && $decimal_config_read != 1 && $decimal_config_write != 1) {
            return $this->fail(\Yii::t('base', 'no_auth'));
        }

        $isCanWrite = $decimal_config_write == 1;

        /*验证完成*/

        if (in_array(1,$role)){
            $isCanWrite = true;
        }


        $data['detimal']=$decimalDetail;
        $data['isCanWrite']=$isCanWrite;


        $file = $this->renderAjax('/setting/company_setting_decimal.php',$data);
        return $this->success([
            'contentHtml' => $file,

        ]);
    }

    //add by wy 2023/3/16 保存中英文物料名称设置
    /**
     * 中英文物料名称设置
     * ?r=group-setting/company-iupac-setting-submit
     */
    public function actionCompanyIupacSettingSubmit() {
        //权限控制
        $authData = (new CompanyAuthServer())->getCompanyAuthByUserId($this->userinfo->id, 1);
        $decimal_config_write = @getVar($authData['company_feature']['edit_decimal_config'], 0);
        if (!\Yii::$app->view->params['is_system_admin'] && $decimal_config_write != 1) {
            return $this->fail(\Yii::t('base', 'no_auth'));
        }
        //获取iupac设置
        $data = \Yii::$app->request->post();
        $company_id = $this->userinfo->current_company_id;
        $data['company_id'] = $company_id;

        $companyDecimal = new CompanyDecimal();
        $decimalDetail = $companyDecimal->findOne(['company_id'=>$company_id]);
        if(empty($decimalDetail)) {
            $companyDecimal->setAttributes($data);
            $companyDecimal->save();
        } else {
            $data['update_time'] = date('Y-m-d H:i:s');
            $companyDecimal->updateAll($data,['id'=>$decimalDetail->id]);
        }

        //添加日志
        $expAuthChangeLog = new ExpAuthChangeLog();
        $expAuthChangeLog->user_id = $this->userinfo->id;
        $expAuthChangeLog->operate_desc = '['.\Yii::t('views/left', 'company_auth').']-[' . \Yii::t('base','setting_name_of_materials') . '] 为' . \Yii::t('base',$data['iupac_setting']);

        $expAuthChangeLog->log_ip = $_SERVER[ 'REMOTE_ADDR' ] ;
        $role_arr=[];
        $system_master = 0;
        if(@getVar($this->userinfo->role_ids)){
            $role_arr = explode(',',$this->userinfo->role_ids);
        }
        if(in_array('1',$role_arr)){
            $system_master = 1;
        }
        $expAuthChangeLog->system_master = $system_master ;
        $expAuthChangeLog->save();

        return  $this->success([]);
    }

    /**
     * 企业设置
     * ?r=group-setting/company-setting-decimal-submit
     */
    public function actionCompanySettingDecimalSubmit(){
        // 权限控制 jiangdm 2022/7/13
        $authData = (new CompanyAuthServer())->getCompanyAuthByUserId($this->userinfo->id, 1);
        $decimal_config_write = @getVar($authData['company_feature']['edit_decimal_config'], 0);
        if (!\Yii::$app->view->params['is_system_admin'] && $decimal_config_write != 1) {
            return $this->fail(\Yii::t('base', 'no_auth'));
        }
        //获取企业的自定义配置
        $data = \Yii::$app->request->post();
        $company_id = $this->userinfo->current_company_id;
        $data['company_id'] = $company_id;


        $companyDecimal = new CompanyDecimal();
        $decimalDetail = $companyDecimal->findOne(['company_id'=>$company_id]);

        if(empty($decimalDetail))
        {

            $companyDecimal->setAttributes($data);
            $companyDecimal->save();

        }
        else
        {
            $data['update_time']=date('Y-m-d H:i:s');
            $companyDecimal->updateAll($data,['id'=>$decimalDetail->id]);
        }

        /*添加日志*/
        $expAuthChangeLog = new ExpAuthChangeLog();
        $expAuthChangeLog->user_id = $this->userinfo->id;
        $expAuthChangeLog->operate_desc = '['.\Yii::t('views/left', 'company_auth').']-['.\Yii::t('base', 'decimal_point_setting_of_materials').'] 为: Reactant-Eq:'.$data['reactant_eq'].';Reactant-N:'.$data['reactant_n'].';Reactant-MW:'.$data['reactant_mw'].';Reactant-Mass:'.$data['reactant_mass'].';Reactant-D:'.$data['reactant_d'].';Reactant-V:'.$data['reactant_v'].';Reagent-Eq:'.$data['reagent_eq'].';Reagent-N:'.$data['reagent_n'].';Reagent-MW:'.$data['reagent_mw'].';Reagent-Mass:'.$data['reagent_mass'].';Reagent-D:'.$data['reagent_d'].';Reagent-V:'.$data['reagent_d'].';Product-N:'.$data['product_n'].';Product-MW:'.$data['product_mw'].';Product-Theo Mass:'.$data['product_theo_mass'].';Product-Yield:'.$data['product_yield'].';Solvent-B:'.$data['solvent_b'].';Solvent-Mass:'.$data['solvent_mass'].';Solvent-D:'.$data['solvent_d'].';Solvent-Bp:'.$data['solvent_bp'];

        $expAuthChangeLog->log_ip = $_SERVER[ 'REMOTE_ADDR' ] ;
        $role_arr=[];
        $system_master = 0;
        if(@getVar($this->userinfo->role_ids)){
            $role_arr = explode(',',$this->userinfo->role_ids);
        }
        if(in_array('1',$role_arr)){
            $system_master = 1;
        }
        $expAuthChangeLog->system_master = $system_master ;
        $expAuthChangeLog->save();
        /*添加日志完成*/

        return $this->success([]);

    }

    /**
     * 设置实验核查人员
     * ?r=group-setting/get-share-set
     *
     * <AUTHOR>
     * @copyright 2016-10-29
     * post
     * @param int $group_id 鹰群id
     * @param int|null $user_id 如果是针对鹰群设置的无需传递
     * @param boolean is_group 1表示是group 0表示是用户
     */
    public function actionSetCheckUser() {

        //根据userID 获取 企业下所有用户的信息
        $company_id = \Yii::$app->view->params['curr_company_id'];

        /*验证用户有没有设置实验核查人员的权限*/
        //验证是否是系统管理员
        $userDetails  = (new CenterInterface())->userDetailsByUserIds([$this->userinfo->id]);
         if(isset($userDetails[0]['role'])){
            $is_admin = in_array(1,$userDetails[0]['role'])?1:0;
        }else{
            $is_admin = 0;
        }
        //如果不是管理员验证是否是有相关权限
        if(!$is_admin){
            $isCan= (new CompanyAuthServer())->isCanCompanyAuth($this->userinfo->current_company_id,0, $this->userinfo->id,'auth_config_write');
            if(!$isCan )
            {
               return $this->fail(\Yii::t('setting', 'no_rights_tips'));
            }
        }


        /*获取默认用户*/
        $companyCheckUser = new CompanyCheckUser();
        $defaultResult = $companyCheckUser->find()->where(['company_id'=>$this->userinfo->current_company_id,'status'=>1])->asArray(TRUE)->all();

        /*默认用户*/
        $default_user_list = array_column($defaultResult,'user_id');
        $default_user_list = array_unique($default_user_list);
        /*默认鹰群*/
        $default_group_list = array_column($defaultResult,'group_id');
        $default_group_list = array_unique($default_group_list);

        $data['defalut_user_ids']=$default_user_list;
        $data['defalut_user_names']=[];
        $data['defalut_group_names']=[];

        /*获取默认鹰群*/
        $data['shareDefaultUsersIds']=$default_user_list;
        $data['shareDefaultGroupIds']=$default_group_list;
        $userList = (new CenterInterface())->getUserListByCompanyId($company_id);
        //print_r($userList);exit;
        $data['user_list'] = isset($userList['list'])?$userList['list']:[];
        if($data['user_list']){
            foreach($data['user_list'] as $key=>$value){
                $data['user_list'][$key]['is_checked']='';
                if(in_array($value['uid'], $data['shareDefaultUsersIds'])){
                    $data['user_list'][$key]['is_checked']='checked';
                    $data['defalut_user_names'][] = strlen(trim($value['nick_name']))>0?$value['nick_name']:$value['real_name'];
                }
            }
        }
        // print_r($data['user_list']);exit;
        /*给默认用户赋值用户名和ID*/

        //获取该企业下所有的鹰群
        $data['group_list'] = (new CenterInterface())->getGroupsListByCompanyId($company_id);

        if($data['group_list']){
            foreach($data['group_list'] as $key=>$value){
                $data['group_list'][$key]['is_checked']='';
                if(in_array($value['id'], $data['shareDefaultGroupIds'])){
                    $data['group_list'][$key]['is_checked']='checked';
                    $data['defalut_group_names'][] = $value['group_name'];
                }
            }
        }


        //print_r($data);exit;
        $html = $this->renderAjax('/setting/set_check_user.php', $data);

        return $this->success([
            'file' => $html
        ]);
    }

    /**
     * 企业级权限配置
     * ?r=group-setting/get-share-set
     *
     * <AUTHOR>
     * @copyright 2016-10-29
     * post
     * @param int $group_id 鹰群id
     * @param int|null $user_id 如果是针对鹰群设置的无需传递
     * @param boolean is_group 1表示是group 0表示是用户
     */
    public function actionSetCompanyAuthConfig() {
        $postData = \Yii::$app->getRequest()->post();

        //验证用户是否是系统管理员
        $userDetails  = (new CenterInterface())->userDetailsByUserIds([$this->userinfo->id]);
        if(isset($userDetails[0]['role'])){
            $is_admin = in_array(1,$userDetails[0]['role'])?1:0;
        }else{
            $is_admin = 0;
        }
        //如果不是管理员验证是否是有相关权限
        if(!$is_admin){
            $isCan= (new CompanyAuthServer())->isCanCompanyAuth($this->userinfo->current_company_id,0, $this->userinfo->id,'auth_config_write');
            if(!$isCan )
            {
               return $this->fail(\Yii::t('base', 'no_power'));
            }
        }

       // print_r($postData);exit;
        if (empty($postData['user_id'])) {
            return $this->fail(\Yii::t('base', 'select_user'));
        }


        $userId = !empty($postData['user_id']) ? $postData['user_id'] : NULL;

       $data=[];
        $userDetails = (new CenterInterface())->userDetailsByUserIds($userId);
        foreach ($userDetails as $user) {
            if (in_array(1, $user['role'])) {
                return $this->fail(\Yii::t('base', 'admin_modify_notice'));
            }
        }
        if(count($userId) == 1)
        {
            //验证设置人员是否是系统管理员
            $userDetails = (new CenterInterface())->userDetailsByUserIds([$userId[0]]);
            if (isset($userDetails[0]['role'])) {
                $data['isAdmin'] = in_array(1, $userDetails[0]['role']) ? 1 : 0;
            } else {
                $data['isAdmin'] = 0;
            }

            //获取鹰群权限的默认值
            $user_info  =explode('_',$userId[0]);
            $company_id = $this->userinfo->current_company_id;
            $group_id = $user_info[1];
            $user_id = $user_info[0];

            $companyAuth = new CompanyAuth();
            $companyAuthDetail = $companyAuth->find()->where(['group_id'=>$group_id,'user_id'=>$user_id,'company_id'=>$this->userinfo->current_company_id])->asArray()->one();
            $data['company_auth_detail'] = $companyAuthDetail;

            if($companyAuthDetail['auth_config_read']==1 && $companyAuthDetail['decimal_config_read']==1 ){
                $data['company_auth_detail']['company_config_read_all']=1;
            }
            if($companyAuthDetail['auth_config_write']==1 && $companyAuthDetail['decimal_config_write']==1 ){
                $data['company_auth_detail']['company_config_write_all']=1;
            }
            if($companyAuthDetail['login_log_read']==1 && $companyAuthDetail['share_log_read']==1 && $companyAuthDetail['view_log_read']==1 && $companyAuthDetail['export_log_read']==1 && $companyAuthDetail['set_log_read']==1){
                $data['company_auth_detail']['log_all_read']=1;
            }

        }
        else
        {
            $data['company_auth_detail']=[];
        }

        $html = $this->renderAjax('/setting/set_company_auth_config.php', $data);

        return $this->success([
            'file' => $html
        ]);
    }

    /**
     * 鹰群设置页权限
     * ?r=group-setting/set-group-auth-config
     *
     * <AUTHOR>
     * @copyright 2016-10-29
     * post
     * @param int $group_id 鹰群id
     * @param int|null $user_id 如果是针对鹰群设置的无需传递
     * @param boolean is_group 1表示是group 0表示是用户
     */
    public function actionSetGroupAuthConfig() {
        $postData = \Yii::$app->getRequest()->post();
        if (empty($postData['user_id'])) {
            return $this->fail(\Yii::t('base', 'select_user'));
        }
        $userId = $postData['user_id'];

        // 验证是否是系统管理员
        $userDetails = (new CenterInterface())->userDetailsByUserIds([$this->userinfo->id]);

        if (isset($userDetails[0]['role'])) {
            $is_admin = in_array(1, $userDetails[0]['role']) ? 1 : 0;
        } else {
            $is_admin = 0;
        }

        // 如果不是管理员验证是否是有相关权限
        if (!$is_admin) {
            $isCan = (new CompanyAuthServer())->isCanCompanyAuth($this->userinfo->current_company_id, 0, $this->userinfo->id, 'auth_config_write');
            if (!$isCan) {
                return $this->fail(\Yii::t('base', 'no_power'));
            }
        }

        $disabledAuth = [];
        if (count($userId) == 1) {
            //获取鹰群权限的默认值
            $userInfo = explode('_', $userId[0]);
            $companyId = $this->userinfo->current_company_id;
            $groupId = $userInfo[1];
            $userId = $userInfo[0];

            $authRes = CompanyAuthServer::getGroupAuth($companyId, $groupId, $userId);
            if (!empty($authRes['disabled_auth'])) {
                $disabledAuth = json_decode($authRes['disabled_auth'], true);
            }
        }

        $html = $this->renderAjax('/setting/set_group_auth_config.php', [
            'authArr' => CompanyAuthServer::listGroupMemberAuth(),
            'disabledAuth' => $disabledAuth
        ]);

        return $this->success([
            'file' => $html
        ]);
    }

    /**
     * 鹰群设置页权限
     * ?r=group-setting/set-group-auth-config-submit
     *
     * <AUTHOR>
     * @copyright 2016-10-29
     * post
     * @param int $group_id 鹰群id
     * @param int|null $user_id 如果是针对鹰群设置的无需传递
     * @param boolean is_group 1表示是group 0表示是用户
     */
    public function actionSetGroupAuthConfigSubmit() {
        $postData = \Yii::$app->getRequest()->post();
        if (empty($postData['user_id'])) {
            return $this->fail(\Yii::t('base', 'select_user'));
        }
        $userId = $postData['user_id'];
        $enabledAuth = @getVar($postData['enabled_auth'], []);
        $disabledAuth = @getVar($postData['disabled_auth'], []);

        $authServer = new CompanyAuthServer();
        foreach ($userId as $value) {
            $userInfo = explode('_', $value);
            $companyId = $this->userinfo->current_company_id;
            $groupId = $userInfo[1];
            $userId = $userInfo[0];
            $authServer->setGroupAuth($companyId, $groupId, $userId, $disabledAuth);
        }

        /* 添加日志 */
        $expAuthChangeLog = new ExpAuthChangeLog();
        $expAuthChangeLog->user_id = $this->userinfo->id;
        $expAuthChangeLog->operate_desc = '[' . \Yii::t('base', 'company_setting') . ']';
        $expAuthChangeLog->operate_desc .= '-[' . \Yii::t('base', 'group_setting_permission') . ']';
        $expAuthChangeLog->operate_desc .= \Yii::t('base', 'set the user') . ' ' . join(',', $postData['user_name']);
        $expAuthChangeLog->operate_desc .= ' ' . \Yii::t('base', 'power is') . ':</br>';
        foreach (CompanyAuthServer::listGroupMemberAuth() as $key => $val) {
            $expAuthChangeLog->operate_desc .= $val . ' ' . \Yii::t('base', in_array($key, $enabledAuth) ? 'enable' : 'disable') . ';</br>';
        }

        $expAuthChangeLog->log_ip = $_SERVER['REMOTE_ADDR'];
        $role_arr = [];
        $system_master = 0;
        if (@getVar($this->userinfo->role_ids)) {
            $role_arr = explode(',', $this->userinfo->role_ids);
        }
        if (in_array('1', $role_arr)) {
            $system_master = 1;
        }
        $expAuthChangeLog->system_master = $system_master;
        $expAuthChangeLog->save();
        /* 添加日志完成 */

        return $this->success([]);
    }

    /**
     * 鹰群设置页权限
     * ?r=group-setting/set-check-user-submit
     *
     * <AUTHOR>
     * @copyright 2016-10-29
     * post
     * @param int $group_id 鹰群id
     * @param int|null $user_id 如果是针对鹰群设置的无需传递
     * @param boolean is_group 1表示是group 0表示是用户
     */
    public function actionSetCheckUserSubmit() {
        $postData = \Yii::$app->getRequest()->post();

        $user_arr = explode(',',$postData['user_ids']);
        $group_arr = explode(',',$postData['group_ids']);
        $group_name = $postData['group_name'];
        $user_name = $postData['user_name'];

        $companyCheckUser = new CompanyCheckUser();
        $companyCheckUser->updateAll(['status'=>0,'update_time'=>date('Y-m-d H:i:s')],['company_id'=>$this->userinfo->current_company_id]);
        //有则更新 无 则添加
        foreach($user_arr as $user)
        {
            //将所有设置过的设为无效

            //重新赋值
            foreach($group_arr as $group){
                $data['company_id'] = $this->userinfo->current_company_id;
                $data['group_id'] =  $group;
                $data['user_id'] =  $user;

                $companyCheckUser = new CompanyCheckUser();
                $companyCheckUserDetail = $companyCheckUser->findOne(['company_id'=>$this->userinfo->current_company_id,'group_id'=>$group,'user_id'=>$user]);
                if(empty($companyCheckUserDetail))
                {
                    $data['status']=1;
                    $companyCheckUser-> setAttributes($data);
                    $result = $companyCheckUser->save();
                }
                else
                {
                    if($companyCheckUserDetail->status == 0)
                    {
                        $data['update_time']=date('Y-m-d H:i:s');
                        $companyCheckUser->updateAll(['status'=>1],['id'=>$companyCheckUserDetail->id]);
                    }
                }
            }
        }

        /*添加日志*/
        $expAuthChangeLog = new ExpAuthChangeLog();
        $expAuthChangeLog->user_id = $this->userinfo->id;
        $expAuthChangeLog->operate_desc = '['.\Yii::t('base', 'company_setting').']-['.\Yii::t('base', 'set_check_user').'] '.\Yii::t('base', 'set the user').' '.$user_name.';'.\Yii::t('base', 'set group name').':'.$group_name;
        $expAuthChangeLog->log_ip = $_SERVER[ 'REMOTE_ADDR' ] ;
        $role_arr=[];
        $system_master = 0;
        if(@getVar($this->userinfo->role_ids)){
            $role_arr = explode(',',$this->userinfo->role_ids);
        }
        if(in_array('1',$role_arr)){
            $system_master = 1;
        }
        $expAuthChangeLog->system_master = $system_master ;
        $expAuthChangeLog->save();
        /*添加日志完成*/

        return $this->success([]);
    }

     /**
     * 企业级权限配置 提交
     * ?r=group-setting/set-company-auth-config-submit
     *
     * <AUTHOR>
     * @copyright 2016-10-29
     * post
     * @param int $group_id 鹰群id
     * @param int|null $user_id 如果是针对鹰群设置的无需传递
     * @param boolean is_group 1表示是group 0表示是用户
     */
    public function actionSetCompanyAuthConfigSubmit() {
        $postData = \Yii::$app->getRequest()->post();

        $data=$postData;
        unset($data['user_id']);
        unset($data['user_name']);


        //有则更新 无 则添加
        foreach($postData['user_id'] as $key=>$value)
        {

            $user_info  =explode('_',$value);
            $company_id = $this->userinfo->current_company_id;
            $group_id = $user_info[1];
            $user_id = $user_info[0];

            $data['company_id'] = $company_id;
            $data['group_id'] =  $group_id;
            $data['user_id'] =  $user_id;

            $companyAuth = new CompanyAuth();
            $companyDetailAuth = $companyAuth->findOne(['company_id'=>$company_id,'group_id'=>$group_id,'user_id'=>$user_id]);

            if(empty($companyDetailAuth))
            {
                $companyAuth->setAttributes($data);
                $result = $companyAuth->save();
            }
            else
            {
                $data['update_time']=date('Y-m-d H:i:s');
                $companyAuth->updateAll($data,['id'=>$companyDetailAuth->id]);
            }
        }

        // 需要修改日志增加记录本管理 仪器库管理 todo

        /*添加日志*/
        $expAuthChangeLog = new ExpAuthChangeLog();
        $expAuthChangeLog->user_id = $this->userinfo->id;
        $expAuthChangeLog->operate_desc = '['.\Yii::t('base', 'company_setting').']-['.\Yii::t('base', 'enterprise_permission_setting').'] '.\Yii::t('base', 'set the user').' '.join(',',$postData['user_name']).' '.\Yii::t('base', 'power is').':'.str_replace(["1", '0'], [\Yii::t('base', 'read').\Yii::t('base', 'company_setting').\Yii::t('base', 'enable'),\Yii::t('base', 'read').\Yii::t('base', 'company_setting').\Yii::t('base', 'disable')], $postData['auth_config_read']).";".str_replace(["1", '0'], [\Yii::t('base', 'edit').\Yii::t('base', 'company_setting').\Yii::t('base', 'enable'),\Yii::t('base', 'edit').\Yii::t('base', 'company_setting').\Yii::t('base', 'disable')], $postData['auth_config_write']).';'.str_replace(["1", '0'], [\Yii::t('base', 'read').\Yii::t('base', 'decimal_point_setting_of_materials').\Yii::t('base', 'enable'),\Yii::t('base', 'read').\Yii::t('base', 'decimal_point_setting_of_materials').\Yii::t('base', 'disable')], $postData['decimal_config_read']).';'.str_replace(["1", '0'], [\Yii::t('base', 'edit').\Yii::t('base', 'decimal_point_setting_of_materials').\Yii::t('base', 'enable'),\Yii::t('base', 'edit').\Yii::t('base', 'decimal_point_setting_of_materials').\Yii::t('base', 'disable')], $postData['decimal_config_write']).';'.str_replace(["1", '0'], [\Yii::t('base', 'read').\Yii::t('base', 'group_setting').\Yii::t('base', 'enable'),\Yii::t('base', 'read').\Yii::t('base', 'group_setting').\Yii::t('base', 'disable')], $postData['group_config_read']).';'.str_replace(["1", '0'], [\Yii::t('base', 'read').\Yii::t('base', 'sys_log').\Yii::t('base', 'enable'),\Yii::t('base', 'read').\Yii::t('base', 'sys_log').\Yii::t('base', 'disable')], $postData['use_static_read']).';'.str_replace(["1", '0'], [\Yii::t('base', 'read').\Yii::t('base', 'company_dict').\Yii::t('base', 'enable'),\Yii::t('base', 'read').\Yii::t('base', 'company_dict').\Yii::t('base', 'disable')], $postData['company_dict_read']).';'.str_replace(["1", '0'], [\Yii::t('base', 'edit').\Yii::t('base', 'company_dict').\Yii::t('base', 'enable'),\Yii::t('base', 'edit').\Yii::t('base', 'company_dict').\Yii::t('base', 'disable')], $postData['company_dict_write']).';'.str_replace(["1", '0'], [\Yii::t('base', 'read').\Yii::t('base', 'login_log').\Yii::t('base', 'enable'),\Yii::t('base', 'read').\Yii::t('base', 'login_log').\Yii::t('base', 'disable')], $postData['login_log_read']).';'.str_replace(["1", '0'], [\Yii::t('base', 'read').\Yii::t('base', 'share_log').\Yii::t('base', 'enable'),\Yii::t('base', 'read').\Yii::t('base', 'share_log').\Yii::t('base', 'disable')], $postData['share_log_read']).';'.str_replace(["1", '0'], [\Yii::t('base', 'read').\Yii::t('base', 'view_log').\Yii::t('base', 'enable'),\Yii::t('base', 'read').\Yii::t('base', 'view_log').\Yii::t('base', 'disable')], $postData['view_log_read']).';'.str_replace(["1", '0'], [\Yii::t('base', 'read').\Yii::t('base', 'file_export_log').\Yii::t('base', 'enable'),\Yii::t('base', 'read').\Yii::t('base', 'file_export_log').\Yii::t('base', 'disable')], $postData['export_log_read']).';'.str_replace(["1", '0'], [\Yii::t('base', 'read').\Yii::t('base', 'setting_log').\Yii::t('base', 'enable'),\Yii::t('base', 'read').\Yii::t('base', 'setting_log').\Yii::t('base', 'disable')], $postData['set_log_read']).';'.str_replace(["1", '0'], [\Yii::t('base', 'edit').\Yii::t('base', 'enterprise_template_setting').\Yii::t('base', 'enable'),\Yii::t('base', 'read').\Yii::t('edit', 'enterprise_template_setting').\Yii::t('base', 'disable')], $postData['set_conmany_template_write']);

        $expAuthChangeLog->log_ip = $_SERVER[ 'REMOTE_ADDR' ] ;
        $role_arr=[];
        $system_master = 0;
        if(@getVar($this->userinfo->role_ids)){
            $role_arr = explode(',',$this->userinfo->role_ids);
        }
        if(in_array('1',$role_arr)){
            $system_master = 1;
        }
        $expAuthChangeLog->system_master = $system_master ;
        $expAuthChangeLog->save();
        /*添加日志完成*/

        return $this->success([]);
    }

    /**
     * 企业设置
     * ?r=group-setting/use-static
     */
    public function actionUseStatic(){
        // 权限控制 jiangdm 2022/7/13
        if (!\Yii::$app->view->params['is_system_admin'] && \Yii::$app->view->params['use_static_read'] != 1) {
            return $this->fail(\Yii::t('base', 'no_auth'));
        }

        //渲染页面
        //获取字段显示配置
        $useStaticConfig = (new GroupSettingServer())->getUseStaticConfig($this->userinfo->id);
        //字段显示配置获取完成


        \Yii::info('渲染页面开始：');
        $page = \Yii::$app->request->post('page', 1);
        $limit = \Yii::$app->request->post('limit', \Yii::$app->params['default_page_size']);
        $orderBy = \Yii::$app->request->post('order_by', 'id');
        $orderType = \Yii::$app->request->post('order_type', 'asc');

        $where['user_id'] = \Yii::$app->request->post('user_id', 0);
        $where['group_id'] = \Yii::$app->request->post('group_id', 0);
        $where['department_id'] = \Yii::$app->request->post('department_id', 0);
        $where['role_id'] = \Yii::$app->request->post('role_id', 0);
        $where['start_time'] = \Yii::$app->request->post('start_time', '');
        $where['end_time'] = \Yii::$app->request->post('end_time', '');
        $where['order'] = \Yii::$app->request->post('order', 'asc');
        $where['sort'] = \Yii::$app->request->post('sort', 'id');
        $where['include_history'] = \Yii::$app->request->post('include_history', 0);

        //查询该企业下可见用户
        $company_id = \Yii::$app->view->params['curr_company_id'];
        if(!$company_id){
            return $this->fail(\Yii::t('base', 'select_user'));
        }
        $userList = (new CenterInterface())->getVisibleUsers($this->userinfo->user_id, false, [0, 1, 2]);
        $data['all_user'] = $userList;
        //获取可见部门列表
        $departmentList = (new CenterInterface())->getVisibleDepartments($this->userinfo->user_id);

//        拼接部门完整路径
        $allDepartments = (new CenterInterface())->getDepartmentListByCompanyId(1, '', false, true);
        $allDepartments = ArrayHelper::index($allDepartments['list'], 'id');
        foreach ($allDepartments as &$department) {
            $department['full_name'] = $department['department_name'];
            $p = $department;
            while (!empty($allDepartments[$p['parent_department']])) {
                $department['full_name'] = $allDepartments[$p['parent_department']]['department_name'] . '-' . $department['full_name'];
                $p = $allDepartments[$p['parent_department']];
            }
        }
        $data['department_list'] = $allDepartments;
        //获取角色列表
        $roleList = (new CenterInterface())->getRoleListByCompanyId($company_id);
        $data['role_list'] = $roleList['list'];
        //获取可见鹰群列表
        $groupList = (new CenterInterface())->getVisibleGroups($this->userinfo->user_id);
        $data['group_list'] = $groupList;

        $data['params'] = $where;
        //print_r($where);exit;

        $where['user_id'] = !empty($where['user_id']) ? $where['user_id'] : array_column($userList, 'user_id');
            $userData = (new GroupSettingServer())->listUseStatic($where, $limit, $page, $where['order'], $where['sort'],$useStaticConfig);
        //根据时间统计关闭的实验数和成功的实验数

        $data['user_list'] = $userData['user_list'];
        $data['limit'] = $limit;
        $data['totalCount'] = $userData['totalCount'];
        $data['useStaticConfig'] = $useStaticConfig;
        
        $data['all_user'] = yii\helpers\ArrayHelper::index($data['all_user'], 'user_id');
        foreach ($data['user_list'] as $key => &$value) {
            if (array_key_exists($value['user_id'], $data['all_user'])) {
                $value['real_name'] = $data['all_user'][$value['user_id']]['real_name'];
                $value['name'] = $data['all_user'][$value['user_id']]['name'];
                $value['dep_full_names'] = '';
                $depIdArr = empty($value['dep_ids']) ? [] : explode(',', $value['dep_ids']);
                $depNameArr = array_map(function ($id) use ($allDepartments) {
                    return $allDepartments[$id]['full_name'];
                }, $depIdArr);
                $value['dep_full_names'] = join(', ', $depNameArr);

                $depSimpleNameArr = array_map(function ($id) use ($allDepartments) {
                    return $allDepartments[$id]['department_name'];
                }, $depIdArr);
                $value['dep_names'] = join(', ', $depSimpleNameArr);
            }
        }

        //$file = $this->renderAjax('/setting/use_static.php',$data);
        $needUpdateAllPage = \Yii::$app->request->post('needUpdateAllPage', 1); // add by hkk 2020/6/23
        // 第一次渲染，更新整个页面
        if (!empty($needUpdateAllPage)) {
            $file = $this->renderAjax('/static/use_static.php', $data);
            return $this->success(['contentHtml' => $file]);
        }

        // 传page参数只更新表格数据
        $file = $this->renderAjax('/static/use_static_page.php', $data);
        return $this->success(['contentHtml' => $file]);

    }

    /**
     * 企业设置
     * ?r=group-setting/use-static
     */
    public function actionInitUseStatic(){

//        //初始化数据
//        //获取当前企业下的所有用保护
        $user_list = (new CenterInterface())->getUserListByCompanyId($this->userinfo->current_company_id);

        //开始拼装数据
        foreach($user_list['list'] as $value)
        {
            $data['user_id'] = $value['uid'];
            $data['user_name'] = strlen(trim($value['nick_name']))>0?$value['nick_name']:$value['real_name'];

            $data['dep_ids'] = $value['dep_ids'];
            $data['dep_names'] = $value['dep_names'];
            $data['role_ids'] = $value['role_ids'];
            $data['role_names'] = $value['role_names'];

            //根据用户获取鹰群数
            $group_list = (new CenterInterface())->allGroupByUserId($value['uid']);
            //print_r($group_list);
            $data['group_num'] = count($group_list);
            $data['group_ids'] = join(',',array_column($group_list,'group_id'));
            //根据用户获取创建记录本总数
//            $data['book_num'] = BookModel::find()->where(['user_id'=>$value['uid']])->count();
//            //根据用户获取创建实验总数
//            $data['exp_num'] = ExperimentModel::find()->where(['user_id'=>$value['uid']])->count();
//
//            //根据用户获取关闭实验总数
//            $data['exp_close_num'] = ExperimentModel::find()->where(['user_id'=>$value['uid'],'step'=>[2,5]])->count();
//            //根据用户获取成功实验总数
//            $data['exp_success_num'] = ExperimentModel::find()->where(['user_id'=>$value['uid'],'result'=>[1]])->count();;
            //根据用户获取最近登录时间
            $userInfo = (new CenterInterface())->userDetailsByUserIds($value['uid']);

            $data['last_login_time'] = $userInfo[0]['login_time'];

            $useStatic = new UseStatic();
            $userInfo = $useStatic->findOne(['user_id'=>$value['uid']]);
            if(empty($userInfo))
            {
                $useStatic->setAttributes($data);
                $result = $useStatic->save();
            }
            else
            {

                $data['update_time']=date('Y-m-d H:i:s');
                $useStatic->updateAll($data,['id'=>$userInfo->id]);
            }

        }
        //exit;
//

    }

    /**
     * 企业设置
     * ?r=group-setting/use-static
     */
    public function actionUseStaticExport()
    {

        $where['user_id'] = \Yii::$app->request->get('user_id', 0);
        $where['group_id'] = \Yii::$app->request->get('group_id', 0);
        $where['department_id'] = \Yii::$app->request->get('department_id', 0);
        $where['role_id'] = \Yii::$app->request->get('role_id', 0);
        $where['start_time'] = \Yii::$app->request->get('start_time', '');
        $where['end_time'] = \Yii::$app->request->get('end_time', '');
        $where['order'] = \Yii::$app->request->get('order', 'asc');
        $where['sort'] = \Yii::$app->request->get('sort', 'id');
        $userId = $this->userinfo->user_id;
        ob_end_clean();
        require_once \Yii::$app->vendorPath . '/PHPExcel/PHPExcel.php';
        require_once \Yii::$app->vendorPath . '/PHPExcel' . DIRECTORY_SEPARATOR . 'IOFactory.php';
        $objPHPExcel = new PHPExcel();
        $groupSetting = new GroupSettingServer();
        $showConfigMap = $groupSetting->getUseStaticShowMap($userId, false);
        $groupSetting->useStaticExport($objPHPExcel, $where, $userId, $showConfigMap);
        $objPHPExcel->setActiveSheetIndex(0);
        $objWriter = \PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel2007');
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="' . \Yii::t('base', 'use_statistics') . '-' . date('Ymd') . '.xlsx"');
        header('Cache-Control: max-age=0');
//        $objWriter->save('php://output');

        $fileName = \Yii::t('base', 'use_statistics') . '-' . date('Ymd');
        $file = \Yii::getAlias('@filepath') . DS . $fileName . '.xlsx';
        $encryption = new Encryption();
        if ($encryption->isHuatuEncrypt()) {
            $tmpFile = \Yii::getAlias('@svgpath') . DS . 'use_statistics.xlsx';
            $objWriter->save($tmpFile);
            $file = $encryption->encryptFileApi(\Yii::getAlias('@filepath') . DS . 'use_statistics.xlsx', $tmpFile); // 调用加密接口加密
            @unlink($tmpFile);// 删除临时文件
        } else {
            $objWriter->save($file);
            $file = $encryption->encryptFileApi($file); // 调用加密接口加密
        }
        readfile($file);
        return $this->success([
            'file_name'=>$fileName
        ]);

    }

    /**
     * 企业设置
     * 记录本编号规则设置
     * add by hkk 2019/9/23
     * ?r=group-setting/book-number-manage
     */
    public function actionBookNumberManage(){

        // 权限控制 jiangdm 2022/7/13
        if (!\Yii::$app->view->params['is_system_admin'] && \Yii::$app->view->params['book_manage_read'] != 1 && \Yii::$app->view->params['book_manage_write'] != 1) {
            return $this->fail(\Yii::t('base', 'no_auth'));
        }



        $page = \Yii::$app->getRequest()->post('page', 1);
        $limit = \Yii::$app->getRequest()->post('limit', \Yii::$app->params['default_page_size']);


        // 根据企业id查询记录本编号规则
        $company_id = \Yii::$app->view->params['curr_company_id'];

        $book_prefix = CompanySettingModel::findOne(['company_id'=>$company_id,'key'=>'BOOK_PREFIX','status'=>1]);

        if(!empty($book_prefix)){
            $book_prefix_data = $book_prefix->value;
        }else{
            $book_prefix_data = '';
        }

        $data['book_prefix_data'] = $book_prefix_data;

        // 获取是否有编辑权限
        $data['isCanWrite'] = \Yii::$app->view->params['book_manage_write'] == 1;


        $file = $this->renderAjax('/setting/book_number_manage.php',$data);
        return $this->success([
            'contentHtml' => $file,
        ]);
    }

    /**
     * 企业设置
     * 提交记录本编号前缀
     * ?r=group-setting/book-number-submit
     */
    public function actionBookNumberSubmit(){
        // 权限控制 jiangdm 2022/7/13
        $authData = (new CompanyAuthServer())->getCompanyAuthByUserId($this->userinfo->id,1);
        $book_manage_write = isset($authData['company_feature']['edit_book_manage']) ? $authData['company_feature']['edit_book_manage'] : '0';
        if (!\Yii::$app->view->params['is_system_admin'] && $book_manage_write != 1) {
            return $this->fail(\Yii::t('base', 'no_auth'));
        }

        //获取企业的自定义配置
        $data = \Yii::$app->request->post();
        $company_id = $this->userinfo->current_company_id;

        $companySetting = new CompanySettingModel();
        $bookRecord = $companySetting->findOne(['company_id'=>$company_id,'key'=>'BOOK_PREFIX']);

        // 前缀为空则则让记录失效，不为空往数据库写入一条记录,
        if(empty($data['bookPrefix'])){

            if(!empty($bookRecord)){// 有记录status置0，值为空，无记录不处理
                $companySetting->updateAll(['status'=>0,'value'=>''],['company_id'=>$company_id,'key'=>'BOOK_PREFIX']);
            }

        }else{

            if(!empty($bookRecord)){// 有记录status置1，更新值，无记录则插入一条记录
                $companySetting->updateAll(['status'=>1,'value'=>$data['bookPrefix']],['company_id'=>$company_id,'key'=>'BOOK_PREFIX']);
            }else{

                $saveData=[
                    'company_id'=>$company_id,
                    'key'=>'BOOK_PREFIX',
                    'value'=>$data['bookPrefix'],
                    'status'=>1,
                    'remark'=>'记录本前缀值，值为空就是默认前缀，值不为空时自定义前缀("null"为自定义的空值)',
                ];
                $companySetting ->setAttributes($saveData);
                $companySetting ->save();
            }
        }

        /*添加日志*/
        $expAuthChangeLog = new ExpAuthChangeLog();
        $expAuthChangeLog->user_id = $this->userinfo->id;
        $expAuthChangeLog->operate_desc = '[企业设置]-[记录本编号前缀设置] 为: '.(empty($data['bookPrefix']) ? '默认值' : $data['bookPrefix']);
        $expAuthChangeLog->log_ip = $_SERVER[ 'REMOTE_ADDR' ] ;
        $role_arr=[];
        $system_master = 0;
        if(@getVar($this->userinfo->role_ids)){
            $role_arr = explode(',',$this->userinfo->role_ids);
        }
        if(in_array('1',$role_arr)){
            $system_master = 1;
        }
        $expAuthChangeLog->system_master = $system_master ;
        $expAuthChangeLog->save();
        /*添加日志完成*/

        return $this->success([]);

    }

    /**
     * 企业设置
     * 记录本档案查询
     * add by hkk 2019/9/24
     * ?r=group-setting /book-number-manage
     */
    public function actionBookFileManage(){

        $postData = \Yii::$app->getRequest()->post();

        $userDetails  = (new CenterInterface())->userDetailsByUserIds([$this->userinfo->id]);
        if(isset($userDetails[0]['role'])){
            $is_admin = in_array(1,$userDetails[0]['role'])?1:0;
        }else{
            $is_admin = 0;
        }
        //如果不是管理员验证是否是有相关权限
        if (!\Yii::$app->view->params['is_system_admin'] && \Yii::$app->view->params['book_manage_read'] != 1 && \Yii::$app->view->params['book_manage_write'] != 1) {
            return $this->fail(\Yii::t('base', 'no_auth'));
        }

        \Yii::info('渲染页面开始：');

        $page = \Yii::$app->request->post('page', 1);
        $limit = \Yii::$app->request->post('limit', \Yii::$app->params['default_page_size']);

        $where['user_id'] = \Yii::$app->request->post('user_ids', []);
        $where['group_id'] = \Yii::$app->request->post('group_ids', []);
        $where['status'] = \Yii::$app->request->post('book_status', []);


        //查询该企业下所有用户,和所有鹰群，用于用户筛选
        $company_id = \Yii::$app->view->params['curr_company_id'];
        if(!$company_id){
            return $this->fail(\Yii::t('base', 'select_user'));
        }
        $userList = (new CenterInterface())->getVisibleUsers($this->userinfo->id, 0, [0, 1, 2]);
        $groupList = (new CenterInterface())->getGroupsListByCompanyId($company_id, '', true);

        // 获取查询的数据
        $where['user_id'] = !empty($where['user_id']) ? $where['user_id'] : array_column($userList, 'user_id');
        $where['group_id'] = !empty($where['group_id']) ? $where['group_id'] : array_column($groupList, 'id');
        $bookData = (new GroupSettingServer())->listBookFileView($where, $limit, $page);

        $data['all_user'] = $userList;
        $data['group_list'] = $groupList;
        $data['book_list'] = $bookData['book_list'];
        $data['totalCount'] = $bookData['totalCount'];
        $data['params'] = $where;
        $data['limit'] = $limit;

        $data['defalut_user_ids']=[];
        $data['defalut_group_ids']=[];
        $data['user_id']= $this->userinfo->id;


         // 不传page参数相当于第一次渲染，更新整个页面
        if (!isset($postData['page'])) {
            $file = $this->renderAjax('/setting/book_file_manage.php', $data);
            return $this->success(['contentHtml' => $file]);
        }

        // 传page参数只更新表格数据
        $data['page'] = $postData['page'];
        $file = $this->renderAjax('/setting/book_file_manage_page.php', $data);
        return $this->success(['contentHtml' => $file]);

    }

    /**
     * @Notes: 记录本档案导出
     * @return \common\controllers\json|void
     * @throws \PHPExcel_Exception
     * @throws \PHPExcel_Reader_Exception
     * @throws \PHPExcel_Writer_Exception
     * @throws yii\web\NotFoundHttpException
     * @author: tianyang
     * @Time: 2023/6/7 19:07
     */
    public function actionExportBookFileManage() {
        //bug 1329
        /*$page = \Yii::$app->request->post('page', 1);
        $limit = \Yii::$app->request->post('limit', \Yii::$app->params['default_page_size']);*/
        $where['user_id'] = \Yii::$app->request->post('user_ids', []);
        $where['group_id'] = \Yii::$app->request->post('group_ids', []);
        $where['status'] = \Yii::$app->request->post('book_status', []);

        //查询该企业下所有用户,和所有鹰群，用于用户筛选
        $company_id = \Yii::$app->view->params['curr_company_id'];
        if(!$company_id){
            return $this->fail(\Yii::t('base', 'select_user'));
        }
        $userList = (new CenterInterface())->getVisibleUsers($this->userinfo->id, 0, [0, 1, 2]);
        $groupList = (new CenterInterface())->getGroupsListByCompanyId($company_id, '', true);

        // 获取查询的数据
        $where['user_id'] = !empty($where['user_id']) ? $where['user_id'] : array_column($userList, 'user_id');
        $where['group_id'] = !empty($where['group_id']) ? $where['group_id'] : array_column($groupList, 'id');
        $bookData = (new GroupSettingServer())->listBookFileView($where);
        $bookDataList = @getVar($bookData['book_list'], []);
        $exportFields = [
            'user_book_number' => Yii::t('base', 'user_book_number'),
            'book_code'        => Yii::t('base', 'book_code'),
            'book_name'        => Yii::t('base', 'book_name'),
            'project_name'     => Yii::t('base', 'project_name'),
            'group_name'       => Yii::t('base', 'group_name'),
            'page_number'      => Yii::t('base', 'page_number'),
            'page_use'         => Yii::t('base', 'page_use'),
            'exp_last_time'    => Yii::t('base', 'exp_last_time'),
            'book_create_time' => Yii::t('base', 'book_create_time'),
            'status'           => Yii::t('base', 'status'),
        ];

        // 加载 PHPExcel
        require_once \Yii::$app->vendorPath . '/Excel/PHPExcel.php';
        require_once \Yii::$app->vendorPath . '/Excel/PHPExcel' . DIRECTORY_SEPARATOR . 'IOFactory.php';

        $objPHPExcel = new PHPExcel();
        $objPHPExcel->getProperties()->setTitle("export")->setDescription("none");

        // 选择当前活动的工作表
        $objPHPExcel->setActiveSheetIndex(0);
        $worksheet = $objPHPExcel->getActiveSheet();

        // 写入标题行
        // 缓存坐标计算结果
        $exportFieldsIndexMap = [];
        $exportServer = new ExportServer();
        $colIndex = 0;
        foreach ($exportFields as $caseKey => $title) {
            $colIndexStr = $exportServer -> IntToChr($colIndex);
            $exportFieldsIndexMap[$caseKey] = $colIndexStr;
            // 将单元格样式设置为居中对齐
            $worksheet->getStyle($colIndexStr . '1')->getAlignment()
                ->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER)
                ->setVertical(\PHPExcel_Style_Alignment::VERTICAL_CENTER);
            $worksheet->setCellValue($colIndexStr . '1', $title);
            $colIndex++;
        }

        // 写入内容
        $i = 0;
        foreach ($bookDataList as $rol => $rolData) {
            $cellNum = $i + 2;
            $i++;
            foreach ($exportFields as $caseKey => $title) {
                $colIndex = $exportFieldsIndexMap[$caseKey];
                $rolIndex = $cellNum;
                $cellVal = '';
                switch ($caseKey) {
                    case 'user_book_number':
                        $cellVal = $rolData['user_name'] . '('.$rolData['book_number'] .')';
                        break;
                    case 'book_code':
                        $cellVal = $rolData['book_code'];
                        break;
                    case 'book_name':
                        $cellVal = $rolData['book_name'];
                        break;
                    case 'project_name':
                        $cellVal = $rolData['project_name'];
                        break;
                    case 'group_name':
                        $cellVal = $rolData['group_name'];
                        break;
                    case 'page_number':
                        $cellVal = $rolData['experiment_number'] . '/500';
                        break;
                    case 'page_use':
                        $cellVal = number_format(100 * intval($rolData['experiment_number']) / 500, 2) . '%';
                        break;
                    case 'exp_last_time':
                        $cellVal = $rolData['last_exp_create_time'];
                        break;
                    case 'book_create_time':
                        $cellVal = $rolData['book_create_time'];
                        break;
                    case 'status':
                        $cellVal = !empty($rolData['status'])
                            ? Yii::t('base', 'normal')
                            : Yii::t('base', 'recycle_bin');
                        break;
                    default:
                        $cellVal = @getVar($rolData[$caseKey]);
                        break;
                }
                // 将单元格样式设置为居中对齐
                $worksheet->getStyle($colIndex . $rolIndex)->getAlignment()
                    ->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER)
                    ->setVertical(\PHPExcel_Style_Alignment::VERTICAL_CENTER);
                // 文本格式
                $worksheet->setCellValueExplicit($colIndex . $cellNum, $cellVal, \PHPExcel_Cell_DataType::TYPE_STRING);
            }
        }
        $objWriter = \PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel2007');

        $filePathname = uniqid('book_file_manage_') . '.xlsx';
        $filePath = \Yii::getAlias('@filepath') . DS . $filePathname;
        $encryption = new Encryption();$fileName = Yii::t('base', 'export') . Yii::t('base', 'book_manage_file') . '.xlsx';
        if ($encryption->isHuatuEncrypt()) {
            $tmpFile = \Yii::getAlias('@svgpath') . DS . $filePathname;
            $objWriter->save($tmpFile);
            $filePath = $encryption->encryptFileApi($filePath, $tmpFile); // 调用加密接口加密
            @unlink($tmpFile);// 删除临时文件
        } else {
            $objWriter->save($filePath);
            $filePath = $encryption->encryptFileApi($filePath); // 调用加密接口加密
        }
        return $this->success([
            'file_path_name' => $filePathname,
            'file_name' => $fileName
        ]);
    }

    /**
     * 企业设置
     * ?r=group-setting/sys-log
     */
    public function actionSysLog() {
        // 权限控制 jiangdm 2022/7/13
        if (!\Yii::$app->view->params['is_system_admin'] && \Yii::$app->view->params['login_log_read'] != 1) {
            return $this->fail(\Yii::t('base', 'no_auth'));
        }

        $page = \Yii::$app->getRequest()->post('page', 1);
        $limit = \Yii::$app->getRequest()->post('limit', \Yii::$app->params['default_page_size']);

        $params['start_time'] = \Yii::$app->getRequest()->post('start_time', '');
        $params['end_time'] = \Yii::$app->getRequest()->post('end_time', '');
        $params['user_id'] = \Yii::$app->getRequest()->post('user_id', 0);

        // 获取公司所有人员
        $companyUsers = (new CenterInterface())->getUserListByCompanyId(\Yii::$app->view->params['curr_company_id'], 1);
        $userList = yii\helpers\ArrayHelper::index($companyUsers['list'], 'id');

        //print_r($params);exit;
        // 获取登录日志
        $loginData = (new CenterInterface())->userLoginLogList($page, $limit, $params);

        $data['all_user'] = $userList;
        $data['login_list'] = isset($loginData['data'])?$loginData['data']:[];
        $data['limit'] = $limit;
        $data['totalCount'] =  isset($loginData['count'])?$loginData['count']:0;;
        $data['params'] = $params;

        //$file = $this->renderAjax('/setting/use_static.php',$data);
        $needUpdateAllPage = \Yii::$app->request->post('needUpdateAllPage', 1); // add by hkk 2020/6/23
        // 第一次渲染，更新整个页面
        if (!empty($needUpdateAllPage)) {
            $file = $this->renderAjax('/log/sys_log.php', $data);
            return $this->success(['contentHtml' => $file]);
        }

        // 传page参数只更新表格数据
        $file = $this->renderAjax('/log/sys_log_page.php', $data);
        return $this->success(['contentHtml' => $file]);


    }

    /**
     * 实验分享日志
     * ?r=group-setting/sys-exp-share-log
     */
    public function actionSysExpShareLog(){
        // 权限控制 jiangdm 2022/7/13
        if (!\Yii::$app->view->params['is_system_admin'] && \Yii::$app->view->params['share_log_read'] != 1) {
            return $this->fail(\Yii::t('base', 'no_auth'));
        }



        \Yii::info('渲染页面开始：');
        $page = \Yii::$app->request->post('page', 1);
        $limit = \Yii::$app->request->post('limit', \Yii::$app->params['default_page_size']);
        $where['user_id'] = \Yii::$app->request->post('user_id', 0);
        $where['start_time'] = \Yii::$app->request->post('start_time', '');
        $where['end_time'] = \Yii::$app->request->post('end_time', '');
        $where['project_id'] = \Yii::$app->request->post('project_id', 0);
        $where['group_id'] = \Yii::$app->request->post('group_id', 0);
        $where['share_type'] = \Yii::$app->request->post('share_type', 0);
        $where['exp_num'] = \Yii::$app->request->post('exp_num', 0);


        //查询用户
        $company_id = \Yii::$app->view->params['curr_company_id'];
        if(!$company_id){
            return $this->fail(\Yii::t('base', 'select_user'));
        }
        $companyUsers = (new CenterInterface())->getUserListByCompanyId(\Yii::$app->view->params['curr_company_id'], 1);
        $userList = yii\helpers\ArrayHelper::index($companyUsers['list'], 'id');
        $data['all_user'] = $userList;

        //获取鹰群
        $groupList = (new CenterInterface())->getGroupsListByCompanyId($company_id, '', true);

        //获取企业的所有项目
        $projectList = (new PMInterface())->getCompanyProjects(['only_active' => 0]);

        $userData = (new GroupSettingServer())->listExpShareLog($where, $limit, $page);
        $data['user_list'] = $userData['user_list'];
        $data['limit'] = $limit;
        $data['totalCount'] = $userData['totalCount'];

        $data['params'] = $where;
        $data['group_list'] = $groupList;
        $data['project_list'] = $projectList;

        $data['all_user'] = yii\helpers\ArrayHelper::index($data['all_user'], 'user_id');
        foreach ($data['user_list'] as $key => $value) {
            if (array_key_exists($value['user_id'], $data['all_user'])) {
                $data['user_list'][$key]['real_name'] = $data['all_user'][$value['user_id']]['real_name'];
                $data['user_list'][$key]['name'] = $data['all_user'][$value['user_id']]['name'];
            }
        }

        $needUpdateAllPage = \Yii::$app->request->post('needUpdateAllPage', 1); // add by hkk 2020/6/23
        // 第一次渲染，更新整个页面
        if (!empty($needUpdateAllPage)) {
            $file = $this->renderAjax('/log/sys_exp_share_log.php', $data);
            return $this->success(['contentHtml' => $file]);
        }

        // 传page参数只更新表格数据
        $file = $this->renderAjax('/log/sys_exp_share_log_page.php', $data);
        return $this->success(['contentHtml' => $file]);

    }

    /**
     * 实验分享日志
     * ?r=group-setting/sys-exp-share-log
     */
    public function actionSysExpOpenLog(){
        // 权限控制 jiangdm 2022/7/13
        if (!\Yii::$app->view->params['is_system_admin'] && \Yii::$app->view->params['view_log_read'] != 1) {
            return $this->fail(\Yii::t('base', 'no_auth'));
        }

        \Yii::info('渲染页面开始：');

        $page = \Yii::$app->request->post('page', 1);
        $limit = \Yii::$app->request->post('limit', \Yii::$app->params['default_page_size']);

        $where['user_id'] = \Yii::$app->request->post('user_id', 0);
        $where['start_time'] = \Yii::$app->request->post('start_time', '');
        $where['end_time'] = \Yii::$app->request->post('end_time', '');

        $where['project_id'] = \Yii::$app->request->post('project_id', 0);
        $where['group_id'] = \Yii::$app->request->post('group_id', 0);
        $where['exp_num'] = \Yii::$app->request->post('exp_num', 0);

        //查询该企业下用户
        $company_id = \Yii::$app->view->params['curr_company_id'];
        if(!$company_id){
            return $this->fail(\Yii::t('base', 'select_user'));
        }

        $companyUsers = (new CenterInterface())->getUserListByCompanyId(\Yii::$app->view->params['curr_company_id'], 1);
        $userList = yii\helpers\ArrayHelper::index($companyUsers['list'], 'id');
        $data['all_user'] = $userList;

        //获取企业的鹰群
        $groupList = (new CenterInterface())->getGroupsListByCompanyId($company_id, '', true);

        // 获取企业的所有项目
        $projectList = (new PMInterface())->getCompanyProjects(['only_active' => 0]);

        $userData = (new GroupSettingServer())->listExpOpenLog($where, $limit, $page);

        $data['user_list'] = $userData['user_list'];
        $data['limit'] = $limit;
        $data['totalCount'] = $userData['totalCount'];
        $data['params'] = $where;
        $data['group_list'] = $groupList;
        $data['project_list'] = $projectList;
        //$file = $this->renderAjax('/setting/sys_exp_open_log.php',$data);
        //$file = $this->renderAjax('/setting/use_static.php',$data);

        $data['all_user'] = yii\helpers\ArrayHelper::index($data['all_user'], 'user_id');
        foreach ($data['user_list'] as $key => $value) {
            if (array_key_exists($value['user_id'], $data['all_user'])) {
                $data['user_list'][$key]['real_name'] = $data['all_user'][$value['user_id']]['real_name'];
                $data['user_list'][$key]['name'] = $data['all_user'][$value['user_id']]['name'];
            }
        }

        $needUpdateAllPage = \Yii::$app->request->post('needUpdateAllPage', 1); // add by hkk 2020/6/23
        // 第一次渲染，更新整个页面
        if (!empty($needUpdateAllPage)) {
            $file = $this->renderAjax('/log/sys_exp_open_log.php', $data);
            return $this->success(['contentHtml' => $file]);
        }

        // 传page参数只更新表格数据
        $file = $this->renderAjax('/log/sys_exp_open_log_page.php', $data);
        return $this->success(['contentHtml' => $file]);


    }

    /**
     * 实验分享日志
     * ?r=group-setting/sys-exp-share-log
     */
    public function actionSysFileExportLog(){
        // 权限控制 jiangdm 2022/7/13
        if (!\Yii::$app->view->params['is_system_admin'] && \Yii::$app->view->params['export_log_read'] != 1) {
            return $this->fail(\Yii::t('base', 'no_auth'));
        }


        \Yii::info('渲染页面开始：');
        $page = \Yii::$app->request->post('page', 1);
        $limit = \Yii::$app->request->post('limit', \Yii::$app->params['default_page_size']);

        $where['user_id'] = \Yii::$app->request->post('user_id', 0);
        $where['start_time'] = \Yii::$app->request->post('start_time', '');
        $where['end_time'] = \Yii::$app->request->post('end_time', '');

        $where['type'] = \Yii::$app->request->post('type', 0);
        $where['project_id'] = \Yii::$app->request->post('project_id', 0);
        $where['group_id'] = \Yii::$app->request->post('group_id', 0);
        $where['exp_num'] = \Yii::$app->request->post('exp_num', 0);

        //查询该企业下用户
        $company_id = \Yii::$app->view->params['curr_company_id'];
        if(!$company_id){
            return $this->fail(\Yii::t('base', 'select_user'));
        }

        $companyUsers = (new CenterInterface())->getUserListByCompanyId(\Yii::$app->view->params['curr_company_id'], 1);
        $userList = yii\helpers\ArrayHelper::index($companyUsers['list'], 'id');
        $data['all_user'] = $userList;
        //获取企业的鹰群
        $groupList = (new CenterInterface())->getGroupsListByCompanyId($company_id, '', true);

        //获取企业的所有项目
        $projectList = (new PMInterface())->getCompanyProjects(['only_active' => 0]);

        $userData = (new GroupSettingServer())->listFileExportLog($where, $limit, $page);

        $data['user_list'] = $userData['user_list'];
        $data['limit'] = $limit;
        $data['totalCount'] = $userData['totalCount'];
        $data['params'] = $where;
        $data['group_list'] = $groupList;
        $data['project_list'] = $projectList;

        $data['all_user'] = yii\helpers\ArrayHelper::index($data['all_user'], 'user_id');
        foreach ($data['user_list'] as $key => $value) {
            if (array_key_exists($value['user_id'], $data['all_user'])) {
                $data['user_list'][$key]['real_name'] = $data['all_user'][$value['user_id']]['real_name'];
                $data['user_list'][$key]['name'] = $data['all_user'][$value['user_id']]['name'];
            }
        }

        $needUpdateAllPage = \Yii::$app->request->post('needUpdateAllPage', 1); // add by hkk 2020/6/23
        // 第一次渲染，更新整个页面
        if (!empty($needUpdateAllPage)) {
            $file = $this->renderAjax('/log/sys_file_export_log.php', $data);
            return $this->success(['contentHtml' => $file]);
        }

        // 传page参数只更新表格数据
        $file = $this->renderAjax('/log/sys_file_export_log_page.php', $data);
        return $this->success(['contentHtml' => $file]);


    }

    /**
     * 实验分享日志
     * ?r=group-setting/sys-exp-share-log
     */
    public function actionSysAuthChangeLog(){
        // 权限控制 jiangdm 2022/7/13
        if (!\Yii::$app->view->params['is_system_admin'] && \Yii::$app->view->params['set_log_read'] != 1) {
            return $this->fail(\Yii::t('base', 'no_auth'));
        }

        \Yii::info('渲染页面开始：');
        $page = \Yii::$app->request->post('page', 1);
        $limit = \Yii::$app->request->post('limit', \Yii::$app->params['default_page_size']);

        $where['user_id'] = \Yii::$app->request->post('user_id', 0);
        $where['start_time'] = \Yii::$app->request->post('start_time', '');
        $where['end_time'] = \Yii::$app->request->post('end_time', '');
        $where['operate_desc'] = \Yii::$app->request->post('operate_desc', '');
        $where['role_id'] = \Yii::$app->request->post('role_id', 0);

        //查询该企业下可见用户
        $company_id = \Yii::$app->view->params['curr_company_id'];
        if(!$company_id){
            return $this->fail(\Yii::t('base', 'select_user'));
        }

        $companyUsers = (new CenterInterface())->getUserListByCompanyId(\Yii::$app->view->params['curr_company_id'], 1);
        $userList = yii\helpers\ArrayHelper::index($companyUsers['list'], 'id');
        $data['all_user'] = $userList;

        // 获取所有鹰群
        $groupList = (new CenterInterface())->getGroupsListByCompanyId($company_id);
        $groupMasterArr = array_column($groupList,'master_id');


        // 获取所有eln管理员
        $elnManageList = (new GroupSettingServer())->getElnManageList();
        $groupManageArr = array_column($elnManageList,'user_id');

        $userData = (new GroupSettingServer())->listAuthChangeLog($where, $limit, $page);

        $from_user = yii\helpers\ArrayHelper::index($data['all_user'], 'user_id');

        foreach ($userData['user_list'] as $key => $value) {
            $userData['user_list'][$key]['user_name'] = (isset($from_user[$value['user_id']]['name']) ? $from_user[$value['user_id']]['name'] : isset($from_user[$value['user_id']]['real_name'])?$from_user[$value['user_id']]['real_name']:'');
            $userData['user_list'][$key]['is_group_master'] = in_array($value['user_id'],$groupMasterArr)?1:0;
            $userData['user_list'][$key]['is_group_manage'] = in_array($value['user_id'],$groupManageArr)?1:0;
        }
        //print_r($userData);exit;
        $data['user_list'] = $userData['user_list'];
        $data['limit'] = $limit;
        $data['totalCount'] = $userData['totalCount'];
        $data['params'] = $where;

        $data['all_user'] = yii\helpers\ArrayHelper::index($data['all_user'], 'user_id');
        foreach ($data['user_list'] as $key => $value) {
            if (array_key_exists($value['user_id'], $data['all_user'])) {
                $data['user_list'][$key]['real_name'] = $data['all_user'][$value['user_id']]['real_name'];
                $data['user_list'][$key]['name'] = $data['all_user'][$value['user_id']]['name'];
            }
        }

        $needUpdateAllPage = \Yii::$app->request->post('needUpdateAllPage', 1); // add by hkk 2020/6/23
        // 第一次渲染，更新整个页面
        if (!empty($needUpdateAllPage)) {
            $file = $this->renderAjax('/log/sys_auth_change_log.php', $data);
            return $this->success(['contentHtml' => $file]);
        }

        // 传page参数只更新表格数据
        $file = $this->renderAjax('/log/sys_auth_change_log_page.php', $data);
        return $this->success(['contentHtml' => $file]);
    }

    /**
     * @Notes: 导出系统日志数据
     * @return \common\controllers\json
     * @throws \PHPExcel_Exception
     * @throws \PHPExcel_Reader_Exception
     * @throws \PHPExcel_Writer_Exception
     * <AUTHOR>
     * @DateTime: 2024/1/10 15:17
     */
    public function actionSysLogExportBook() {
        // 权限控制
        if (!\Yii::$app->view->params['is_system_admin'] && \Yii::$app->view->params['login_log_read'] != 1) {
            return $this->fail(\Yii::t('base', 'no_auth'));
        }
        $page = \Yii::$app->getRequest()->post('page', 1);
        $limit = \Yii::$app->getRequest()->post('limit', \Yii::$app->params['default_page_size']);
        $postData = Yii::$app->getRequest()->post();
        $exportType = $postData['exportType'];
        $filterCondition = $postData['filterCondition'];
        $groupSettingServer = new GroupSettingServer();
        $exportFields = $groupSettingServer->getExportLogFields($exportType);
        // bug#1316, 只按时间, 不分页
        $exportData = $groupSettingServer->getExportLogData($exportType, null, null, $filterCondition);
        $fileInfo = $groupSettingServer->exportLogFile($exportType, $exportFields, $exportData);
        $filePathname = $fileInfo['filePathname'];
        $fileName = $fileInfo['fileName'];
        return $this->success([
            'file_path_name' => $filePathname,
            'file_name' => $fileName
        ]);
    }

    /**
     *
     * ?r=group-setting/struct-data
     */
    public function actionStructData(){
        //渲染页面

        \Yii::info('渲染页面开始：');
        $page = \Yii::$app->request->post('page', 1);
        $limit = \Yii::$app->request->post('limit', \Yii::$app->params['default_page_size']);
        $orderBy = \Yii::$app->request->post('order_by', 'id');
        $orderType = \Yii::$app->request->post('order_type', 'asc');


        $where['start_time'] = \Yii::$app->request->post('start_time', '');
        $where['end_time'] = \Yii::$app->request->post('end_time', '');

        $where['exp_num'] =explode(';',\Yii::$app->request->post('exp_num', ''));


        $data['params'] = $where;
        $file = $this->renderAjax('/setting/struct_data.php',$data);


        return $this->success([
            'contentHtml' => $file,
            'exp_num' => join(',',explode(';',\Yii::$app->request->post('exp_num', ''))),
        ]);
    }

    /**
     * Notes: 结构化数据的构造，标题只按模板内容展示
     * Author: hkk modified by hkk 2021/3/10
     * Date: 2021/3/8 17:39
     * @return \common\controllers\json
     */
    public function actionGetStructData(){


        $data = \Yii::$app->request->post();

        // 1 验证是否有实验编号不存在 是否同一模板
        $exp_nums='';
        if (isset($data['exp_nums']) && $data['exp_nums'] != '') {
            $exp_nums = array_filter(explode(',', $data['exp_nums']));
        }
        if (empty($exp_nums)) {
            return $this->fail(\Yii::t('common', 'no exp number'));
        }
        $i = 0;
        $idArr = []; // 实验id数组
        $tmpArr = [];
        $differ_array = [];
        $exist_differ = false;
        $base_temp_id = 0;

        foreach ($exp_nums as $item) {

            // 解析实验编号
            $pos = strrpos($item, '-');
            if (!$pos) {
                return $this->fail($item . \Yii::t('common', 'no exp'));
            } else {
                $bookCode = substr($item, 0, $pos);
                $expPage = substr($item, $pos + 1);
            }

            // 查找实验
            $exp = ExperimentModel::find()->select(['EXP.id', 'EXP.user_id', 'EXP.template_id'])->from(ExperimentModel::tableName() . ' AS EXP')
                ->innerJoin(BookModel::tableName() . ' AS BOK', 'BOK.id=EXP.book_id')
                ->where([
                    'BOK.book_code' => $bookCode,
                    'EXP.exp_page' => $expPage
                ])->one();

            if (empty($exp)) {
                return $this->fail($item . \Yii::t('common', 'no exp'));
            }

            if (empty($exp['template_id'])) {
                return $this->fail($item . \Yii::t('common', 'no exp template'));
            }

            if ($i == 0) {
                $base_temp_id = $exp['template_id'];
            }

            if ($base_temp_id != $exp['template_id']) {
                $exist_differ = true;
                array_push($differ_array, $item);
            }

            $i++;

            array_push($tmpArr, $exp['template_id']);
            array_push($idArr, $exp['id']);
        }
        if ($exist_differ) {
            return $this->fail(\Yii::t('common', 'number different'),'',$differ_array);
        }


        // 2 根据模板ID获取结构化数据字段并解析
        $structureDataFiled = (new GroupSettingServer())->getStructureDataFiled($base_temp_id);
        if (empty($structureDataFiled)) {
            return $this->fail(\Yii::t('common', 'no structure data'));
        }

        // 2.1 构造基础信息标题数组和数值索引 (包括实验名称，关键字，自定义项，页码始终存在)
        $baseRelayArray = [];
        $baseTitleArray = [Yii::t('base', 'pre_order_note_page')]; // 页码始终存在
        if (!empty($structureDataFiled['base'])) {
            if (isset($structureDataFiled['base']['title']) && $structureDataFiled['base']['title'] == 1) {
                $baseTitleArray[] = Yii::t('views/set_require', 'title'); // 实验标题
                $baseRelayArray['exp_title'] = 1;
            }
            if (isset($structureDataFiled['base']['keyword']) && $structureDataFiled['base']['keyword'] == 1) {
                $baseTitleArray[] = Yii::t('views/set_require', 'keyword');//关键字
                $baseRelayArray['keyword'] = 1;
            }
            if (isset($structureDataFiled['base']['project']) && $structureDataFiled['base']['project'] == 1) {
                $baseTitleArray[] = Yii::t('base', 'project_name');//项目
                $baseRelayArray['project'] = 1;
            }
            if (isset($structureDataFiled['base']['task']) && $structureDataFiled['base']['task'] == 1) {
                $baseTitleArray[] = Yii::t('base', 'task_name');//任务
                $baseRelayArray['task'] = 1;
            }

            if (!empty($structureDataFiled['base']['define_item'])) {
                foreach ($structureDataFiled['base']['define_item'] as $index => $item) {
                    $baseTitleArray[] = $item;
                    $baseRelayArray['define_item'][] = $item;
                }
            }
        }


        // 2.2 构造inDraw物料表标题数组和数值索引
        $inDrawRelayArray = [];
        $inDrawTitleArray = [];
        if(!empty($structureDataFiled['chem']) && !empty($structureDataFiled['chem']['relay_id'])){
            foreach($structureDataFiled['chem'] as $filedKey => $isStructure){
                if ($filedKey == 'relay_id' || $isStructure == 0) {
                    continue;
                }
                $inDrawRelayArray[$structureDataFiled['chem']['relay_id']][] = $filedKey;
                $filedTitle =  str_replace('batchno','batch_no',$filedKey);
                $filedTitle =  str_replace('salteq','salt_eq',$filedTitle);
                $filedTitle =  ucwords(str_replace('_',' ',$filedTitle));
                $inDrawTitleArray[] = $filedTitle; // 转换标题格式 reactant_name=> Reactant Name
            }
        }


        // 2.3 构造自定义表格模块的标题数组和数值索引 (包括参考文献，材料与仪器，自定义表格模块)
        $defineRelayArray = [];
        $defineTitleArray = [];
        if(!empty($structureDataFiled['define'])){
            foreach($structureDataFiled['define'] as $key => $defineModule){
                for ($i = 1; $i <= 21; $i++) {
                    if (isset($defineModule['define_field' . $i]) && $defineModule['define_field' . $i] == 1) {
                        $defineTitleArray[] = $defineModule['field' . $i];
                        $defineRelayArray[$defineModule['relay_id']][] = $i; // 用于后面获取对应实验的结构化内容
                    }
                }
            }
        }


        // 2.4 构造inTable模块的标题数组和数值索引
        $inTableRelayArray = [];
        $inTableTitleArray = [];
        if (!empty($structureDataFiled['in_table'])) {
            foreach ($structureDataFiled['in_table'] as $key => $inTableData) {
                foreach ($inTableData as $sheetIndex => $structData) {
                    if (isset($structData) && !empty($structData) && $sheetIndex !== 'relay_id') {
                        foreach ($structData as $inTableStructure) { // 每个sheet分块考虑
                            $titleArr = explode(",", $inTableStructure['titles']); // modified by hkk 2021/2/3  兼容多标题模式
                            $number = 0;
                            foreach ($inTableStructure['areaArr'] as $key => $area) {
                                $indexString = '(' . Yii::t('base', 'row') . ($area[0] + 1) . Yii::t('base', 'column') . ($area[1] + 1) . ')';
                                if ($key >= count($titleArr)) { // 有标题用标题无标题用坐标
                                    $title = $titleArr[count($titleArr) - 1] ? $titleArr[count($titleArr) - 1] : $indexString;
                                } else {
                                    $title = $titleArr[$number] ? $titleArr[$number] : $indexString;
                                }
                                $number += 1;
                                $inTableTitleArray[] = $title;
                                $inTableRelayArray[$inTableData['relay_id']][] = [$sheetIndex, $area[0], $area[1]]; // 用于后面获取对应实验的结构化内容
                            }
                        }
                    }
                }
            }
        }


        // 2.5 合并各模块数据用于个实验分析
        $moduleRelayArray =  $inDrawRelayArray + $defineRelayArray + $inTableRelayArray ;
        $resultTitleArray = array_merge($baseTitleArray,$inDrawTitleArray,$defineTitleArray,$inTableTitleArray);


        // 3 根据实验id和权限去查询对应的实验的模块数据，没有就填空数据
        $expValueArray = [];
        $allBaseData = (new ExperimentModel())->getBaseDataByIds($idArr);
        $myGroupList = (new CenterInterface())->elnGroupListByUserId($this->userinfo->id);
        $allCoauthorRelayIds = (new ExperimentServer())->getCoauthorRelayIds($idArr,$this->userinfo->id);
        $projectAndTaskArr = (new PMInterface())->listAllProjectTaskName();
        $projects = @getVar($projectAndTaskArr['projects'], []);
        $tasks = @getVar($projectAndTaskArr['tasks'], []);
        foreach($idArr as $exp_id){

            // 3.1 获取基础数据的结构化数据
            $baseStructureData = [];
            $baseData = $allBaseData[$exp_id];
            if (!empty($baseData)) {
                $baseStructureData[] = $baseData['book_code']. '-' . sprintf('%03s', $baseData['exp_page']);// exp_all_code

                if (isset($baseRelayArray['exp_title']) && $baseRelayArray['exp_title'] == 1) {
                    $baseStructureData[] = $baseData['title'];
                }
                if (isset($baseRelayArray['keyword']) && $baseRelayArray['keyword'] == 1) {
                    $baseStructureData[] = $baseData['keywords'];
                }
                if (isset($baseRelayArray['project']) && $baseRelayArray['project'] == 1) {
                    $baseStructureData[] = @getVar($projects[$baseData['project_id']]);
                }
                if (isset($baseRelayArray['task']) && $baseRelayArray['task'] == 1) {
                    $baseStructureData[] = @getVar($tasks[$baseData['task_id']]);
                }
                if (isset($baseRelayArray['define_item']) && count($baseRelayArray['define_item']) >= 1) {
                    $define_item_arr = json_decode($baseData['define_item'], true);
                    foreach ($baseRelayArray['define_item'] as $index => $item){
                        $match = false;
                        foreach ($define_item_arr as $index2 => $item2) {
                            if ($item == $item2['title']) { // 根据名称匹配
                                $baseStructureData[] = $item2['value'];
                                $match = true;
                                break;
                            }
                        }
                        if (!$match) {
                            $baseStructureData[] = ''; // 默认空元素确保个数一直
                        }
                    }
                }

            }

            // 3.1 获取各个模块的结构化数据
            $moduleDataArr = [];
            $moduleDataArr['base'] = $baseStructureData; // 先合并基础数据和模块数据
            $coauthorRelayIds = !empty($allCoauthorRelayIds[$exp_id]) ? $allCoauthorRelayIds[$exp_id] : [];
            $checkRange = (new CompanyAuthServer())->getCheckRange($userId);
            $accessModulesRes = (new ExperimentServer())->getStructureAccessModules($exp_id, $this->userinfo->id, $myGroupList, $baseData, $coauthorRelayIds, $checkRange);
            $modules = $accessModulesRes['modules'];
            if ($accessModulesRes['type'] === 'all') {
                $modules = ExperimentRelayModel::find()->where([
                    'experiment_id' => $expId,
                    'status' => 1
                ])->orderBy('class ASC')->asArray()->all();
            }

            foreach ($moduleRelayArray as $relayId => $fieldIdArray) {
                $moduleDataArr[$relayId] = [];
                $match = false;
                foreach ($modules as $module) {
                    if ($module['temp_relay_id'] == $relayId) {
                        switch ($module['component_id']) {
                            case \Yii::$app->params['component']['define_table']: // 包括参考文献，材料与仪器，自定义表格模块

                                $dataResult = (new DefineTableModuleServer())->getData($module['id'])['data'];
                                foreach ($fieldIdArray as $index => $fieldId) {
                                    $fieldString = '';
                                    foreach ($dataResult['field_value'] as $valueArray) {
                                        $fieldString = ( isset($valueArray['data' . $fieldId]) && !emptyExclude0($valueArray['data' . $fieldId]) ? $valueArray['data' . $fieldId] : ''). '||' .$fieldString;
                                    }

                                    $moduleDataArr[$relayId][$index] = rtrim($fieldString,"||");
                                }
                                $match = true;
                                break;
                            case \Yii::$app->params['component']['xsheet']:
                                $dataResult = (new InTableServer())->getDataByRelayId($module['id']);
                                $inTableData = json_decode($dataResult['data'][0]['data'],true);
                                foreach ($inTableData as $sheetIndex => $inTable) {
                                    foreach ($fieldIdArray as $index => $area) {
                                        if ($sheetIndex == $area[0]) {
                                            $value_str =
                                                isset($inTable['data']['rows'][$area[1]]['cells'][$area[2]]['dataFormatVal'])?
                                                $inTable['data']['rows'][$area[1]]['cells'][$area[2]]['dataFormatVal']:
                                                    (isset($inTable['data']['rows'][$area[1]]['cells'][$area[2]]['text']) ?
                                                        $inTable['data']['rows'][$area[1]]['cells'][$area[2]]['text'] : "");//单元格有格式就用格式化后的数据
                                            if ($value_str && substr($value_str, 0, 1) == '=') {
                                                $value_str = isset($inTable['data']['rows'][$area[1]]['cells'][$area[2]]['formulaVal']) ? $inTable['data']['rows'][$area[1]]['cells'][$area[2]]['formulaVal'] : $value_str;
                                            }
                                            $moduleDataArr[$relayId][$index] = $value_str;
                                        }
                                    }
                                    $match = true;
                                }
                                break;
                            case \Yii::$app->params['component']['chem']:
                                $dataResult = (new IndrawModuleServer())->getData($module['id']);
                                foreach ($fieldIdArray as $index => $fieldName) {

                                    $valueString = '';
                                    $type =  explode("_",$fieldName)[0];
                                    if ($type == 'reactant') {
                                        foreach ($dataResult['data']['substrates_data'] as $key2 => $value2) {
                                            if ($fieldName == 'reactant_name') {
                                                $valueString = (!emptyExclude0($value2['substrates_name']) ? $value2['substrates_name'] : '') . '||' . $valueString;
                                            }
                                            if ($fieldName == 'reactant_batchno') {
                                                $valueString = (!emptyExclude0($value2['substrates_batch_num']) ? $value2['substrates_batch_num'] : '') . '||' . $valueString;
                                            }
                                            if ($fieldName == 'reactant_mass') {
                                                $valueString = (!emptyExclude0($value2['substrates_mass']) ? $value2['substrates_mass'] : '') . '||' . $valueString;
                                            }
                                            if ($fieldName == 'reactant_v') {
                                                $valueString = (!emptyExclude0($value2['substrates_volume']) ? $value2['substrates_volume'] : '') . '||' . $valueString;
                                            }
                                            if ($fieldName == 'reactant_c') {
                                                $valueString = (!emptyExclude0($value2['substrates_temperature']) ? $value2['substrates_temperature'] : '') . '||' . $valueString;
                                            }
                                            if ($fieldName == 'reactant_purity') {
                                                $valueString = (!emptyExclude0($value2['substrates_pressure']) ? $value2['substrates_pressure'] : '') . '||' . $valueString;
                                            }
                                            if ($fieldName == 'reactant_resource') {
                                                $valueString = (!emptyExclude0($value2['substrates_comment']) ? $value2['substrates_comment'] : '') . '||' . $valueString;
                                            }
                                        }
                                    } else if ($type == 'reagent') {
                                        foreach ($dataResult['data']['catalysts_data'] as $key2 => $value2) {
                                            if ($fieldName == 'reagent_name') {
                                                $valueString = (!emptyExclude0($value2['catalysts_name']) ? $value2['catalysts_name'] : '') . '||' . $valueString;
                                            }
                                            if ($fieldName == 'reagent_batchno') {
                                                $valueString = (!emptyExclude0($value2['catalysts_batch_num']) ? $value2['catalysts_batch_num'] : '') . '||' . $valueString;
                                            }
                                            if ($fieldName == 'reagent_mass') {
                                                $valueString = (!emptyExclude0($value2['catalysts_mass']) ? $value2['catalysts_mass'] : '') . '||' . $valueString;
                                            }
                                            if ($fieldName == 'reagent_v') {
                                                $valueString = (!emptyExclude0($value2['catalysts_volume']) ? $value2['catalysts_volume'] : '') . '||' . $valueString;
                                            }
                                            if ($fieldName == 'reagent_c') {
                                                $valueString = (!emptyExclude0($value2['catalysts_temperature']) ? $value2['catalysts_temperature'] : '') . '||' . $valueString;
                                            }
                                            if ($fieldName == 'reagent_purity') {
                                                $valueString = (!emptyExclude0($value2['catalysts_pressure']) ? $value2['catalysts_pressure'] : '') . '||' . $valueString;
                                            }
                                            if ($fieldName == 'reagent_resource') {
                                                $valueString = (!emptyExclude0($value2['catalysts_comment']) ? $value2['catalysts_comment'] : '') . '||' . $valueString;
                                            }
                                        }
                                    } else if ($type == 'solvent') {
                                        foreach ($dataResult['data']['solvent_data'] as $key2 => $value2) {
                                            if ($fieldName == 'solvent_name') {
                                                $valueString = (!emptyExclude0($value2['solvent_name']) ? $value2['solvent_name'] : '') . '||' . $valueString;
                                            }
                                            if ($fieldName == 'solvent_v') {
                                                $valueString = (!emptyExclude0($value2['solvent_volume']) ? $value2['solvent_volume'] : '') . '||' . $valueString;
                                            }
                                            if ($fieldName == 'solvent_ratio') {
                                                $valueString = (!emptyExclude0($value2['solvent_ratio']) ? $value2['solvent_ratio'] : '') . '||' . $valueString;
                                            }
                                            if ($fieldName == 'solvent_mass') {
                                                $valueString = (!emptyExclude0($value2['solvent_mass']) ? $value2['solvent_mass'] : '') . '||' . $valueString;
                                            }
                                            if ($fieldName == 'solvent_resource') {
                                                $valueString = (!emptyExclude0($value2['solvent_comment']) ? $value2['solvent_comment'] : '') . '||' . $valueString;
                                            }
                                        }
                                    } else if ($type == 'condition') {
                                        foreach ($dataResult['data']['condition_data'] as $key2 => $value2) {
                                            if ($fieldName == 'condition_name') {
                                                $valueString = (!emptyExclude0($value2['condition_name']) ? $value2['condition_name'] : '') . '||' . $valueString;
                                            }
                                            if ($fieldName == 'condition_pressure') {
                                                $valueString = (!emptyExclude0($value2['condition_pressure']) ? $value2['condition_pressure'] : '') . '||' . $valueString;
                                            }
                                            if ($fieldName == 'condition_temperature') {
                                                $valueString = (!emptyExclude0($value2['condition_temperature']) ? $value2['condition_temperature'] : '') . '||' . $valueString;
                                            }
                                            if ($fieldName == 'condition_protection_gas') {
                                                $valueString = (!emptyExclude0($value2['condition_pg']) ? $value2['condition_pg'] : '') . '||' . $valueString;
                                            }
                                            if ($fieldName == 'condition_heating') {
                                                $valueString = (!emptyExclude0($value2['condition_heating']) ? $value2['condition_heating'] : '') . '||' . $valueString;
                                            }
                                            if ($fieldName == 'condition_reaction_time') {
                                                $valueString = (!emptyExclude0($value2['condition_rt']) ? $value2['condition_rt'] : '') . '||' . $valueString;
                                            }
                                            if ($fieldName == 'condition_comment') {
                                                $valueString = (!emptyExclude0($value2['condition_comment']) ? $value2['condition_comment'] : '') . '||' . $valueString;
                                            }
                                        }
                                    } else if ($type == 'product') {
                                        foreach ($dataResult['data']['pro_data'] as $key2 => $value2) {
                                            if ($fieldName == 'product_name') {
                                                $valueString = (!emptyExclude0($value2['name']) ? $value2['name'] : '') . '||' . $valueString;
                                            }
                                            if ($fieldName == 'product_batchno') {
                                                $valueString = (!emptyExclude0($value2['batch_num']) ? $value2['batch_num'] : '') . '||' . $valueString;
                                            }
                                            if ($fieldName == 'product_simple_id') {
                                                $valueString = (!emptyExclude0($value2['sample_id']) ? $value2['sample_id'] : '') . '||' . $valueString;
                                            }
                                            if ($fieldName == 'product_barcode') {
                                                $valueString = (!emptyExclude0($value2['barcode']) ? $value2['barcode'] : '') . '||' . $valueString;
                                            }
                                            if ($fieldName == 'product_salt') {
                                                $valueString = (!emptyExclude0($value2['salt']) ? $value2['salt'] : '') . '||' . $valueString;
                                            }
                                            if ($fieldName == 'product_salteq') {
                                                $valueString = (!emptyExclude0($value2['salt_eq']) ? $value2['salt_eq'] : '') . '||' . $valueString;
                                            }
                                            if ($fieldName == 'product_actual_mass') {
                                                $valueString = (!emptyExclude0($value2['mass']) ? $value2['mass'] : '') . '||' . $valueString;
                                            }
                                            if ($fieldName == 'product_yield') {
                                                $valueString = (!emptyExclude0($value2['yield']) ? $value2['yield'] : '') . '||' . $valueString;
                                            }
                                            if ($fieldName == 'product_purity') {
                                                $valueString = (!emptyExclude0($value2['purity']) ? $value2['purity'] : '') . '||' . $valueString;
                                            }
                                        }
                                    } else if ($type == 'details') {
                                        foreach ($dataResult['data']['details_data'] as $key2 => $value2) {
                                            if ($fieldName == 'details_name') {
                                                $valueString = (!emptyExclude0($value2['name']) ? $value2['name'] : '') . '||' . $valueString;
                                            }
                                            if ($fieldName == 'details_cas') {
                                                $valueString = (!emptyExclude0($value2['cas']) ? $value2['cas'] : '') . '||' . $valueString;
                                            }
                                            if ($fieldName == 'details_risk_assessment') {
                                                $valueString = (!emptyExclude0($value2['risk_assessment']) ? $value2['risk_assessment'] : '') . '||' . $valueString;
                                            }
                                            if ($fieldName == 'details_comment') {
                                                $valueString = (!emptyExclude0($value2['comment']) ? $value2['comment'] : '') . '||' . $valueString;
                                            }
                                        }
                                    }

                                    $moduleDataArr[$relayId][$index] = rtrim($valueString,"||");
                                    $match = true;
                                }
                                break;
                        }
                    }
                }


                if (!$match) { // 没匹配就填充空数据，
                    foreach ($fieldIdArray as $index => $fieldId) {
                        $moduleDataArr[$relayId][$index] = '';
                    }
                }
            }

            // 3.2 把各个模块值提出来直接合并成最终的结构化内容数组
            $resultDataArray = [];
            foreach ($moduleDataArr as $moduleData) {
                foreach ($moduleData as $item) {
                    $resultDataArray[] = $item;
                }
            }
            $expValueArray[$exp_id] = $resultDataArray;

        }

        $data['structureTitle'] = $resultTitleArray;
        $data['structureData'] = $expValueArray;

        // 插入历史
        if(isset($data['is_history']) && $data['is_history']==0){
            $structureDataHistory = new StructdataHistory();
            $structureDataHistory->exp_num = join(';',$exp_nums);
            $structureDataHistory->user_id = $this->userinfo->id;
            $structureDataHistory->save();
        }

        $file = $this->renderAjax('/setting/struct_data_data.php',$data);
        return $this->success([
            'file' => $file,

        ]);

    }

    /**
     * Notes: 导出结构化数据csv
     * Author: hkk
     * Date: 2021/3/11 17:15
     * @return \common\controllers\json
     * @throws \PHPExcel_Exception
     */
    public function actionExportStructureDataCsv(){


        $keyResult =  \Yii::$app->getRequest()->post('titleArray',[]);
        $dataResult =  \Yii::$app->getRequest()->post('dataArray',[]);


        // 数字转excel列标函数
        function IntToChr($index, $start = 65) {
            $str = '';
            if (floor($index / 26) > 0) {
                $str .= IntToChr(floor($index / 26)-1);
            }
            return $str . chr($index % 26 + $start);
        }

        // 开始导出excel
        require_once \Yii::$app->vendorPath . '/PHPExcel/PHPExcel.php';
        require_once \Yii::$app->vendorPath . '/PHPExcel' . DIRECTORY_SEPARATOR . 'IOFactory.php';
        $objPHPExcel = new PHPExcel();
        $objPHPExcel->getProperties()->setTitle("export")->setDescription("none");


        // 写入标题
        foreach ($keyResult as $key => $title) {
            $objPHPExcel->setActiveSheetIndex(0)->setCellValueExplicit(IntToChr($key).'1',$title);// 设置内容栏
        }

        // 写入内容
        foreach ($dataResult as $rowIndex => $rowData) {
            foreach ($rowData as $colIndex => $value) {

                $objPHPExcel->setActiveSheetIndex(0)->setCellValueExplicit(IntToChr($colIndex) . ($rowIndex + 2), $value);// 设置内容栏
            }
        }

        ob_end_clean();
        $objPHPExcel->setActiveSheetIndex(0);
        $objWriter = \PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel2007');


        $file = \Yii::getAlias('@filepath') . DS . 'structureData'. '.xlsx';
        $encryption = new Encryption();
        if ($encryption->isHuatuEncrypt()) {
            $tmpFile = \Yii::getAlias('@svgpath') . DS . 'structureData' . '.xlsx';
            $objWriter->save($tmpFile);
            $file = $encryption->encryptFileApi($file, $tmpFile); // 调用加密接口加密
            @unlink($tmpFile);// 删除临时文件
        } else {
            $objWriter->save($file);
            $file = $encryption->encryptFileApi($file); // 调用加密接口加密
        }
        return $this->success([]);

    }


    /*
     * 获取结构化数据历史
     *   */
    public function actionStructdataHistory(){

        \Yii::info('渲染页面开始：');
        $page = \Yii::$app->request->post('page', 1);
        $limit = \Yii::$app->request->post('limit', \Yii::$app->params['default_page_size']);
        $orderBy = \Yii::$app->request->post('order_by', 'id');
        $orderType = \Yii::$app->request->post('order_type', 'asc');

        $where['start_time'] = \Yii::$app->request->post('start_time', '');
        $where['end_time'] = \Yii::$app->request->post('end_time', '');
        $where['user_id'] = $this->userinfo->id;


        $dataList = (new GroupSettingServer())->listStructdataHistory($where, $limit, $page, $orderBy, $orderType);
        $data['history_list'] = $dataList['history_list'];
        $data['limit'] = $limit;
        $data['totalCount'] = $dataList['totalCount'];
        $data['params'] = $where;

        //$file = $this->renderAjax('/setting/historyList.php',$data);

        $needUpdateAllPage = \Yii::$app->request->post('needUpdateAllPage', 1); // add by hkk 2020/6/23
        // 第一次渲染，更新整个页面
        if (!empty($needUpdateAllPage)) {
            $file = $this->renderAjax('/setting/historyList.php', $data);
            return $this->success(['contentHtml' => $file]);
        }

        // 传page参数只更新表格数据
        $file = $this->renderAjax('/setting/historyList_page.php', $data);
        return $this->success(['contentHtml' => $file]);



    }

    public function actionGetStructureDataCsvCheck(){

        //渲染页面

        \Yii::info('渲染页面开始：');
        $data = \Yii::$app->request->post();

        /*验证是否有实验编号不存在*/

        $exp_nums= explode(',',substr($data['exp_nums'], 0,strlen($data['exp_nums'])-1));
        $exp_nums = array_filter($exp_nums);
        if(empty($exp_nums)){
             return $this->fail(\Yii::t('common', 'no exp number'));
        }
        $tmpArr=[];
        $idArr=[];
        $i=0;
        foreach($exp_nums as $item) {
            // 解析实验编号

            // delete by szq 2020/6/10 逻辑有错误
            // $arr = explode('-', $item);
            // if(strlen($item)<1)
            // {
            //     return $this->fail(\Yii::t('common', 'number cant empty'));
            // }
            // if(count($arr) < 2) {
            //     return $this->fail($item . \Yii::t('common', 'no exp'));
            // }
            //
            // $bookCode = substr($arr[0], 3);
            // $expPage = $arr[1];

            $pos = strrpos($item,'-');
            if (!$pos) {
                return $this->fail($item . \Yii::t('common', 'no exp'));
            } else {
                $bookCode = substr($item, 0, $pos);
                $expPage = substr($item, $pos + 1);
            }

            // 查找实验
            $exp = ExperimentModel::find()->select(['EXP.id', 'EXP.user_id', 'EXP.template_id'])->from(ExperimentModel::tableName() . ' AS EXP')
                ->innerJoin(BookModel::tableName() . ' AS BOK', 'BOK.id=EXP.book_id')
                ->where([
                    // 'BOK.code' => $bookCode,
                    'BOK.book_code' => $bookCode,
                    'EXP.exp_page' => $expPage
                ])->one();

            if(empty($exp)) {
                return $this->fail($item . \Yii::t('common', 'no exp'));
            }

            if(empty($exp['template_id'])){
                return $this->fail($item . \Yii::t('common', 'no exp template'));
            }
            if($i==0){
                $base_temp_id = $exp['template_id'];
            }

            if ($base_temp_id != $exp['template_id']) {
                return $this->fail(\Yii::t('common', 'number different'), '', $item);
            }

            $i++;

            array_push($tmpArr, $exp['template_id']);
            array_push($idArr, $exp['id']);
        }
        return $this->success([]);

    }

    public function _numToABC($index) {
        $ABC = array();
        for ($i = 65; $i < 90; $i++) {
            $ABC[$i - 64] = chr($i);
        }
        $ABC[0] = chr(90);
        $str = '';
        while ($index > 0) {
            $remainder = $index % 26;
            $str = $ABC[$remainder] . $str;
            if ($index % 26 == 0)
                $index--;
            $index = intval($index / 26);
        }
        return $str == '' ? 'A' : $str;
    }

    /*
     * 获取企业词库的内容
     * ?r=group-setting/get-dict-value-by-parent-id
     * <AUTHOR>
     * @copyright 2019-09-05
     * */
    public function actionGetDictValueByParentId(){
        //$postData = \Yii::$app->request->post();
        $parent_id=\Yii::$app->getRequest()->post('id',0);
        $company_id = \Yii::$app->view->params['curr_company_id'];
        $result = (new GroupSettingServer())->getDictValueByParentId($parent_id,$company_id);
        return json_encode($result);
    }

    /**
     * Notes: 获取鹰群复核后重开设置
     * Author: zhu huajun
     * Date: 2019/3/11 10:18
     */
    public function actionGetRequireSetting() {
        // 群id
        $groupId = \Yii::$app->getRequest()->post('group_id');
        if (empty($groupId)) {
            return $this->fail(\Yii::t('base', 'select_group'));
        }

        // 验证权限
//        $hasAuth = CompanyAuthServer::checkGroupAuth($this->userinfo->current_company_id, $groupId, $this->userinfo->id, 'group_require');
//        if (!$hasAuth) {
//            return $this->fail(\Yii::t('base', 'no_auth'));
//        }

        $auth = CompanyAuthServer::getGroupAuthByUserIdAndGroupId($this->userinfo->id, $groupId);
        $type = 'set_require_config';
        if (empty($auth) || !isset($auth[$type]) || !$auth[$type]) {
            return $this->fail(\Yii::t('base', 'no_auth'));
        }

        // 获取群成员
        $requireResult  = (new GroupSettingServer())->getRequireSetting($groupId);

        $html = $this->renderAjax('/setting/group_require_temp.php', [
            'setting' => $requireResult['data']
        ]);

        return $this->success([
            'html' => $html
        ]);
    }

    /**
     * Notes: 保存鹰群复核后重开设置
     * Author: zhu huajun
     * Date: 2019/3/11 10:18
     */
    public function actionSetRequireSetting() {
        //群id
        $groupId = \Yii::$app->getRequest()->post('group_id');
        $params['title'] = \Yii::$app->getRequest()->post('title', 0);
        $params['keyword'] = \Yii::$app->getRequest()->post('keyword', 0);
        $params['project'] = \Yii::$app->getRequest()->post('project', 0);
        $params['task'] = \Yii::$app->getRequest()->post('task', 0);
        $params['wo_cc'] = \Yii::$app->getRequest()->post('wo_cc', 0);
        $params['wo_resolve_exp'] = \Yii::$app->getRequest()->post('wo_resolve_exp', 0);
        $params['admin_id'] = $this->userinfo->id;

        if (empty($groupId)) {
            return $this->fail(\Yii::t('base', 'select_group'));
        }
        $result = (new GroupSettingServer())->setRequireSetting($groupId, $params);

        $group = (new CenterInterface())->getGroupByGroupId($groupId);
        // 添加日志
        $expAuthChangeLog = new ExpAuthChangeLog();
        $expAuthChangeLog->user_id = $this->userinfo->id;
        $expAuthChangeLog->operate_desc = '[鹰群] '.$group['group_name'].' [群通用设置] [必填项设置]-用户 ' .
            (strlen(trim($this->userinfo->name)) > 0 ? $this->userinfo->name : $this->userinfo->real_name)
            . '设置必填项设置为 ' . str_replace(['1', '0'], ['开启', '关闭'], $params['title']) . ' 标题;'
            . str_replace(['1', '0'], ['开启', '关闭'], $params['keyword']) . ' 关键字;'
            . str_replace(['1', '0'], ['开启', '关闭'], $params['project']) . ' 项目;'
            . str_replace(['1', '0'], ['开启', '关闭'], $params['task']) . ' 任务;'
            . str_replace(['1', '0'], ['开启', '关闭'], $params['wo_cc']) . ' 工单抄送人员必填;'
            . str_replace(['1', '0'], ['开启', '关闭'], $params['wo_resolve_exp']) . ' 对工单操作"解决"时实验页码必填;';

        $expAuthChangeLog->log_ip = $_SERVER[ 'REMOTE_ADDR' ];
        $role_arr=[];
        $system_master = 0;
        if(@getVar($this->userinfo->role_ids)){
            $role_arr = explode(',',$this->userinfo->role_ids);
        }
        if(in_array('1',$role_arr)){
            $system_master = 1;
        }
        $expAuthChangeLog->system_master = $system_master ;
        $expAuthChangeLog->save();

        if (empty($result['status'])) {
            return $this->fail($result['info']);
        }
        return $this->success($result['data']);
    }

    /**
     * 鹰群设置页权限
     * ?r=group-setting/set-group-auth-config
     *
     * <AUTHOR>
     * @copyright 2016-10-29
     * post
     * @param int $group_id 鹰群id
     * @param int|null $user_id 如果是针对鹰群设置的无需传递
     * @param boolean is_group 1表示是group 0表示是用户
     */
    public function actionSetUseStaticConfig() {
        $useStaticConfig = (new GroupSettingServer())->getUseStaticConfig($this->userinfo->id);
        //print_r($useStaticConfig);
        $html = $this->renderAjax('/static/set_use_static_config.php', $useStaticConfig);

        return $this->success([
            'file' => $html
        ]);
    }

    /**
     * 鹰群设置页权限
     * ?r=group-setting/set-group-auth-config-submit
     *
     * <AUTHOR>
     * @copyright 2016-10-29
     * post
     * @param int $group_id 鹰群id
     * @param int|null $user_id 如果是针对鹰群设置的无需传递
     * @param boolean is_group 1表示是group 0表示是用户
     */
    public function actionSetUseStaticConfigSubmit() {
        $postData = \Yii::$app->getRequest()->post();
        $data=$postData;

            $useStaticConfig = new \frontend\models\UseStaticConfig();
            $useStaticConfig->updateAll($data,['user_id'=>$this->userinfo->id]);

        return $this->success([]);
    }

    /**
     * Notes: 根据类型获取鹰群通用设置
     * Author: zhu huajun
     * Date: 2019/10/14 13:25
     * @return \common\controllers\json
     */
    public function actionGetGeneralSettingByType() {
        // 群id
        $groupId = \Yii::$app->getRequest()->get('group_id');
        if (empty($groupId)) {
            return $this->fail(\Yii::t('base', 'select_group'));
        }

        // 设置类型（创建记录本设置等）
        $type = \Yii::$app->getRequest()->get('type');
        if (empty($type)) {
            return $this->fail('');
        }

        /*// 验证权限
        $typeAuth = [
            'reopen' => 'group_reopen',
            'create_book' => 'group_create_notebook',
            'approve' => 'group_approve',
        ];
        $hasAuth = CompanyAuthServer::checkGroupAuth($this->userinfo->current_company_id, $groupId, $this->userinfo->id, $typeAuth[$type]);
        if (!$hasAuth) {
            return $this->fail(\Yii::t('base', 'no_auth'));
        }*/

        $auth = CompanyAuthServer::getGroupAuthByUserIdAndGroupId($this->userinfo->id, $groupId);
        $typeAuth = [
//            'reopen' => 'group_reopen',
            'create_book' => 'set_create_book_config',
            'approval' => 'set_group_check_config',
        ];
        if (empty($auth) || !isset($auth[$typeAuth[$type]]) || !$auth[$typeAuth[$type]]) {
            return $this->fail(\Yii::t('base', 'no_auth'));
        }

        // 获取鹰群通用设置
        $settingResult  = (new GroupSettingServer())->getGeneralSetting($groupId);
        $generalSetting = $settingResult['data'];

        // 根据类型获取相应设置以及页面模板
        $setting = null;
        $viewTemp = '';
        switch($type) {
            case 'approval':
                if(!empty($generalSetting['approval_setting'])) {
                    $setting = json_decode($generalSetting['approval_setting'], true);
                }
                $viewTemp = '/setting/group_approval_temp.php';
                break;
            case 'reopen':
                if(!empty($generalSetting['reopen_setting'])) {
                    $setting = json_decode($generalSetting['reopen_setting'], true);
                }
                $viewTemp = '/setting/group_reopen_temp.php';
                break;
            case 'create_book':
                if(!empty($generalSetting['create_book_setting'])) {
                    $setting = json_decode($generalSetting['create_book_setting'], true);
                }
                $viewTemp = '/setting/create_book_temp.php';
                break;
        }

        // 获取公司所有人员
        $companyUsers = (new CenterInterface())->getUserListByCompanyId(\Yii::$app->view->params['curr_company_id']);
        $userList = yii\helpers\ArrayHelper::index($companyUsers['list'], 'id');

        $html = $this->renderAjax($viewTemp, [
            'user_list' => $userList,
            'setting' => $setting
        ]);

        return $this->success([
            'html' => $html
        ]);
    }

    /**
     * Notes: 根据类型保存鹰群通用设置
     * Author: zhu huajun
     * Date: 2019/10/14 13:25
     */
    public function actionSaveGeneralSettingByType() {
        // 群id
        $groupId = \Yii::$app->getRequest()->post('group_id');
        if (empty($groupId)) {
            return $this->fail(\Yii::t('base', 'select_group'));
        }

        // 设置类型（创建记录本设置等）
        $type = \Yii::$app->getRequest()->post('type');
        if (empty($type)) {
            return $this->fail('');
        }

        // 设置内容
        $setting = \Yii::$app->getRequest()->post('setting');

        $result = (new GroupSettingServer())->saveGeneralSetting($groupId, $type, $setting);

        /*//添加日志(针对reopen操作，老代码，先备份放在这里)
        $expAuthChangeLog = new ExpAuthChangeLog();
        $expAuthChangeLog->user_id = $this->userinfo->id;
        $expAuthChangeLog->operate_desc = '['.\Yii::t('base', 'group_setting').'] ['.\Yii::t('base', 'reopen_setting').']-'.\Yii::t('base', 'user').' '.(strlen(trim($this->userinfo->name))>0?$this->userinfo->name:$this->userinfo->real_name).'  '.str_replace(["1", '0'], [\Yii::t('base', 'enable'),\Yii::t('base', 'disable')], $setting['un_allow_edit'])." [".\Yii::t('base', 'can not edit')."] ;";
        if(!empty($setting['reopen_checker'])) {
            $user_info = (new CenterInterface())->userDetailsByUserIds($setting['reopen_checker']);
            $expAuthChangeLog->operate_desc .= "[".\Yii::t('base', 'reopen need sign')." ".$user_info[0]['nick_name']."]";
        }
        $expAuthChangeLog->log_ip = $_SERVER[ 'REMOTE_ADDR' ];
        $expAuthChangeLog->save();*/

        return $this->success([]);
    }

    /**
     * Notes: 获取鹰群关于某个成员针对某个类型的设置
     * Author: zhu huajun
     * Date: 2019/10/18 17:07
     */
    public function actionGetMemberSettingByType() {
        // 群id
        $groupId = \Yii::$app->getRequest()->get('group_id', 0);
        if (empty($groupId)) {
            return $this->fail('');
        }

        // 用户id
        $userIds = \Yii::$app->getRequest()->get('user_ids', []);

        $settingRes = (new CompanyServer())->getCompanySetting( [
            'EMPOWER_BY_AUTH',
        ]);
        $setting = @getVar($settingRes['data'], []);
        if (isset($setting['EMPOWER_BY_AUTH']) && $setting['EMPOWER_BY_AUTH']['value'] != 1) {
            return $this->fail(\Yii::t('base', 'no_power_by_auth'));
        }

        // 设置类型（复核设置等）
        $type = \Yii::$app->getRequest()->get('type');
        if (empty($type)) {
            return $this->fail('');
        }
        $groupAuthSetting = CompanyAuthServer::getGroupAuthByUserIdAndGroupId($this->userinfo->id, $groupId);

        // 验证权限
        $typeAuthArr = [
            'witness' => 'set_witness_config',
            'signing' => 'set_sign_config',
            'pretrial' => 'set_pretrial_config',
            'other' => 'set_other_config',
        ];
        $hasAuth = 1;
        if (empty($groupAuthSetting['set_witness_config']) && empty($groupAuthSetting['set_sign_config']) && empty($groupAuthSetting['set_pretrial_config']) && empty($groupAuthSetting['set_other_config'])) {
            $hasAuth = 0;
        }

        if (!$hasAuth) {
            switch ($type) {
                case 'witness':
                    return $this->fail(\Yii::t('base', 'witnessSetNoAuthTip'));
                case 'signing':
                    return $this->fail(\Yii::t('base', 'signSetNoAuthTip'));
                case 'pretrial':
                    return $this->fail(\Yii::t('base', 'pretrialSetNoAuthTip'));
                case 'other':
                    return $this->fail(\Yii::t('base', 'otherSetNoAuthTip'));
            }
        }

        $memberSetting = null;
        if (!empty($userIds)) {
            $settingResult = (new GroupSettingServer())->getMemberSetting($groupId, $userIds);
            $memberSetting = $settingResult['data'];

            // 多个用户批量设置，需判断每个用户的设置是不是完全一致
            if (count($userIds) > 1) {
                // 用户数和结果数不一致，说明部分用户之前未设置过，则不完全一致
                if (count($userIds) !== count($memberSetting)) {
                    $memberSetting = null;
                } else {
                    // 用户数和结果数一致，对相关设置列去重,如果去重后只剩一项，说明这些用户的设置是一致的
                    $setting = array_column($memberSetting, $type . '_setting');
                    $setting = array_unique($setting);
                    if (count($setting) === 1) {
                        $memberSetting = $memberSetting[0];
                    } else {
                        $memberSetting = null;
                    }
                }
            }
        }

        // 企业全部用户列表
        $companyUsers = (new CenterInterface())->getUserListByCompanyId(\Yii::$app->view->params['curr_company_id'], $type === 'other');
        $cmpUserList = yii\helpers\ArrayHelper::index($companyUsers['list'], 'id');

        // 根据类型获取相应设置以及页面模板
        $setting = null;
        $viewTemp = '';
        $renderData = [
            'user_list' => $cmpUserList,
            'cmp_user_list' => $cmpUserList,
        ];
        switch ($type) {
            case 'witness'://复核设置
                if(empty($groupAuthSetting['set_witness_config'])){
                    return $this->fail(\Yii::t('base', 'witnessSetNoAuthTip'));
                }
                if (!empty($memberSetting['witness_setting'])) {
                    $setting = json_decode($memberSetting['witness_setting'], true);
                    // 一级审批转多级
                    if (!empty($setting['approval_user_ids'])) {
                        $setting['approval_nodes'] = [
                            ['approval_user_ids' => $setting['approval_user_ids']]
                        ];
                    }
                }
                $viewTemp = '/setting/witness_temp.php';
                break;
            case 'signing'://签字设置
                if(empty($groupAuthSetting['set_sign_config'])){
                    return $this->fail(\Yii::t('base', 'signSetNoAuthTip'));
                }
                if (!empty($memberSetting['signing_setting'])) {
                    $setting = json_decode($memberSetting['signing_setting'], true);
                }
                $viewTemp = '/setting/signing_temp.php';
                break;
            case 'pretrial'://预审设置
                if(empty($groupAuthSetting['set_pretrial_config'])){
                    return $this->fail(\Yii::t('base', 'pretrialSetNoAuthTip'));
                }
                if (!empty($memberSetting['pretrial_setting'])) {
                    $setting = json_decode($memberSetting['pretrial_setting'], true);
                }
                $viewTemp = '/setting/pretrial_temp.php';

                // 获取鹰群关联的项目
                $projectList = (new PMInterface())->getUserProjects($this->userinfo);
                $renderData['project_list'] = $projectList;
                break;
            case 'other': // 其他设置
                if (empty($groupAuthSetting['set_other_config'])) {
                    return $this->fail(\Yii::t('base', 'otherSetNoAuthTip'));
                }
                if (!empty($memberSetting['other_setting'])) {
                    $setting = json_decode($memberSetting['other_setting'], true);
                }
                $viewTemp = '/setting/other_temp.php';

                // 获取鹰群成员
                $groupMember = (new CenterInterface())->listGroupMember($groupId, ['include_history_member' => true]);
                $groupUserList = yii\helpers\ArrayHelper::index($groupMember, 'user_id');

                $renderData['group_user_list'] = $groupUserList;
                $renderData['is_group_master'] = false;
                if (count($userIds) === 1) {
                    // 获取当前用户的角色
                    $centerInterface = new CenterInterface();
                    $roleParams = [
                        'user_id' => $userIds[0],
                        'group_id' => $groupId,
                    ];
                    $currUserRole = $centerInterface->getUserRole($roleParams);
                    $renderData['is_group_master'] = $currUserRole == 3;
                }
                break;
        }

        $renderData['setting'] = $setting;
        $html = $this->renderAjax($viewTemp, $renderData);

        return $this->success([
            'html' => $html
        ]);
    }

    /**
     * Notes: 保存鹰群关于某个成员针对某个类型的设置
     * Author: zhu huajun
     * Date: 2019/10/18 17:07
     */
    public function actionSaveMemberSettingByType() {
        // 群id
        $groupId = \Yii::$app->getRequest()->post('group_id');
        if (empty($groupId)) {
            return $this->fail(\Yii::t('base', 'select_group'));
        }

        // 用户id数组
        $userIdArr = \Yii::$app->request->post('user_ids', []);
        if(empty($userIdArr)) {
            return $this->fail('');
        }

        // 设置类型
        $type = \Yii::$app->getRequest()->post('type');
        if (empty($type)) {
            return $this->fail('');
        }

        // 设置内容
        $setting = \Yii::$app->getRequest()->post('setting');

        $result = (new GroupSettingServer())->saveMemberSetting($groupId, $userIdArr, $type, $setting);
        if (empty($result['status'])) {
            return $this->fail($result['info']);
        }
        $group = (new CenterInterface())->getGroupByGroupId($groupId);
        $setting['group_name'] = $group['group_name'];
        $groupSettingServer = (new GroupSettingServer());
        switch ($type) {
            case 'witness': //复核设置
                $logRes = $groupSettingServer->logGroupMemberWitness($this->userinfo, $setting, $userIdArr);
                break;
            case 'pretrial':    //预审设置
                $logRes = $groupSettingServer->logGroupMemberPretrial($this->userinfo, $setting, $userIdArr);
                break;
            case 'signing': //签字设置
                $logRes = $groupSettingServer->logGroupMemberSigning($this->userinfo, $setting, $userIdArr);
                break;
            case 'other':   //其他设置
                $logRes = $groupSettingServer->logGroupMemberOther($this->userinfo, $setting, $userIdArr);
                break;
        }

        return $this->success([]);
    }

    /**
     * 鹰群设置页权限
     * ?r=group-setting/set-group-auth-config-submit
     *
     * <AUTHOR>
     * @copyright 2016-10-29
     * post
     * @param int $group_id 鹰群id
     * @param int|null $user_id 如果是针对鹰群设置的无需传递
     * @param boolean is_group 1表示是group 0表示是用户
     */
    public function actionUseStaticChatsTwo() {
        //获取企业的自定义配置

        //渲染页面
        //获取字段显示配置
        $useStaticConfig = (new GroupSettingServer())->getUseStaticConfig($this->userinfo->id);
        //字段显示配置获取完成


        \Yii::info('渲染页面开始：');
        $page = \Yii::$app->request->post('page', 1);
        $limit = \Yii::$app->request->post('limit', \Yii::$app->params['default_page_size']);
        $orderBy = \Yii::$app->request->post('order_by', 'id');
        $orderType = \Yii::$app->request->post('order_type', 'asc');

        $where['user_id'] = \Yii::$app->request->post('user_id', 0);
        $where['group_id'] = \Yii::$app->request->post('group_id', 0);
        $where['department_id'] = \Yii::$app->request->post('department_id', 0);
        $where['role_id'] = \Yii::$app->request->post('role_id', 0);
        $where['start_time'] = \Yii::$app->request->post('start_time', '');
        $where['end_time'] = \Yii::$app->request->post('end_time', '');
        $where['order'] = \Yii::$app->request->post('order', 'asc');
        $where['sort'] = \Yii::$app->request->post('sort', 'id');
        $where['group_id'] = is_array($where['group_id'])?join(',',$where['group_id']):$where['group_id'];
        $data['where'] = $where;

       $echats_data = [];

       $temp_id= \Yii::$app->request->post('temp_id', 0);
       if($temp_id>0){
           $data = (new GroupSettingServer())->getUseStaticById($temp_id);

           $data['where']=$where;
           if(json_decode($data['filter'],true)){
               $data['where'] = json_decode($data['filter'],true);
           }

           $echats_data = json_decode($data['content'],true);

       }


        $file = $this->renderAjax('/static/use_static_chats_two.php',$data);
        return $this->success([
            'contentHtml' => $file,
            'echats_data' => $echats_data,
        ]);
    }

    /**
     * 鹰群设置页权限
     * ?r=group-setting/set-group-auth-config-submit
     *
     * <AUTHOR>
     * @copyright 2016-10-29
     * post
     * @param int $group_id 鹰群id
     * @param int|null $user_id 如果是针对鹰群设置的无需传递
     * @param boolean is_group 1表示是group 0表示是用户
     */
    public function actionUseStaticChatsMulti() {
        //获取企业的自定义配置
        $company_id = $this->userinfo->current_company_id;
        $companyDecimal = new CompanyDecimal();
        $decimalDetail = $companyDecimal->findOne(['company_id'=>$company_id]);

        /*验证有没有分享设置的权限*/
         $isCan= (new CompanyAuthServer())->isCanCompanyAuth($this->userinfo->current_company_id,0, $this->userinfo->id,'decimal_config_read');
         if(!$isCan)
         {
             return $this->fail('您没有查看设置物料表小数点的权限');
         }

        $isCanWrite = (new CompanyAuthServer())->isCanCompanyAuth($this->userinfo->current_company_id,0, $this->userinfo->id,'decimal_config_write');

        /*验证完成*/


        $data['detimal']=$decimalDetail;
        $data['isCanWrite']=$isCanWrite;

        $file = $this->renderAjax('/setting/use_static_chats_multi.php',$data);
        return $this->success([
            'file' => $file,

        ]);
    }

    /*
     * 获取结构化数据历史
     *   */
    public function actionGetUseStaticHistory(){

        \Yii::info('渲染页面开始：');
        $page = \Yii::$app->request->post('page', 1);
        $limit = \Yii::$app->request->post('limit', \Yii::$app->params['default_page_size']);
        $orderBy = \Yii::$app->request->post('order_by', 'id');
        $orderType = \Yii::$app->request->post('order_type', 'asc');

        $where['start_time'] = \Yii::$app->request->post('start_time', '');
        $where['end_time'] = \Yii::$app->request->post('end_time', '');
        $where['user_id'] = $this->userinfo->id;


        $dataList = (new GroupSettingServer())->listUseStaticHistory($where, $limit, $page, $orderBy, $orderType);
        $data['history_list'] = $dataList['history_list'];
        $data['limit'] = $limit;
        $data['totalCount'] = $dataList['totalCount'];
        $data['params'] = $where;
        ///print_r($dataList);

        $file = $this->renderAjax('/static/use_static_history.php',$data);
        //$file = $this->renderAjax('/setting/use_static.php',$data);
        $needUpdateAllPage = \Yii::$app->request->post('needUpdateAllPage', 1); // add by hkk 2020/6/23
        // 第一次渲染，更新整个页面
        if (!empty($needUpdateAllPage)) {
            $file = $this->renderAjax('/static/use_static_history.php', $data);
            return $this->success(['contentHtml' => $file]);
        }

        // 传page参数只更新表格数据
        $file = $this->renderAjax('/static/use_static_history_page.php', $data);
        return $this->success(['contentHtml' => $file]);
    }

    /*
     * 获取结构化数据历史
     *   */
    public function actionGetUseStaticImg(){

        //渲染页面
        //获取字段显示配置
        $useStaticConfig = (new GroupSettingServer())->getUseStaticConfig($this->userinfo->id);
        //字段显示配置获取完成


        \Yii::info('渲染页面开始：');
        $page = \Yii::$app->request->post('page', 1);
        $limit = \Yii::$app->request->post('limit', \Yii::$app->params['default_page_size']);
        $orderBy = \Yii::$app->request->post('order_by', 'id');
        $orderType = \Yii::$app->request->post('order_type', 'asc');


        $where['order']=\Yii::$app->request->post('order', 'asc');
        $where['sort']=\Yii::$app->request->post('sort', 'id');
        $where['group_id']=\Yii::$app->request->post('group_id', '0');
        $where['department_id']=\Yii::$app->request->post('department_id', '0');
        $where['role_id']=\Yii::$app->request->post('role_id', '0');
        $where['user_id']=\Yii::$app->request->post('user_id', '0');
        $where['start_time']=\Yii::$app->request->post('start_time', '0');
        $where['end_time']=\Yii::$app->request->post('end_time', '0');

        $title = \Yii::$app->request->post('title', '');
        $param['exp_num'] = \Yii::$app->request->post('exp_num', 0);
        $param['exp_close_num'] = \Yii::$app->request->post('exp_close_num', 0);
        $param['exp_success_num'] = \Yii::$app->request->post('exp_success_num', 0);
        $param['exp_fail_num'] = \Yii::$app->request->post('exp_fail_num', 0);
        $param['exp_stop_num'] = \Yii::$app->request->post('exp_stop_num', 0);
        $param['exp_record_time'] = \Yii::$app->request->post('exp_record_time', 0);
        $param['exp_login_num'] = \Yii::$app->request->post('exp_login_num', 0);
        $param['exp_unlock_num'] = \Yii::$app->request->post('exp_unlock_num', 0);
        $param['exp_witness_num'] = \Yii::$app->request->post('exp_witness_num', 0);
        $param['group_num'] = \Yii::$app->request->post('group_num', 0);
        $param['book_num'] = \Yii::$app->request->post('book_num', 0);

        $color['exp_num']='#F3AAB5';  // 221, 107, 102 #DD6B66    F3AAB5
        $color['exp_close_num']='#F58A5E'; // RGB(64,49,193), #4031C1  F58A5E
        $color['exp_success_num']='#FDC873'; //230, 157, 135  E69D87  FDC873
        $color['exp_fail_num']='#9FC280'; //0, 0, 0  000000 9FC280
        $color['exp_stop_num']='#74A69F'; //234, 126, 83  EA7E53 74A69F
        $color['exp_record_time']='#8DA8D9';//238, 221, 120 #EEDD78 8DA8D9
        $color['exp_login_num']='#AAB8DB'; //89, 196, 230 59C4E6 AAB8DB
        $color['exp_unlock_num']='#C5ACE5';// 203, 176, 227 CBB0E3 C5ACE5
        $color['group_num']='#CC7A7A';// 255, 0, 0 FF0000 CC7A7A
        $color['book_num']='#D59687';//255, 140, 0 #FF8C00 D59687
        $color['exp_witness_num']='#D6B295'; //234, 126, 83  EA7E53 D6B295

        $unit['exp_num']='个';
        $unit['exp_close_num']='个';
        $unit['exp_success_num']='个';
        $unit['exp_fail_num']='个';
        $unit['exp_stop_num']='个';
        $unit['exp_witness_num']='个';
        $unit['exp_record_time']='分';
        $unit['exp_login_num']='次';
        $unit['exp_unlock_num']='次';
        $unit['group_num']='个';
        $unit['book_num']='个';
//'#313695', '#4575b4', '#74add1', '#abd9e9', '#e0f3f8', '#ffffbf', '#fee090', '#fdae61', '#f46d43', '#d73027', '#a50026'

        // 可见用户
        $userList = (new CenterInterface())->getVisibleUsers($this->userinfo->user_id, 0, [0, 1, 2]);
        $where['user_id'] = !empty($where['user_id']) ? $where['user_id'] : array_column($userList, 'user_id');

        $userData = (new GroupSettingServer())->listUseStatic($where, 1000, $page, $where['order'], $where['sort'], $param);
        if(!empty($userData['user_list'])){

            $user_data = $userData['user_list'];
            foreach($user_data as $uk=>$ukValue){
                if($ukValue['exp_record_time']>0){
                    $user_data[$uk]['exp_record_time']=intval($ukValue['exp_record_time']/60);
                }
            }
            $data['x_ray'] = array_column($user_data, 'user_name');


            //print_r($param);
            $i=0;
            foreach($param as $key=>$value){

                if($value){
                    //$data['y_ray'][$key] = array_column($user_data, $key);
                    $data['color'][] = $color[$key];
                    $data['legend'][] = (($key=='exp_num')?\Yii::t('base', 'experiments'):\Yii::t('base', $key));
                    $position = 'right';
                    if($i==0){
                        $position = 'left';
                    }

                    $offset =$i*20;
                    $dataTemp = array_column($user_data, $key);
                    foreach($dataTemp as $k=>$temp){
                        if(empty($temp)){
                            $dataTemp[$k] = 0;
                        }
                    }


                    $data['yaxis'][] = json_encode(['type'=>'value',
//                                         'name'=> (($key=='exp_num')?\Yii::t('base', 'experiments'):\Yii::t('base', $key)),
//                                         'position'=>  $position,
//                                         'offset'=> $offset,
//                                         'nameLocation'=>'middle',
//                                         'nameGap'=>($i==0?50:5),
//                                         'axisLine'=>['lineStyle'=>['color'=>$color[$key]]],
                                         //'axisLabel'=>['formatter'=>$unit[$key]],
                                        ]);

                    $data['series'][] = json_encode(['name'=>(($key=='exp_num')?\Yii::t('base', 'experiments'):\Yii::t('base', $key)),
                                         'type'=> 'bar',
                                         //'barWidth'=> '5',
                                         'data'=>  $dataTemp,
                                        ]);



                }
                $i++;
            }

        }

        $data['title']=['text'=>$title,'left'=>'center','top'=>'0'];

        $useStaticHistory = (new GroupSettingServer())->addUseStaticHistory($this->userinfo->id,$title,$param,$data,$where);
        $data['history_id'] = $useStaticHistory->id;

        return $this->success($data);



    }

    /*
     * 获取结构化数据历史
     *   */
    public function actionGetUseStaticImgHistory($id){

        $data = (new GroupSettingServer())->getUseStaticById($temp_id);
        //print_r($data);exit;
        return $this->success($data);



    }

    /*
     * 获取结构化数据历史
     *   */
    public function actionUpdateUseStaticHistoryImg(){
        $id = \Yii::$app->request->post('id', 0);
        $param['img_data'] = \Yii::$app->request->post('img_data', 0);
        $data = (new GroupSettingServer())->updateUseStaticHistoryById($id,$param);
        //print_r($data);exit;
        return $this->success($data);



    }

    /*
     * 获取结构化数据历史
     *   */
    public function actionGetUseStaticMailConfig(){
        $company_id = \Yii::$app->view->params['curr_company_id'];

        $useStaticMailConfig = (new GroupSettingServer())->getUseStaticMailConfig($company_id);


        $userListResult = (new CenterInterface())->getUserListByCompanyId($company_id, 1);
        $userList = isset($userListResult['list']) ? $userListResult['list'] : [];
        $tmpUserList = yii\helpers\ArrayHelper::index($userList,'user_id');


        $userIds = isset($useStaticMailConfig['to_user_ids'])?(!empty($useStaticMailConfig['to_user_ids'])?explode(',',$useStaticMailConfig['to_user_ids']):[]):[];
        if(!empty($userIds)){
            foreach($userIds as $uid){
                $userNames[]=$tmpUserList[$uid]['real_name'].'('.$tmpUserList[$uid]['name'].')';
            }
            $useStaticMailConfig['to_user_names']=join(',',$userNames);
        }


        $returnData = [
            'shareDefaultUserIds' => isset($useStaticMailConfig['to_user_ids'])? explode(',', $useStaticMailConfig['to_user_ids']) : [],
            'shareDefaultUserNames' => isset($useStaticMailConfig['to_user_names']) ? explode(',', $useStaticMailConfig['to_user_names']) : [],
            'type' => isset($useStaticMailConfig['type'])?$useStaticMailConfig['type']:'',
            //'start_time' => isset($useStaticMailConfig['send_day'])?$useStaticMailConfig['send_day']:'',
            'user_list' => $userList
        ];

        //print_r($returnData);exit;
        $html = $this->renderAjax('/static/use_static_mail_config.php', $returnData);

        return $this->success([
            'file' => $html
        ]);

    }

    /**
     * 鹰群设置页权限
     * ?r=group-setting/set-use-static-config-mail-submit
     *
     * <AUTHOR>
     * @copyright 2016-10-29
     * post
     * @param int $group_id 鹰群id
     * @param int|null $user_id 如果是针对鹰群设置的无需传递
     * @param boolean is_group 1表示是group 0表示是用户
     */
    public function actionSetUseStaticMailConfigSubmit() {
        $postData = \Yii::$app->getRequest()->post();
        $company_id = \Yii::$app->view->params['curr_company_id'];
        $data=$postData;


        $useStaticMailConfig = new \frontend\models\UseStaticMailConfig();
        $result = $useStaticMailConfig->updateAll($data,['company_id'=>$company_id]);

        return $this->success([]);
    }

    /*
     * 获取个人的时长统计图表
     *   */
    public function actionGetUseStaticRecordTime(){
        $company_id = \Yii::$app->view->params['curr_company_id'];

       $postData = \Yii::$app->getRequest()->post();

       $recordTimeData = (new GroupSettingServer())->getUseStaticRecordTime($postData);

       $returnData = ['data'=> $recordTimeData ];



        $html = $this->renderAjax('/static/use_static_record_time.php', $returnData);

        return $this->success([
            'file' => $html
        ]);

    }

    /**
     * Notes: 记录本迁移弹窗
     * Author: licongying
     * Date: 2019/10/18 17:07
     */
    public function actionMoveNoteBook() {
        $postData = \Yii::$app->getRequest()->post();
        //获取企业下所有鹰群
        /*//$company_id = \Yii::$app->view->params['curr_company_id'];
          //$groupList = (new CenterInterface())->getGroupsListByCompanyId($company_id);
          //$data['group_list'] = $groupList;*/
        $data['book_id'] = $postData['book_id'];
        $data['group_id'] = $postData['group_id'];
        $data['user_id'] = $postData['user_id'];    //记录本创建人员
        $data['book_name'] = $postData['book_name'];

        // 获取用户可见鹰群
        $curUid = $this->userinfo->id;
        $data['group_list'] = (new CenterInterface())->getVisibleGroups($curUid, [1]);

        //获取用户记录本换群设置
        $otherSetting = (new CompanyAuthServer())->getOtherSetting($curUid, $postData['group_id']);
        $groupRole = (new CenterInterface())->getUserRole(['user_id'=>$curUid,'group_id'=>$postData['group_id']]);
        $canCBG = ($groupRole == 3) || !empty($otherSetting['allow_cbg']) ? 1 : 0;  //用户是否有权换群（群主或被设置可以换群)

        if (!$canCBG) {
            $info = (new CenterInterface())->getGroupByGroupId($postData['group_id']);
            if (empty($info)) {
                return $this->fail(\Yii::t('group', 'get group info failed.'));
            }
            $userList = (new CenterInterface())->userDetailsByUserIds($info['master_id']);
            $failMessage = \Yii::t('views/applysetting', 'no_allow_cbg') . '<br/>' . \Yii::t('views/applysetting', 'connect_group_owner_enable_permission', [
                    $userList[0]['real_name'] . '(' . $userList[0]['name'] . ')',
                ]);
            return $this->fail($failMessage);
        }

        if ($canCBG && !empty($otherSetting['cbg_approval_nodes'])) {    //如果用户有权换群且需要审批
            $approvalRouteRes = (new ApprovalServer())->getApprovalRoute($otherSetting['cbg_approval_nodes'],  @getVar($otherSetting['approval_nodes_source'], []), $postData['group_id']);
            if ($approvalRouteRes['status'] != 1) {
                return $this->fail($approvalRouteRes['info'], 'JSON',['type' => 'popContent']);
            }
            $approverNames = $approvalRouteRes['data']['approver_names'];
            $data['approver_names'] = $approverNames;
        }

        $viewTemp = '/setting/move_note_book.php';

        $html = $this->renderAjax($viewTemp, $data);

        return $this->success([
            'html' => $html
        ]);
    }

    /**
     * Notes: 记录本迁移弹窗
     * Author: licongying
     * Date: 2019/10/18 17:07
     */
    public function actionMoveNoteBookSubmit() {
        $postData = \Yii::$app->getRequest()->post();

        if (empty($postData['from_group']) || empty($postData['to_group'])) {
            return $this->fail(\Yii::t('exp_info', 'please_select_group'));
        }
        //添加记录本的信息
        $centerInterface = new CenterInterface();
        $allGroupInfo = $centerInterface->groupByGroupIds([$postData['from_group'], $postData['to_group']]);
        $indexedGroup = ArrayHelper::index($allGroupInfo, 'id');
        $postData['group_name'] = $indexedGroup[$postData['from_group']]['group_name'];   //记录本旧鹰群
        $postData['to_group_name'] = $indexedGroup[$postData['to_group']]['group_name']; //记录本新鹰群
        $book = BookModel::findOne($postData['book_id']);
        $curUid = $this->userinfo->id;
        $otherSetting = (new CompanyAuthServer())->getOtherSetting($curUid, $postData['from_group']);
        $groupRole = (new CenterInterface())->getUserRole(['user_id'=>$curUid,'group_id'=>$postData['from_group']]);
        $canCBG = !empty($otherSetting['allow_cbg']) ? 1 : 0;  //用户是否有权换群（群主或被设置可以换群）

        if (!$canCBG) {
            return $this->fail(\Yii::t('views/applysetting', 'no_allow_cbg'));
        }

        if (!empty($otherSetting['allow_cbg'])) {    //如果用户被设置可以记录本换群
            if (!empty($otherSetting['cbg_approval_nodes'])) {  //如果用户换群需要审批
                //检查当前记录本当前用户是否有未完成的换群审批
                $type = \Yii::$app->params['approval_type']['change_book_group'];
                $checkRes = (new BookServer())->checkBookIsApproving($postData['book_id'], $curUid, $postData['from_group'], $type);
                if ($checkRes) {
                    return $this->fail(\Yii::t('msg', 'book_move_approving'));
                }
                $postData['send_email'] = 1;    //设置需要发送邮件
                //处理数据
                $postData['book_code'] = $book['book_code'];
                // 生成记录本审批
                $approvalServer = new ApprovalServer();
                $createApprovalRes = $approvalServer->createApproval(
                    \Yii::$app->params['approval_type']['change_book_group'],
                    $postData['from_group'],
                    $postData['from_group'],
                    json_encode($postData),
                    $otherSetting['cbg_approval_nodes'],
                    @getVar($otherSetting['approval_nodes_source'], [])
                );

                if (empty($createApprovalRes['status'])) {
                    return $this->fail($createApprovalRes['info'], 'JSON',['tipType' => 'popContent']);
                }

                return $this->success([$createApprovalRes['data']]);
            }
        }
        //print_r($postData);exit;
        //换群第一步 更新 book表的所属群
        // 1. 原来群里，所有的主动、被动分享、合著，在转移群后，全部取消，相当于一个孤立的记录本，默认除目标群的群主外，没人能够看到
        // 2. 如果记录者在目标群里，则记录者在转移后的群里可以直接看到该记录本；
        // 如果记录者不在目标群里，则他无法看到该群以及转到该群的记录本，除非目标群的群主把记录者加到群里。
        //发消息
        //记录者的记录本被群主转移后，记录者可以收到平台消息和邮件
        //获取记录本创建者的用户信息
        // 鹰群id
        //exit;
        //迁移鹰群
        BookModel::updateAll(['group_id' => $postData['to_group']], ['id' => $postData['book_id']]);

        //取消各种分享
        ShareModel::updateAll(['status' => 0], ['book_id' => $postData['book_id'], 'status' => 1]);

        //清理置顶的记录
        ShareTopModel::updateAll(['status' => 0], ['share_book_id' => $postData['book_id'], 'status' => 1]);

        //清理合著
        ExperimentCoauthorModel::updateAll(['status' => 0], ['book_id' => $postData['book_id'], 'status' => 1]);

        // 根据记录本用户在目标群的分享设置进行分享操作
        $shareRes = (new ShareServer())->shareBookExp($postData['book_id']);

        // 将记录本下的实验同步至pgsql中
        Curl::sendPostCurl(ELN_URL . '?r=async-task/sync-book-exp-to-pgsql', [
            'book_id' => $postData['book_id']
        ], true);

        //分享给目标群主
        //根据book_id 获取 exp_id
        // $experimentModel = new ExperimentModel();
        // $expList = $experimentModel->expIdsByBookId($postData['book_id']);
        // foreach($expList as $val){
        //     $expListArr[] = ['id'=>$val,'book_id'=>$postData['book_id'],'group_id'=>$postData['to_group']];
        // }
        // $expListArr =  yii\helpers\ArrayHelper::index($expListArr, 'id');

        //去掉分享 给目标群主的逻辑
        //(new ShareModel())->defaultUser(\Yii::$app->view->params['curr_user_id'], $postData['master_id'],  $expListArr, 1);
        //给目标群添加人
        $userDetail = (new CenterInterface())->getUserByUserId($postData['user_id']);
        $groupId = $postData['to_group'];
        // 被添加者账号 数组格式
        $data = [
            'group_id' => $groupId,
            'remark_name' => isset($userDetail['name'])?$userDetail['name']:$userDetail['real_name'],
            'user_id' => $userDetail['user_id'],
        ];
        (new CenterInterface())->addGroupMemberHistory($data); //调用接口添加成员到鹰群




        //换群日志
        $expChangeNotebookGroupLog = new ExpChangeNotebookGroupLog();
        $expChangeNotebookGroupLog->user_id = $this->userinfo->id;
        $expChangeNotebookGroupLog->from_group = $postData['from_group'];
        $expChangeNotebookGroupLog->to_group = $postData['to_group'];
        $expChangeNotebookGroupLog->book_id = $postData['book_id'];

        $expChangeNotebookGroupLog->operate_desc = $postData['operate_desc']; ;
        $expChangeNotebookGroupLog->save();

        return $this->success([]);

    }

    /**
     * 实验分享日志
     * ?r=group-setting/sys-exp-share-log
     */
    public function actionSysChangeNotebookGroupLog(){
        // 权限控制 jiangdm 2022/7/13
        if (!\Yii::$app->view->params['is_system_admin'] && \Yii::$app->view->params['change_book_group_log_read'] != 1) {
            return $this->fail(\Yii::t('base', 'no_auth'));
        }
        //渲染页面


        \Yii::info('渲染页面开始：');
        $page = \Yii::$app->request->post('page', 1);
        $limit = \Yii::$app->request->post('limit', \Yii::$app->params['default_page_size']);

        $where['operate_uid'] = \Yii::$app->request->post('operate_uid', 0);
        $where['create_uid'] = \Yii::$app->request->post('create_uid', 0);
        $where['start_time'] = \Yii::$app->request->post('start_time', '');
        $where['end_time'] = \Yii::$app->request->post('end_time', '');

        //查询该企业下所有用户
        $company_id = \Yii::$app->view->params['curr_company_id'];
        if(!$company_id){
            return $this->fail(\Yii::t('base', 'select_user'));
        }

        // 获取用户
        $companyUsers = (new CenterInterface())->getUserListByCompanyId(\Yii::$app->view->params['curr_company_id'], 1);
        $userList = yii\helpers\ArrayHelper::index($companyUsers['list'], 'id');
        $userArr = $userList;


        // 获取所有鹰群
        $groupList = (new CenterInterface())->getGroupsListByCompanyId($company_id, '', true);

        $groupArr = yii\helpers\ArrayHelper::index($groupList, 'id');

        $dataResult = (new GroupSettingServer())->listChangeNotebookGroupLog($where, $limit, $page);
        foreach($dataResult['data_list']  as  $key=>$val){

            $dataResult['data_list'][$key]['operate_username'] = isset($userArr[$val['operate_uid']]['name'])?$userArr[$val['operate_uid']]['name']:(isset($userArr[$val['operate_uid']]['real_name'])?:'');
            $dataResult['data_list'][$key]['create_username'] = isset($userArr[$val['create_uid']]['name'])?$userArr[$val['create_uid']]['name']:(isset($userArr[$val['operate_uid']]['real_name'])?$userArr[$val['operate_uid']]['real_name']:'');
            $dataResult['data_list'][$key]['from_group_name'] = $groupArr[$val['from_group']]['group_name'];
            $dataResult['data_list'][$key]['to_group_name'] = $groupArr[$val['to_group']]['group_name'];

        }


        $data['data_list'] = $dataResult['data_list'];
        $data['all_user'] = $userArr;

        $data['limit'] = $limit;
        $data['totalCount'] = $dataResult['totalCount'];
        $data['params'] = $where;

        $data['all_user'] = yii\helpers\ArrayHelper::index($data['all_user'], 'user_id');
        foreach ($data['data_list'] as $key => $value) {
            $data['data_list'][$key]['create_real_name']='';
            $data['data_list'][$key]['create_name']='';
            if (array_key_exists($value['create_uid'], $data['all_user'])) {
                $data['data_list'][$key]['create_real_name'] = $data['all_user'][$value['create_uid']]['real_name'];
                $data['data_list'][$key]['create_name'] = $data['all_user'][$value['create_uid']]['name'];
            }
            $data['data_list'][$key]['operate_real_name']='';
            $data['data_list'][$key]['operate_name']='';
            if (array_key_exists($value['operate_uid'], $data['all_user'])) {
                $data['data_list'][$key]['operate_real_name'] = $data['all_user'][$value['operate_uid']]['real_name'];
                $data['data_list'][$key]['operate_name'] = $data['all_user'][$value['operate_uid']]['name'];
            }
        }

        $needUpdateAllPage = \Yii::$app->request->post('needUpdateAllPage', 1); // add by hkk 2020/6/23
        // 第一次渲染，更新整个页面
        if (!empty($needUpdateAllPage)) {
            $file = $this->renderAjax('/log/sys_change_notebook_group_log.php', $data);
            return $this->success(['contentHtml' => $file]);
        }

        // 传page参数只更新表格数据
        $file = $this->renderAjax('/log/sys_change_notebook_group_log_page.php', $data);
        return $this->success(['contentHtml' => $file]);
    }




    public function actionMyAuthBox() {
        // 获取我所在的鹰群列表
        $myGroupList = (new CenterInterface())->elnGroupListByUserId($this->userinfo->id, $this->userinfo->current_company_id);
        $html = $this->renderAjax('/setting/my_group_auth', ['groupList' => $myGroupList]);
        return $this->success(['contentHtml' => $html, 'tabName' => Yii::t('base', 'my_permissions')]);
    }




}
