import {reactive} from "vue";
import {MaterialStore} from "@src/stores/material_store/material_store_types.ts";


export const _expId_materialStore_map: Map<number, MaterialStore> = reactive(new Map())


/**
 * expIdMapMaterialData的单例setter
 * @param expId
 * @param materialDt
 */
export function setMaterialStore(expId: number | string, materialDt: MaterialStore): (typeof _expId_materialStore_map) {
  const _expId = Number(expId)
  return _expId_materialStore_map.set(_expId, materialDt)
}

/**
 * 删除一个记录的物料表数据记录
 * @param expId
 */
export function deleteMaterialStore(expId: number | string): boolean {
  const _expId = Number(expId);
  return _expId_materialStore_map.delete(_expId);
}

/**
 * 校验指定实验id对应的物料表是否已经加载
 * @param expId
 */
export function isExpMaterialStoreLoaded(expId: number | string): boolean {
  const _expId = Number(expId);
  return _expId_materialStore_map.has(_expId);
}

/**
 * expIdMapMaterialData的单例getter
 * @param expId
 */
export function getMaterialStore(expId: number | string): MaterialStore | undefined {
  const _expId = Number(expId)
  return _expId_materialStore_map.get(_expId)
}

