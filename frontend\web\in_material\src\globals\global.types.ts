import {RowType} from "@src/entities/tableRows/rowTraits.ts";
import {DatabaseMaterial, MaterialTextInsertFmt, MaterialUpdateFmt} from "@src/middlewares/v1/material/material_types.ts";
import {IResult} from "@src/utils/utilTypes.ts";
import {GetMaterialErr} from "@src/constants/enums/material_err.ts";
import * as inMaterialApi from "@src/apis/inMaterialApi.ts";
import * as materialErr from "@src/constants/enums/material_err.ts";

declare global {
  type MaterialMenuItemType =
    | 'show_msds'
    | 'do_submit_chemical'
    | 'search_inventory_btn'
    | 'search_action_all_href'
    | 'delete_action'
    | 'copy_action'
    | 'do_submit_wms'

  type MaterialMenuItem<MK extends MaterialMenuItemType> = {
    key: MK,
    menu_auth: boolean,
    attrs: object,
    label: string,
    class_list: MK[],
    display_row_type: RowType[],
  }

  type MaterialSaltItem = {
    id: string;
    formula: string;
    name: string;
    formula_weight: number;
  }
}

declare global {
  interface Window {
    // 链接cms的url, 形如: https://splugcms.ineln.com/
    INCMS_URL?: string;
    
    // ELN 语言类型设置
    lang: `cn` | 'en';

    // ELN 声明的事件类型
    EVENT_NAMES: {
      MATERIAL: {
        // 标记物料表触发的自定义事件的事件来源
        EVENT_SOURCE: 'in_material',

        // 物料表变更计算当量的基础
        CHANGE_EQUIVALENT_BASE: 'material.change_equivalent_base',

        // 物料表添加行的事件名
        ADD_ROW: 'material.add_row',
        // 添加反应条件到inDraw
        ADD_CONDITION_TO_INDRAW: 'material.add_condition_to_indraw',
        // 添加反应产率到inDraw
        ADD_PRD_YIELD_TO_INDRAW: 'material.add_prd_yield_to_indraw',
        // 物料表手动删除物料
        DEL_ROW_ACTION: 'material.delete_row',
        // 修改自定义字段对应的企业词库
        CHANGE_DICT_ID: 'change-dict-id',

        // 物料表单元格退出编辑事件
        EXIT_EDIT: 'exit-edit',
        // 退出可插入仪器数据的单元格的编辑状态
        EXIT_EDIT_INSTRUMENT_MASS: 'exit-edit.instrument-mass',
        // 物料表数据行发生了改变
        ROW_CHANGE: 'material-row-change',
      },
    };

    MATERIAL_CONFIG: {
      // 物料表菜单栏选项
      MATERIAL_MENU: [
        MaterialMenuItem<'show_msds'>,
        MaterialMenuItem<'do_submit_chemical'>,
        MaterialMenuItem<'search_inventory_btn'>,
        MaterialMenuItem<'search_action_all_href'>,
        MaterialMenuItem<'delete_action'>,
        MaterialMenuItem<'copy_action'>,
        MaterialMenuItem<'do_submit_wms'>,
      ],
      // 物料表盐型选项
      MATERIAL_SALT_LIST: MaterialSaltItem[],
    }

    // 语言翻译包
    mainLang(langKey: string): string;
    getMaterialFormatDB(expId: number | string): IResult<DatabaseMaterial | null, GetMaterialErr | null>;
    getMaterialFormatUpdate(expId: number | string): MaterialUpdateFmt | null;
    getMaterialFormatDisplay(expId: number | string): MaterialTextInsertFmt | null;
    getMaterialFormatDebug(expId?: number | string): MaterialTextInsertFmt | null;
    testUpdateMaterial():any;
  }
}

// 物料表模块的全局api类型
type InMaterialApi = {
  [K in keyof typeof inMaterialApi]: typeof inMaterialApi[K] extends Record<string, any> ? typeof inMaterialApi[K] : never;
};
// 物料表模块对外暴露的api返回的错误类型
type InMaterialErr = {
  [K in keyof typeof materialErr]: typeof materialErr[K] extends Record<string, any> ? typeof materialErr[K]: never;
}

declare global {

  // requireJS导入的物料表模块
  type InMaterial = InMaterialApi & InMaterialErr
}