define(function (require) {
    var chooseId = [];
    var expIds = [];
    //require('bin/jquery-pagination');

    /** 一级复核选择的审批人设置缓存键名 */
    window.SINGLE_WIT_APPROVERS = 'single_witness_approvers';

    var getHtml = require('get_html');

    // 展开/隐藏实验列表的筛选
    $('body').on('click', '.filter-box-toggle', function () {
        $(window).scrollTop(0);
        $('.exp_conetnt.active .top-filter-box').toggle(100);
    });

    $('body').on('click', '.approval_btn', function (event) {
        window.noLoadTip = true;
        expTool.pageFn = false;
        var this_ = $(this);
        var type = this_.data('type');
        if (this_.parents('.exp_detial').length > 0) {
            chooseId = this_.parents('.exp_detial').find('.checkbox').val() || this_.closest('.exp_detial').attr('data-id');
            chooseId = [chooseId];
        } else {
            chooseId = expTool.getData();
        }

        if (this_.data('type') == 'viewApproval') {
            type && expTool['viewApproval'] ? expTool['viewApproval'](this_) : '';
            // exp_detial 点击会打开实验, 阻止它
            event.stopPropagation();
        } else if (this_.data('type') == 'approvalSingle') {
            // 单个审批
            var currentChooseId = this_.closest('tr').attr('data-id');
            if (currentChooseId) {
                chooseId.push(currentChooseId);
            } else {
                $.showAlert(mainLang('select_approval_item'));
            }
            type && expTool['approval'] ? expTool['approval'](this_) : '';
            // exp_detial 点击会打开实验, 阻止它
            event.stopPropagation();
        } else if (this_.data('type') == 'approvalInstrumentStatus') {
            // 单个审批
            var currentChooseId = this_.attr('data-id');
            if (currentChooseId) {
                chooseId.push(currentChooseId);
            } else {
                $.showAlert(mainLang('select_approval_item'));
            }
            type && expTool['approval'] ? expTool['approval'](this_) : '';
            // exp_detial 点击会打开实验, 阻止它
            event.stopPropagation();
        } else if ($('.exp_list_table:visible').length > 0 && this_.data('type') != 'share' && !this_.hasClass('no_id') && !this_.hasClass('add_module_btn')) {
            if ($('.exp_conetnt.active .checkbox:checked').length == 0) {
                $.showAlert(mainLang('select_approval_item'));
                return false;
            }
        }

        type && expTool[type] ? expTool[type](this_) : '';
        type && sign[type] ? sign[type](this_) : '';
        type && pretrial[type] ? pretrial[type](this_) : '';
        type && reopen[type] ? reopen[type](this_) : '';
        type && signing[type] ? signing[type](this_) : '';
    });

    $('body').on('click', '.click_btn', function () {
        window.noLoadTip = true;
        expTool.pageFn = false;
        var this_ = $(this);
        var type = this_.data('type');
        if (this_.parents('.exp_detial').length > 0) {
            chooseId = this_.parents('.exp_detial').find('.checkbox').val() || this_.closest('.exp_detial').attr('data-id');
        } else {
            chooseId = expTool.getData();
        }

        if ($('.exp_list_table:visible').length > 0
            && this_.data('type') != 'share'
            && !this_.hasClass('no_id')
            && !this_.hasClass('add_module_btn')
            && this_.data('type') !== 'ai'
        ) {
            if ($('.exp_conetnt.active .checkbox:checked').length == 0) {
                $.showAlert(mainLang('select_exp'));
                return;
            }
        }

        type && expTool[type] ? expTool[type](this_) : '';
        type && sign[type] ? sign[type](this_) : '';
        type && pretrial[type] ? pretrial[type](this_) : '';
        type && reopen[type] ? reopen[type](this_) : '';
        type && signing[type] ? signing[type](this_) : '';
    });

    $('body').on('click', '.modal-content .btn-primary', function (event) {
        var submit_method = $(this).attr('submit_method');
        expTool[submit_method] ? expTool[submit_method]($(this)) : '';
    });

    // 全选
    $('body').on('click', '.exp_conetnt.active #checkbox_all', function () {
        if ($(this).prop('checked')) {
            $('.exp_conetnt.active .exp_list_table .checkbox').prop('checked', true);
        } else {
            $('.exp_conetnt.active .exp_list_table .checkbox').prop('checked', false);
        }
    });

    //点别的checkbox
    $('body').on('click', '.exp_conetnt.active .checkbox:not(#checkbox_all)', function () {
        var checkboxes = $(this).closest('table').find('.checkbox:not(#checkbox_all)');
        $('.exp_conetnt.active #checkbox_all').prop('checked', checkboxes.length == checkboxes.filter(':checked').length);
    });

    $('body').on('click', '.set-excel-export #menu-export-full-select', function () {
        if ($(this).prop('checked')) {
            $('.set-excel-export .menu-export-field-box [type=checkbox]:not(.disabled)').prop('checked', true);
        } else {
            $('.set-excel-export .menu-export-field-box [type=checkbox]:not(.disabled)').prop('checked', false);
        }
    });

    $('body').on('change', '.set-excel-export .menu-export-field-box [type=checkbox]:not(.disabled)', function () {
        var checkboxes = $(this).closest('.menu-export-field-box').find('[type=checkbox]:not(.disabled)');
        $('.set-excel-export #menu-export-full-select').prop('checked', checkboxes.length == checkboxes.filter(':checked').length);
    });

    function checkSingleDisabled() {
        // 等待结果，因为可能是全选。要用他们的事件逻辑结束之后的状态来判断。
        setTimeout(function () {
            var $table = $(".exp_conetnt.active .exp_list_table");
            var inputLen = $table.find('input:not(#checkbox_all):checked').length;
            // 如果选择的大于一个。给该按钮加上disabled。
            if (1 != inputLen) {
                $(".exp_conetnt.active .tool_nav .single_check_btn").addClass('disabled');
            } else {
                $(".exp_conetnt.active .tool_nav .single_check_btn").removeClass('disabled');
            }
        }, 0)
    }

    //给实验列表里所有的checkbox添加的事件。
    //1.给tool-btn中只能单个生效的加disabled类。判断依据single_check_btn
    $('body').on('click', '.exp_conetnt.active .exp_list_table input:checkbox', function (event) {
        checkSingleDisabled();
    });

    //监听页面改动之后的事件。
    //目前的操作：1.如果上面的按钮有单个才可以点的，那么要给它进行一个判断。
    $('body').on('pageChange', function () {
        $(".exp_conetnt.active .tool_nav .single_check_btn").addClass('disabled');
    });

    var expType;
    $('body').on('mouseenter', '.share_exp_list select', function () {
        expType = $('.exp_title .exp_href.on').attr('data-type');
        if ($(this).find('option').length > 1 || $(this).val() > 0) {
            return;
        }
        if ($(this).data('selecetype') == 'user') {
            expTool.getUserList(expType);
        } else {
            expTool.getBookList(expType);
        }
    });

    //分享实验 选择用户 记录本
    $('body').on('change', '.share_exp_list select[data-selecetype="user"]', function () {
        $('.share_exp_list select[data-selecetype="book"]').html('<option>请选择</option>');
    });

    //实验提醒设置 -> 周期提醒checkbox事件
    $('body').on('change', '.experiment_reminder_setting #period_reminder', function (box) {
        var period_text = $('.experiment_reminder_setting .reminder_period_input');
        var reminder_text = $('.experiment_reminder_setting ._visible-user-input');

        if ($('.experiment_reminder_setting #period_reminder').is(':checked') ||
            $('.experiment_reminder_setting #time_point_reminder').is(':checked') ||
            $('.experiment_reminder_setting #update_reminder').is(':checked')) {
            reminder_text.css({"border-color": "red"});
        } else {
            reminder_text.css({"border-color": "#dcdcdc"});
        }

        if ($('.experiment_reminder_setting #period_reminder').is(':checked')) {
            period_text.css({"border-color": "red"});
        } else {
            period_text.css({"border-color": "#dcdcdc"});
        }
    })

    //实验提醒设置 -> 日期提醒checkbox事件
    //2023.4.14 jinkaixun
    $('body').on('change', '.experiment_reminder_setting #time_point_reminder', function (box) {
        var time_text = $('.experiment_reminder_setting .time_point');
        var reminder_text = $('.experiment_reminder_setting ._visible-user-input');

        if ($('.experiment_reminder_setting #period_reminder').is(':checked') ||
            $('.experiment_reminder_setting #time_point_reminder').is(':checked') ||
            $('.experiment_reminder_setting #update_reminder').is(':checked')) {
            reminder_text.css({"border-color": "red"});
        } else {
            reminder_text.css({"border-color": "#dcdcdc"});
        }

        if ($('.experiment_reminder_setting #time_point_reminder').is(':checked')) {
            time_text.css({"border-color": "red"});
            add_to_schedule_disable_or_not();
        } else {
            time_text.css({"border-color": "#dcdcdc"});
            $('.experiment_reminder_setting #add_to_schedule').prop('checked', false)
            $('.experiment_reminder_setting #add_to_schedule').attr('disabled', 'disabled');
        }
    })

    //实验提醒设置 -> 加入日程按钮必须在选择了日期之后才能使用
    $('body').on('change', '.experiment_reminder_setting input.time_point', function () {
        if ($('.experiment_reminder_setting #time_point_reminder').is(':checked')) {
            add_to_schedule_disable_or_not();
        } else {
            $('.experiment_reminder_setting #add_to_schedule').prop('checked', false)
            $('.experiment_reminder_setting #add_to_schedule').attr('disabled', 'disabled');
        }

    })

    //根据日期点的数量，判断加入日程按钮能否点击，在勾选日期提醒，设置日期和删除日期时需要
    function add_to_schedule_disable_or_not() {
        var isNull = true;
        $('.experiment_reminder_setting input.time_point').each(function () {
            var tpValue = $(this).val()
            if (tpValue != '') {
                isNull = false;
            }
        });
        if (!isNull) {
            $('.experiment_reminder_setting #add_to_schedule').removeAttr('disabled')
        } else {
            $('.experiment_reminder_setting #add_to_schedule').prop('checked', false)
            $('.experiment_reminder_setting #add_to_schedule').attr('disabled', 'disabled');
        }
    }

    // 实验提醒设置 -> 添加时间点
    $('body').on('click', '.experiment_reminder_setting .add-tp', function () {
        var tpHtml = '<span class="relative iblock fl mr20 tp-box">' +
            '<input type="text" class="datetimepicker time_point new" style="width: 150px">' +
            '<i class="date-picker-ico font-ico"></i>' +
            '<span class="del-tp"></span>' +
            '</span>';
        $(tpHtml).insertBefore($(this));
        $('.datetimepicker.new').datetimepicker({
            format: 'yyyy-mm-dd',
            autoclose: true,
            minView: 2,
            clearBtn: true,
        });
        $('.datetimepicker.new').removeClass('new');
        var time_point_text = $('.experiment_reminder_setting .time_point');
        if ($('.experiment_reminder_setting #time_point_reminder').is(':checked')) {
            time_point_text.css({"border-color": "red"});
        } else {
            time_point_text.css({"border-color": "#dcdcdc"});
        }
    });

    // 实验提醒设置 -> 删除时间点
    $('body').on('click', '.experiment_reminder_setting .del-tp', function () {
        $(this).parent().remove();
        add_to_schedule_disable_or_not();
    });

    //实验提醒设置 -> 实验有更新时提醒
    $('body').on('change', '.experiment_reminder_setting #update_reminder', function () {
        var reminder_text = $('.experiment_reminder_setting ._visible-user-input');

        if ($('.experiment_reminder_setting #period_reminder').is(':checked') ||
            $('.experiment_reminder_setting #time_point_reminder').is(':checked') ||
            $('.experiment_reminder_setting #update_reminder').is(':checked')) {
            reminder_text.css({"border-color": "red"});
        } else {
            reminder_text.css({"border-color": "#dcdcdc"});
        }
    });

    // 实验提醒设置 -> 提交
    $('body').on('click', '.exp_reminder_setting_submit', function () {
        var setting = {};
        //检测是否需要设置提醒人
        var reminder_people_set_flag = 0;

        if ($('.experiment_reminder_setting #period_reminder').is(':checked')) {
            setting.reminder_period = $('.experiment_reminder_setting .reminder_period_input').val();
            if (setting.reminder_period === '' || setting.reminder_period <= 0) {//开启周期提醒之后，周期时间必须填写且大于0
                $.showAlert(mainLang('please_fill_in_required_field'));
                return;
            }
            //判断输入的周期是否是正整数
            if (!(/(^[1-9]\d*$)/.test(setting.reminder_period))) {
                $.showAlert(mainLang('please_fill_in_period_correctly'));
                return;
            }
            reminder_people_set_flag = 1;
        }

        if ($('.experiment_reminder_setting #time_point_reminder').is(':checked')) {
            var timePoints = [];
            $('.experiment_reminder_setting input.time_point').each(function () {
                var tpValue = $(this).val()
                if (tpValue != '') {
                    timePoints.push(tpValue);
                }
            });
            if (timePoints.length == 0) {//开启时间点提醒之后，时间点必须填写
                $.showAlert(mainLang('please_fill_in_required_field'));
                return;
            }
            setting.time_points = timePoints;
            reminder_people_set_flag = 1;
        }

        if ($('.experiment_reminder_setting #update_reminder').is(':checked')) {
            setting.update_reminder = 1;
            reminder_people_set_flag = 1;
        }

        var userIds = $('.experiment_reminder_setting [name="user_ids"]').attr('idbox');
        if (reminder_people_set_flag) {
            if (userIds) {
                setting.reminder_users = userIds.split(',');
            } else {//提醒人必填
                $.showAlert(mainLang('experiment_reminder_people_error'));
                return;
            }
        }

        setting.reminder_content = $('.experiment_reminder_setting .reminder_content').val();

        var expIds = chooseId;
        if ($(this).attr('data-id')) {
            expIds = [$(this).attr('data-id')];
        }

        setting.addToSchedule = $('.experiment_reminder_setting #add_to_schedule').prop('checked') ? 1 : 0;
        $.ajaxFn({
            url: ELN_URL + '?r=experiment/save-reminder-setting',
            data: {
                experiment_ids: expIds,
                setting: setting
            },
            success: function (data) {
                if (data.status == 1) {
                    $('.experiment_reminder_setting.modal').modal('hide');
                    $.showAlert(mainLang('success'));

                    var showIcon = false;
                    if (setting.reminder_period || (setting.time_points && setting.time_points.length > 0) || setting.update_reminder) {
                        showIcon = true;
                    }

                    for (var i = 0; i < expIds.length; i++) {
                        var $tr = $('.exp_conetnt.active .exp_list_table tr[data-id=' + expIds[i] + ']');
                        var $iconWrap = $tr.find('.exp_list_ico');
                        var $reminderIco = $iconWrap.find('.reminder_ico');

                        if (showIcon && $reminderIco.length == 0) {
                            $iconWrap.append(' <i class="reminder_ico" data-id="' + expIds[i] + '" title="' + mainLang('set_reminder') + '"></i>');
                        } else if (!showIcon && $reminderIco.length != 0) {
                            $reminderIco.remove();
                        }
                    }
                }
            }
        });
    });

    $('body').on('click', '.exp_list_ico .reminder_ico', function () {
        expTool['set_reminder']($(this));
        return false;
    });

    // add by hkk 2020/4/26 绑定通用功能显示隐藏列
    $('body').on('click', '.exp_conetnt.active .newShowHideColumn', function () {
        var tableId = $(this).attr('data-tableId');
        var th = $(`.exp_conetnt.active #${tableId}`).find('tr th');
        var html = '<ul class="ml30 exp-list-cols clear">';
        th.each(function (index, item) {
            var colFlag = 'idForShowColumn' + index;
            var checked = $(this).is(':visible') ? 'checked' : '';
            // 去掉全选后为空，不显示到隐藏列
            var showItem = $(this).find('span').text() && index !== 0 ? 'style="position: relative"' : 'style="position: relative;display: none"';
            var labelText = $(this).find('span').text();
            html += '<li class="col-item" ' + showItem + '>' +
                '<input type="checkbox" class="beauty-checkbox-big" id="' + colFlag + '"' + checked + '/>' +
                '<label for="' + colFlag + '" title="' + labelText + '">' + labelText + '</label>'
            if (index === 1) {
                html +=
                    '<div class="new_tip_icon iblock" data-width="350px;" data-toward="down_right" style="position: absolute; right: 22px; top: 12px">\n' +
                    '<div class="question_mark">?</div>\n' +
                    '<div class="new_tip_content onExpModel hide">' + mainLang('name1_tips') + '</div>\n' +
                    '</div>';
            }
            html += '</li>';
        });

        html += '</ul>';
        $.popContent(html, mainLang('show_hidden'), function () {
            var colIndexArray = []; // 存储隐藏列的索引
            $('.exp-list-cols .col-item :checkbox').each(function (index, item) {
                if ($(this).is(':checked') || index === 0) {
                    $(`.exp_conetnt.active #${tableId}`).find(`td:nth-child(${index + 1})`).show();
                    $(`.exp_conetnt.active #${tableId}`).find(`th:nth-child(${index + 1})`).show();
                } else {
                    $(`.exp_conetnt.active #${tableId}`).find(`td:nth-child(${index + 1})`).hide();
                    $(`.exp_conetnt.active #${tableId}`).find(`th:nth-child(${index + 1})`).hide();
                    colIndexArray.push(index + 1); // 存储隐藏列索引
                }
            });
            localStorage.setItem(tableId + '_cols_index', colIndexArray);
            $(".exp-list-cols").parents('.pop_modal').modal('hide');
        });
    });

    // 弹框中的显示隐藏列
    $('body').on('click', '.modal-body .newShowHideColumn', function () {
        var tableId = $(this).attr('data-tableId');
        var th = $(`.modal-body #${tableId}`).find('tr th');


        var html = '<ul class="ml30 exp-list-cols clear">';
        th.each(function (index, item) {
            var colFlag = 'idForShowColumn' + index;
            var checked = $(this).is(':visible') ? 'checked' : '';
            html += '<li class="col-item">' +
                '<input type="checkbox" class="beauty-checkbox-big" id="' + colFlag + '"' + checked + '/>' +
                '<label for="' + colFlag + '">' + $(this).text() + '</label>' +
                '</li>';
        })

        html += '</ul>';
        $.popContent(html, mainLang('show_hidden'), function () {
            var colIndexArray = []; // 存储显示列的索引
            $('.exp-list-cols .col-item :checkbox').each(function (index, item) {
                if ($(this).is(':checked')) {
                    $(`.modal-body #${tableId}`).find(`td:nth-child(${index + 1})`).show();
                    $(`.modal-body #${tableId}`).find(`th:nth-child(${index + 1})`).show();
                } else {
                    $(`.modal-body #${tableId}`).find(`td:nth-child(${index + 1})`).hide();
                    $(`.modal-body #${tableId}`).find(`th:nth-child(${index + 1})`).hide();
                    colIndexArray.push(index + 1); // 存储隐藏列索引
                }
            });
            localStorage.setItem(tableId + '_cols_index', colIndexArray);
            $(".exp-list-cols").parents('.pop_modal').modal('hide');
        });
    });

    // 库存同步扣减数据弹窗 jiangdm 2022/3/25
    $('body').on('click', '.sync_inventory', function () {
        var relay = $(this).closest('.modul_line');
        var relayId = relay.find('.modul_part_id').data('id');
        var componentId = relay.attr('component_id');
        if (!componentId) {
            componentId = 1;
            var massUnit = relay.find('.substrates_mass_unit');
        }
        $.ajaxFn({
            url: ELN_URL + '?r=experiment/sync-inventory-data',
            data: {
                source: 'module',
                relay_id: relayId,
                component_id: componentId ? componentId : 1,
            },
            type: 'post',
            success: function (data) {
                if (data.status == 1) {
                    $(".sync-inventory-modal").remove();
                    $("body").append(data.data.file);
                    $(".sync-inventory-modal").modal('show');
                }
            }
        });
    });

    // 更新弹窗内的日志和扣减量信息 jiangdm 2022/3/25
    function updateLog(data) {
        var massUnits = ['μg', 'mg', 'g', 'kg', 't'];
        var volumeUnits = ['μl', 'ml', 'l'];
        for (var relay in data) {
            var module = $('.modul_part_id[data-id="' + relay + '"]').closest('.modul_line');
            var componentId = module.attr('component_id');
            for (var batch in data[relay]) {
                var batch_info = data[relay][batch];
                // 同步扣减状态图标渲染
                if (componentId == 13) {
                    var substrateRow = module.find('input.wms_batch_id[value="' + batch + '"]').closest('.defineData');
                    substrateRow.find('.wms_sync_status').val(0);
                    substrateRow.find('.warehouse_sync_ico').remove();
                    substrateRow.each(function () {
                        $(this).find('input[name="field_data"]').eq(2).removeClass('warehouse_sync_input');
                    })
                    if (batch_info['amount'] > 0) {
                        substrateRow.find('.wms_sync_status').val(1);
                        substrateRow.each(function () {
                            var amountInput = $(this).find('input[name="field_data"]').eq(2).addClass('warehouse_sync_input');
                            amountInput.after('<span class="warehouse_sync_ico" title="' + mainLang('sync_wms_ico_title') + '"></span>');
                        })
                    }
                } else {
                    var substrateRow = module.find('input.wms_batch_id[value="' + batch + '"]').closest('.materialTr');
                    substrateRow.find('.warehouse_sync_ico').remove();
                    substrateRow.find('.materiel_mass_input').removeClass('warehouse_sync_input');
                    substrateRow.find('.materiel_vol_input').removeClass('warehouse_sync_input');
                    substrateRow.find('.wms_sync_status').val(0);
                    var unit = batch_info['unit'].toLowerCase();
                    if (massUnits.includes(unit)) {
                        substrateRow.find('.wms_sync_status').val(1);
                        var massInput = substrateRow.find('.materiel_mass_input').addClass('warehouse_sync_input');
                        massInput.after('<span class="warehouse_sync_ico" title="' + mainLang('sync_wms_ico_title') + '"></span>');
                    } else if (volumeUnits.includes(unit)) {
                        substrateRow.find('.wms_sync_status').val(2);
                        var volumeInput = substrateRow.find('.materiel_vol_input').addClass('warehouse_sync_input');
                        volumeInput.after('<span class="warehouse_sync_ico" title="' + mainLang('sync_wms_ico_title') + '"></span>');
                    }
                }
                // 日志内容渲染
                var row = $('.sync-inventory-modal').find('tr[data-batch_id="' + batch + '"][data-relay_id="' + relay + '"]');
                var syncAmount = batch_info['amount'] == 0 ? mainLang('not_sync') : (mainLang('is_sync') + batch_info['amount'] + batch_info['unit']);
                row.find('.sync_amount').text(syncAmount);
                row.find('.used_quantity').val(batch_info['amount']);
                var availableMass = batch_info['available_mass_value'];
                var massInput = row.find('.used_mass');
                massInput.removeClass('disabled');
                if (availableMass <= 0) massInput.addClass('disabled').val('');
                if (availableMass !== undefined) {
                    row.find('.available_mass').text(batch_info['available_mass_value'] + batch_info['unit']);
                    row.find('.available_mass_value').val(batch_info['available_mass_value']);
                }
                var syncLog = '';
                batch_info.log.forEach(l => {
                    var needAddCart = l.error_type == 2 || l.error_type == 3;
                    syncLog += '<p><span ';
                    if (needAddCart) syncLog += 'style="color:red;"';
                    syncLog += '>' + l['create_time'] + ' ' + l['remark'] + '</span>';
                    if (needAddCart) syncLog += '<a style="text-decoration: underline; cursor: pointer" class="sync_inventory_cart">' + mainLang("add_to_wms_cart") + '</a>';
                    if (l.error_type == 8) {
                        syncLog += '<a target="_blank" href="' + INVENTORY_URL + '/inventory/index?inventory_id=' + l.inventory_id;
                        syncLog += '&url=/apply-return/picking-cart?inventory_id=' + l.inventory_id + '">' + mainLang("view_wms_cart") + '</a>';
                    }
                    syncLog += '</p>';
                })
                row.find('.sync_log').html(syncLog);
            }
        }
        $('.sync-inventory-modal').find('.checkboxBtn,.check-all').prop('checked', false);
    }

    function syncOut(submitModal, batchInfos) {
        $.ajaxFn({
            url: ELN_URL + '?r=experiment/sync-inventory-out',
            data: {
                batch_info: batchInfos,
                exp_id: submitModal.data('exp_id'),
            },
            type: 'post',
            success: function (res) {
                updateLog(res.data);
            }
        });
    }

    // 提交扫码出库 2022/3/25
    $('body').on('click', '.sync_inventory_submit', function () {
        var batchInfos = [];
        var unitReg = /(^([0-9]+([\.][0-9]*)?)(.*)$)/;
        var submitModal = $(this).closest('.sync-inventory-modal');
        var itemCheckBox = $('.sync-inventory-modal input:checkbox:checked:not(.check-all)');
        if (itemCheckBox.length == 0) {
            $.showAlert(mainLang("batch_number_empty"));
            return;
        }
        var deducedItems = [];
        itemCheckBox.each(function () {
            var selectRow = $(this).closest('.sync_inventory_row');
            var batchInfo = {};
            batchInfo.batch_id = selectRow.data('batch_id');
            batchInfo.relay_id = selectRow.data('relay_id');
            // 正则匹配用户填写的使用的量，将数量和单位分离
            var usedAmount = selectRow.find('.used_mass').val().trim().match(unitReg);
            if (!usedAmount) {
                $.showAlert(mainLang("used_amount_empty"));
                return;
            }
            var requestMass = parseFloat(usedAmount[2]);
            if (requestMass <= 0) {
                $.showAlert(mainLang("request_mass_positive"));
                return;
            }
            var availableQuantity = selectRow.find('.available_quantity').val();
            if (availableQuantity <= 0) {
                $.showAlert(mainLang("available_quantity_zero"));
                return;
            }
            var availableMass = selectRow.find('.available_mass_value').val();
            if (availableMass < requestMass) {
                $.showAlert(mainLang("request_mass_overhead"));
                return;
            }
            batchInfo.request_mass = requestMass;
            batchInfo.request_unit = usedAmount[usedAmount.length - 1].trim();
            batchInfos.push(batchInfo);
            if (selectRow.find('.used_quantity').val() > 0) {
                deducedItems.push(selectRow.find('.sync_product_name').text());
            }
        });
        if (batchInfos.length == 0) {
            return;
        }
        if (deducedItems.length > 0) {
            var html = '<p class="deduce_confirm">' + deducedItems.join(',') + mainLang('deduce_confirm') + '</p>';
            $.popContent(html, mainLang('leave_tip_title'), function () {
                syncOut(submitModal, batchInfos);
                $('.deduce_confirm').parents('.pop_modal').modal('hide');
            });
        } else {
            syncOut(submitModal, batchInfos);
        }


    });

    // 提交扫码领料 Jiangdm 2022/3/25
    $('body').on('click', '.sync_inventory_cart', function () {
        var unitReg = /(^([0-9]+([\.][0-9]*)?)(.*)$)/;
        var selectRow = $(this).closest('.sync_inventory_row');
        var batchInfo = {};
        batchInfo.batch_id = selectRow.data('batch_id');
        batchInfo.relay_id = selectRow.data('relay_id');
        batchInfo.area_name = '---';
        var usedAmount = selectRow.find('.used_mass').val().trim().match(unitReg);
        if (!usedAmount) {
            $.showAlert(mainLang("used_amount_empty"));
            return;
        }
        batchInfo.request_mass = parseFloat(usedAmount[2]);
        batchInfo.request_unit = usedAmount[usedAmount.length - 1].trim();
        $.ajaxFn({
            url: ELN_URL + '?r=experiment/sync-inventory-cart',
            data: batchInfo,
            type: 'post',
            success: function (res) {
                updateLog(res.data);
            }
        })
    })

    // 全选按钮处理
    $('body').on('click', '.sync-inventory-modal .check-all', function () {
        var $modal = $(this).closest('.sync-inventory-modal');
        if ($(this).prop('checked')) {
            $modal.find('.checkboxBtn').prop('checked', true);
        } else {
            $modal.find('.checkboxBtn').prop('checked', false);
        }
    });

    //点别的checkbox
    $('body').on('click', '.sync-inventory-modal .checkboxBtn:not(.check-all)', function () {
        var $modal = $(this).closest('.sync-inventory-modal');
        var checkboxes = $modal.find('.checkboxBtn:not(.check-all)');
        $modal.find('.check-all').prop('checked', checkboxes.length == checkboxes.filter(':checked').length);
    });

    // 链接相关弹窗关闭事件判断 jiangdm
    $('body').on('click', function (evt) {
        var target = $(evt.target);
        // 实验信息
        if (!target.closest('.exp-link') || target.closest('.exp-link').length <= 0) {
            $('.exp-link-list').addClass('hide');
        }
        // 添加链接下拉菜单
        if (!target.closest('.add-exp-link') || target.closest('.add-exp-link').length <= 0) {
            $('.add-exp-link-box').addClass('hide');
        }
        // 批量搜索添加物品
        if (!target.closest('.batch-add-link-pop') || target.closest('.batch-add-link-pop').length <= 0) {
            if (!target.hasClass('batch-add-link')) {
                $('.batch-add-link-pop').addClass('hide');
            }
        }
    });

    // 链接弹窗展示状态切换相关事件 jiangdm
    // 选择添加链接类别
    $('body').on('click', '.add-exp-link-icon', function () {
        $('.add-exp-link-box').toggleClass('hide');
    });
    // 批量添加
    $('body').on('click', '.batch-add-link', function () {
        $('.batch-add-link-pop').removeClass('hide');
    });
    // 关闭批量添加
    $('body').on('click', '.batch-add-link-close', function () {
        $('.batch-add-link-pop').addClass('hide');
    });
    // 鼠标移入显示删除图标
    $('body').on('mouseenter', '.exp-link-item', function () {
        $(this).find('.delete-link').css('display', 'inline-block');
    });
    // 鼠标移出隐藏删除图标
    $('body').on('mouseleave', '.exp-link-item', function () {
        $(this).find('.delete-link').css('display', 'none');
    });
    // 点击链接跳转到其他页面前隐藏弹窗和下拉列表
    $('body').on('click', '.exp-link-option', function () {
        $('.exp_link_modal').modal('hide');
        $('.exp-link-list').addClass('hide');
    })

    // 链接搜索
    $('body').on('keyup', '.exp-link-search input', function (evt) {
        if (evt.keyCode == 13) {
            var keywords = $(this).val().split(' ');
            $('.exp-link-box:visible').find('.exp-link-item').each(function () {
                $(this).removeClass('hide');
                for (var k = 0; k < keywords.length; k++) {
                    var key = keywords[k].toLowerCase();
                    if (!$(this).text().toLowerCase().includes(key)) {
                        $(this).addClass('hide');
                        break;
                    }
                }
            })
        }
    })

    // 删除链接
    $('body').on('click', '.exp_link_modal .delete-link', function () {
        var linkItem = $(this).closest('.exp-link-item');
        var delTip = '<div class="del-link-tip">' + mainLang('del_link_tip') + '</div>';
        $.popContent(delTip, mainLang('del_link'), function () {
            $.ajax({
                url: '?r=experiment/delete-link',
                type: 'post',
                data: {
                    link_id: linkItem.data('link_id'),
                },
                success: function () {
                    linkItem.remove();
                    $('.del-link-tip').parents('.pop_modal').modal('hide');
                }
            })
        })
    })

    // 根据传递的链接列表数组渲染到前端
    var loadLinkList = function (links) {

        $('.add-link-pop').parents('.pop_modal').modal('hide');
        var viewLinkBox = $('.exp_conetnt.active .exp-link .exp-link-box'); // 实验信息栏的查看链接盒子
        var setLinkBox = $('.exp_link_modal .exp-link-box'); // 设置链接弹窗盒子
        viewLinkBox.html('');
        setLinkBox.html('');
        var len = links.length;
        if (len === 0) {
            setLinkBox.removeClass('border');
            setLinkBox.css('padding', '');
            return;
        }
        for (var l = 0; l < len; l++) {
            var link = links[l];
            var linkItem = '<div data-link_id="' + link.id + '" class="exp-link-item ' + (link.save_type == 2 ? 'manual' : '') + '">';
            linkItem += '<div class="exp-link-option break-inline">';
            if (link.target_type == '10') {
                linkItem += '<a title="' + link.target_name + '" class="exp_detial" data-id="' + link.target_content + '">';
            } else if (link.target_type == '20') {
                linkItem += '<a title="' + link.target_name + '" class="view_collaboration_page" data-id="' + link.target_content + '">';
            } else {
                linkItem += '<a title="' + link.target_name + '" target="_blank" href="' + link.url + '">';
            }
            linkItem += link.target_name + '</a>' + '</div>';
            viewLinkBox.append($(linkItem));
            linkItem += '<i class="delete-link"></i>';
            setLinkBox.append($(linkItem));
        }
        setLinkBox.addClass('border');
        setLinkBox.css('padding', '2px 0px 2px 10px');
    }

    // 实验信息栏的查看链接，加载链接列表
    $('body').on('click', '.exp-link-icon', function () {
        $('.exp-link-list').toggleClass('hide');
        if (!$('.exp-link-list').hasClass('hide')) {
            $.ajax({
                url: '?r=experiment/get-exp-link',
                type: 'POST',
                data: {
                    experiment_id: $('.exp_conetnt.active #exp_id').val(),
                },
                success: function (res) {
                    loadLinkList(res.data.links);
                    $('.modal-backdrop').remove();
                }
            });
            $('.exp_link_modal').show();
        }
    });

    // 添加链接
    $('body').on('click', '.exp_link_modal .add-exp-link-item', function () {
        $('.add-exp-link-box').addClass('hide');
        var linkType = $(this).data('link_type');
        var popWidth = ('work_order' === linkType || 'eln' === linkType) ? '450px' : '';
        var popTitle = $(this).text();
        $.ajax({
            url: '?r=site/add-link',
            type: 'post',
            data: {
                link_type: linkType,
            },
            success: function (res) {
                $.popContent(res.data.html, popTitle, function () {
                    var links = [];
                    var chemLinks = [];
                    var searchBox = $('.add-link-pop');
                    var expId = $(".exp_conetnt.active #exp_id").val();
                    var userId = $(".curr_user_id").val();
                    var expCode = $(".tag.on").attr("title");
                    // 根据链接类型不同选择不同拼接数据的方式
                    switch (linkType) {
                        case 'work_order':
                            // 工单
                            var userInput = searchBox.find('.add-link-text').val().trim();
                            if (userInput.length <= 0) {
                                $.showAlert(mainLang('work_order_empty_tip'));
                                return;
                            }
                            var orderId = userInput.toLowerCase().indexOf('wo') === 0 ? userInput.slice(2) : userInput;
                            links.push({
                                source_type: '1',
                                source_id: expId,
                                target_type: '20',
                                target_content: orderId,
                                target_name: userInput,
                                save_type: '2',
                                status: '1',
                                user_id: userId,
                            });
                            //$('.add-link-text:visible').focus();
                            break;
                        case 'eln':
                            // 实验，推两个是因为另一个实验也要看到本实验里面加了链接
                            var userInput = searchBox.find('.add-link-text').val().trim();
                            if (userInput.length <= 0) {
                                $.showAlert(mainLang('exp_code_empty_tip'));
                                return;
                            }
                            links.push({
                                source_type: '1',
                                source_id: expId,
                                target_type: '10',
                                target_content: userInput,
                                target_name: userInput,
                                save_type: '2',
                                status: '1',
                                user_id: userId,
                            });
                            links.push({
                                source_type: '1',
                                source_code: userInput,
                                target_type: '10',
                                target_content: expCode,
                                target_name: expCode,
                                save_type: '1',
                                status: '1',
                                user_id: userId,
                            });
                            break;
                        case 'cms':
                            // CMS，遍历勾选的化合物组装
                            searchBox.find('tr').each(function () {
                                var chemicalId = $(this).data('chemical_id');
                                if (chemicalId > 0) {
                                    if ($(this).find('input').get(0).checked) {
                                        links.push({
                                            source_type: '1',
                                            source_id: expId,
                                            target_type: '30',
                                            target_content: chemicalId,
                                            target_name: $(this).find('.incms_code').text() + '(' + $(this).find('.exp_code').text() + ')',
                                            save_type: '2',
                                            status: '1',
                                            user_id: userId,
                                        });
                                        chemLinks.push({
                                            source_id: chemicalId,
                                            source_type: '1',
                                            target_type: '10',
                                            target_content: expCode,
                                            target_name: expCode,
                                            save_type: '1',
                                            status: '1',
                                            user_id: userId,
                                        })
                                    }
                                }
                            });
                            if (links.length <= 0) {
                                $.showAlert(mainLang('chemical_empty_tip'));
                                return;
                            }
                            break;
                        case 'wms':
                            // 库存，遍历勾选的物品组装
                            searchBox.find('tr').each(function () {
                                var batchId = $(this).data('batch_id');
                                if (batchId > 0) {
                                    if ($(this).find('input').get(0).checked) {
                                        links.push({
                                            source_type: '1',
                                            source_id: expId,
                                            target_type: '40',
                                            target_content: JSON.stringify({
                                                inventory_id: $(this).data('inventory_id'),
                                                batch_id: batchId,
                                            }),
                                            target_name: $(this).find('.batch_num').text() + '(' + $(this).find('.product_name').text() + ')',
                                            save_type: '2',
                                            status: '1',
                                            user_id: userId,
                                        });
                                    }
                                }
                            });
                            if (links.length <= 0) {
                                $.showAlert(mainLang('inventory_item_empty_tip'));
                                return;
                            }
                            break;
                    }
                    //$('.add-link-text:visible').focus();
                    $.ajax({
                        url: '?r=experiment/save-exp-link',
                        type: 'POST',
                        data: {
                            exp_links: links,
                            chemical_links: chemLinks,
                            exp_id: expId,
                        },
                        success: function (res) {
                            loadLinkList(res.data.links); // 添加后刷新链接列表
                        }
                    });

                }, function () {
                    // $.loading();
                }, true, popWidth);
            },
        });
        setTimeout(function () {
            $('.add-link-text:visible').focus();
        }, 1000)
    })

    // 搜索库存物品
    $('body').on('click', '.search-wms-link', function () {
        var searchBox = $('.add-link-pop');
        var inventoryId = searchBox.find('select.inventory_list').val();
        var searchKey, isBatchSearch;
        if ($(this).hasClass('batch-add-link-confirm')) {
            // 批量搜索要进行标记，对数据预处理
            searchKey = searchBox.find('textarea.batch-search-key').val().split("\n").filter(x => x.toString().trim() != '');
            searchBox.find('.batch-add-link-pop').addClass('hide');
            isBatchSearch = 1;
            if (searchKey.length <= 0) return;
        } else {
            searchKey = searchBox.find('.search-link-key').val().trim();
            isBatchSearch = 0;
        }
        $.ajax({
            url: '?r=site/search-wms-link',
            type: 'post',
            data: {
                inventory_id: inventoryId,
                search_key: searchKey,
                is_batch: isBatchSearch,
            },
            success: function (res) {
                // 根据返回的搜索结果渲染
                var productList = res.data;
                if (productList.length <= 0) {
                    var nodata_html = '<td colspan="6">' + mainLang('wms_search_nodata') + '</td>';
                    $('.search-nodata').html(nodata_html);
                    return;
                }
                searchBox.find('.search-nodata').remove();
                var currentIds = [];
                searchBox.find('tr').each(function () {
                    if ($(this).data('batch_id')) {
                        currentIds.push($(this).data('batch_id').toString());
                    }
                })
                for (var p = 0; p < productList.length; p++) {
                    var product = productList[p];
                    if (currentIds.includes(product.batch_id)) continue;
                    var productRow = '<tr data-inventory_id="' + inventoryId + '" data-batch_id="' + product.batch_id + '">';
                    productRow += '<td><input type="checkbox" class="checkboxBtn"></td>';
                    productRow += '<td><div class="break-inline" style="max-width: 100px;" title="' + product.system_barcode + '">' + product.system_barcode + '</div></td>'
                    productRow += '<td class="product_name"><div class="break-inline" style="max-width: 150px;" title="' + product.product_name + '">' + product.product_name + '</div></td>';
                    productRow += '<td>' + product.product_num + '</td><td>' + product.specs + '</td>';
                    productRow += '<td class="batch_num">' + product.batch_num + '</td></tr>';
                    searchBox.find('table').append($(productRow));
                }
            }
        })
    })

    // 搜索库存物品
    $('body').on('click', '.search-cms-link', function () {
        var searchBox = $('.add-link-pop');
        var groupId = searchBox.find('select.group_list').val();
        var searchKey, isBatchSearch;
        if ($(this).hasClass('batch-add-link-confirm')) {
            // 批量搜索要进行标记，对数据预处理
            searchKey = searchBox.find('textarea.batch-search-key').val().split("\n").filter(x => x.toString().trim() != '');
            searchBox.find('.batch-add-link-pop').addClass('hide');
            isBatchSearch = 1;
            if (searchKey.length <= 0) return;
        } else {
            searchKey = searchBox.find('.search-link-key').val();
            isBatchSearch = 0;
        }
        $.ajax({
            url: '?r=site/search-cms-link',
            type: 'post',
            data: {
                group_id: groupId,
                search_key: searchKey,
                is_batch: isBatchSearch,
            },
            success: function (res) {
                // 根据返回的搜索结果渲染
                var chemicalList = res.data;
                if (chemicalList.length <= 0) {
                    var nodata_html = '<td colspan="6">' + mainLang('search_nodata') + '</td>';
                    $('.search-nodata').html(nodata_html);
                    return;
                }
                searchBox.find('.search-nodata').remove();
                var currentIds = [];
                searchBox.find('tr').each(function () {
                    if ($(this).data('chemical_id')) {
                        currentIds.push($(this).data('chemical_id'));
                    }
                })
                for (var c = 0; c < chemicalList.length; c++) {
                    var chemical = chemicalList[c];
                    if (currentIds.includes(chemical.chemical_id)) continue;
                    var chemicalRow = '<tr data-chemical_id="' + chemical.chemical_id + '"><td style="width:30px;"><input type="checkbox" class="checkboxBtn"></td>';
                    chemicalRow += '<td class="incms_code" style="width:150px;"><div class="break-inline" style="max-width: 150px;" title="' + chemical.basic.incms_code + '">' + chemical.basic.incms_code + '</div></td>';
                    chemicalRow += '<td class="exp_code" style="width:150px;"><div class="break-inline" style="max-width: 150px;" title="' + chemical.basic.exp_code + '">' + chemical.basic.exp_code + '</div></td>';
                    chemicalRow += '<td>' + chemical.basic.user_name + '</td><td style="width:100px;">' + chemical.basic.submit_time + '</td></tr>';
                    searchBox.find('table').append($(chemicalRow));
                }
            }
        })
    })

    // 小弹窗通用的全选按钮处理，绑定check-all即可
    $('body').on('click', '.pop_modal .check-all', function () {
        var $modal = $(this).closest('.pop_modal');
        if ($(this).prop('checked')) {
            $modal.find('.checkboxBtn').prop('checked', true);
        } else {
            $modal.find('.checkboxBtn').prop('checked', false);
        }
    });

    // 小弹窗通用的checkbox按钮处理，绑定checkboxBtn即可
    $('body').on('click', '.pop_modal .checkboxBtn:not(.check-all)', function () {
        var $modal = $(this).closest('.pop_modal');
        var checkboxes = $modal.find('.checkboxBtn:not(.check-all)');
        $modal.find('.check-all').prop('checked', checkboxes.length == checkboxes.filter(':checked').length);
    });

    // 渲染整个样品列表
    var loadSampleList = function () {
        var linkBox = $('.exp_conetnt.active .wms-sample-box');
        var currentLinks = JSON.parse(linkBox.find('textarea[name="wms_samples"]').val());
        var linkList = linkBox.find('.collaboration-link-box');
        linkList.html('');
        for (var c = 0; c < currentLinks.length; c++) {
            var link = currentLinks[c];
            const sampleLink = (link.target_type == '01')
                ? link.target_name
                : `<a title="${link.target_name}" target="_blank" href="${link.url}" >${link.target_name}</a>`;

            const lineItem = `
            <span class="exp-link-item flex-wrap" data-sample_index="${c}">
                ${sampleLink}
                <div class="flex-wrap pl-5" style="width: 10px;">
                    <span class="delete-link">x</span>
                </div>
            </span>`.trim();
            linkList.append(lineItem);
        }
    }

    // 添加链接
    $('body').on('click', '.wms-sample-box .add-exp-link-item', function () {
        $('.add-exp-link-box').addClass('hide');
        var linkType = $(this).data('link_type');
        var popTitle = $(this).text();
        $.ajax({
            url: '?r=site/add-link',
            type: 'post',
            data: {
                link_type: linkType,
            },
            success: function (res) {
                $.popContent(res.data.html, popTitle, function () {
                    var links = [];
                    var searchBox = $('.add-link-pop');
                    // 根据链接类型不同选择不同拼接数据的方式
                    switch (linkType) {
                        case 'text':
                            // 样品名称
                            var userInput = HtmlEncode(searchBox.find('.add-link-text').val().trim());
                            if (userInput.length <= 0) {
                                $.showAlert(mainLang('sample_text_empty_tip'));
                                return;
                            }
                            links.push({
                                target_type: '01',
                                target_content: userInput,
                                target_name: userInput,
                            })
                            break;
                        case 'cms':
                            // CMS，遍历勾选的化合物组装
                            searchBox.find('tr').each(function () {
                                var chemicalId = $(this).data('chemical_id');
                                if (chemicalId > 0) {
                                    if ($(this).find('input').get(0).checked) {
                                        links.push({
                                            target_type: '30',
                                            target_content: chemicalId,
                                            target_name: $(this).find('.incms_code').text(),
                                            url: INCMS_URL + 'chemical/view?chem_id=' + chemicalId,
                                        });
                                    }
                                }
                            });
                            if (links.length <= 0) {
                                $.showAlert(mainLang('chemical_empty_tip'));
                                return;
                            }
                            break;
                        case 'wms':
                            // 库存，遍历勾选的物品组装
                            searchBox.find('tr').each(function () {
                                var batchId = $(this).data('batch_id');
                                var inventoryId = $(this).data('inventory_id');
                                if (batchId > 0) {
                                    if ($(this).find('input').get(0).checked) {
                                        links.push({
                                            target_type: '40',
                                            target_content: JSON.stringify({
                                                inventory_id: inventoryId,
                                                batch_id: batchId,
                                            }),
                                            target_name: $(this).find('.product_name').text(),
                                            url: INVENTORY_URL + 'inventory/index?inventory_id=' + inventoryId +
                                                '&url=/storage-report/get-product-info-page&batchId=' + batchId,
                                        });
                                    }
                                }
                            });
                            if (links.length <= 0) {
                                $.showAlert(mainLang('inventory_item_empty_tip'));
                                return;
                            }
                            break;
                    }
                    var linkBox = $('.exp_conetnt.active .wms-sample-box textarea[name="wms_samples"]');
                    var currentLinks = JSON.parse(linkBox.val());

                    for (var l = 0; l < links.length; l++) {
                        var link = links[l];
                        var linkIndex = -1;
                        for (var c = 0; c < currentLinks.length; c++) {
                            var currLink = currentLinks[c];
                            if (link.target_type == currLink.target_type && link.target_content == currLink.target_content) {
                                linkIndex = c;
                                currentLinks[c].target_name = link.target_name;
                                break;
                            }
                        }
                        if (linkIndex < 0) currentLinks.push(link);
                    }
                    linkBox.val(JSON.stringify(currentLinks));
                    loadSampleList();
                    $('.add-link-pop').parents('.pop_modal').modal('hide');
                });
            },
        })
    })

    // 删除样品
    $('body').on('click', '.wms-sample-box .delete-link', function () {
        var delTip = '<div class="del-link-tip">' + mainLang('del_sample_tip') + '</div>';
        var linkItem = $(this).closest('.exp-link-item');
        $.popContent(delTip, mainLang('del_sample'), function () {
            var linkValBox = $('.exp_conetnt.active .wms-sample-box textarea[name="wms_samples"]');
            var currentLinks = JSON.parse(linkValBox.val());
            currentLinks.splice(linkItem.data('sample_index'), 1);
            linkValBox.val(JSON.stringify(currentLinks));
            loadSampleList();
            $('.del-link-tip').parents('.pop_modal').modal('hide');
        })
    })

    var expTool = {
        //字母筛选
        exp_letter: function () {
            expLetter.init();
        },

        //弹出组件选择框
        showmodule: function () {
            if ($('.exp_conetnt.active .add_module_body').length == 0) {
                var temp = _.template(require('text!module/add_module.html'))();
                $('.exp_conetnt.active .tool_data_box').append(temp);
                $('.exp_conetnt.active .add_module_body').show().trigger('show');
            } else {
                if ($('.exp_conetnt.active .add_module_body').is(':visible')) {
                    $('.exp_conetnt.active .add_module_body').hide();
                } else {
                    $('.exp_conetnt.active .add_module_body').show().trigger('show');
                }
            }
        },

        log: function () {
            log.type = null;
            log.gteTabData();
        },

        share: function (dom) {
            var id = chooseId;
            require(['share_exp'], function (share) {
                share(id, dom, 'exp');
            });
        },

        revoke: function (dom) {
            const html = '<div class="input_part">' +
                    '<label class="iblock vertical-top" style="width: 70px; line-height: 30px;">' +
                    '<span style="color: red; vertical-align: middle">*</span>' +
                    mainLang('reason') +
                    '</label>' +
                    '<textarea ' +
                    'class="angle_input iblock pop_input_con reason-textarea" ' +
                    'style="width: 350px;" ' +
                    'id="revoke_sign" ' +
                    'name="text">' +
                    '</textarea>' +
                '</div>';

            $.popContent(html, mainLang('cancle_sign'), function () {
                var revoke_sign_reason = $('#revoke_sign').val().trim()
                if (revoke_sign_reason == '') {
                    $.showAlert(mainLang('pls_input_reason'))
                    return
                } else {
                    $.ajaxFn({
                        url: ELN_URL + '?r=sign/cancle-sign',
                        data: {
                            id: $('.exp_conetnt.active #exp_id').val(),
                            comment: revoke_sign_reason
                        },
                        success: function (res) {
                            if (res.status == 1) {
                                $.showAlert(mainLang('cancle_sign_success'));
                                require('tab').reloadActiveTag();
                            }
                        }
                    });
                }
            });
        },

        getUserList: function (expType) {
            var url, data = {};
            if (expType == 'shareMe') { // 分享给我的实验
                url = ELN_URL + '?r=share/list-user-by-user';
            } else {
                url = ELN_URL + '?r=share/list-user';
            }
            var data = {};
            var book = $('.exp_href.on[data-type="group"]');
            if (book.length > 0) {
                data.book_id = book.data('id');
            }
            $.ajaxFn({
                //cansend: true,
                data: data,
                url: url,
                success: function (data) {
                    var html = '<option value="-1">' + mainLang('select') + '</option>';
                    if (data.status == 1) {
                        for (var i = 0; i < data.data.length; i++) {
                            html += '<option value="' + data.data[i].id + '">' + data.data[i].real_name + '-' + data.data[i].name + '</option>';
                        }
                        $('.share_exp_list select[data-selecetype="user"]').html(html);
                    }
                }
            }, $('.tool_nav'))
        },

        getBookList: function (expType) {
            var url, data = {};
            if (expType == 'shareMe') { // 分享给我的实验 记录本列表
                url = ELN_URL + '?r=share/list-book-by-user';
            } else {
                url = ELN_URL + '?r=share/list-book-to-user';
            }
            var uid = $('.share_exp_list select[data-selecetype="user"]').val();
            if (!parseInt(uid) || parseInt(uid) < 0) {
                return;
            }

            var book = $('.exp_href.on[data-type="group"]');
            if (book.length > 0) {
                data.book_id = book.data('id');
            }

            $.ajaxFn({
                url: url,
                data: {
                    user_id: uid
                },
                success: function (data) {
                    var html = '<option value="-1">' + mainLang('select') + '</option>';
                    if (data.status == 1) {
                        for (var i = 0; i < data.data.length; i++) {
                            html += '<option value="' + data.data[i].id + '">' + data.data[i].name + '</option>';
                        }
                        $('.share_exp_list select[data-selecetype="book"]').html(html);
                    }
                }
            }, $('.tool_nav'))
        },

        getData: function (dom) {
            var chooseId = [];
            expIds = [];
            if ($('.exp_conetnt.active .exp_list_table:visible').length > 0) {
                $('.exp_conetnt.active .checkbox:checked').each(function () {
                    chooseId.push($(this).parents('tr').data('id'));
                    expIds.push($(this).parents('tr').attr('data-exp_id'));
                });
            } else {
                window.expId = $('.exp_conetnt.active #exp_id').val();  // modified by hkk 2020/6/18
                chooseId = [$('.exp_conetnt.active #exp_id').val()];  // modified by hkk 2020/6/18
            }
            return chooseId;
        },

        //保存实验 、模板
        save: function (dom) {

            var btn = $(".click_btn[data-type=save]:visible");
            var cls = 'save_disabled';
            //防止遮罩还没出来时，获取数据中时点击按钮。
            //卡住的时候点了按钮，后面也会执行。加入的消息队列的时间。
            //$(".tool_btn[data-type=save]").trigger('click');$(".tool_btn[data-type=save]").trigger('click');如果这样执行，跟实际点击也是有差异的。这样依然会发多次请求。
            if (btn.hasClass(cls)) {
                return;
            }

            require(['save_exp'], function (save_exp) {

                var time = 0;

                //这里的判断的用意：
                //如果有indraw，且编辑器中的内容有修改，那么点击这个按钮，会触发加载物料信息的请求，
                //所以保存要等待那个请求结束之后才执行。那个请求结束之后会发布一个showMateriel的自定义事件。
                //ls20171013 在indraw没加载好的情况下，应该默认isSameMol为true。默认结构式没有被修改。

                if ($('.exp_conetnt.active .chendraw iframe')[0] && $('.exp_conetnt.active .chendraw iframe')[0].contentWindow && $('.exp_conetnt.active .chendraw iframe')[0].contentWindow.indraw && !$('.exp_conetnt.active .chendraw iframe')[0].contentWindow.indraw.isSameMol && $('.exp_conetnt.active .chendraw iframe')[0].contentWindow.indraw.getMultiMol()) { //有结构式 并且 已经改变 并且值不为空
                    $('body').off('showMateriel').on('showMateriel', function () {
                        //暂时不能点
                        $.inOutClass(btn, cls, time);
                        save_exp.save();
                        $('body').off('showMateriel');
                    });
                } else {
                    $.inOutClass(btn, cls, time);
                    save_exp.save();
                    $('body').off('showMateriel');
                }
            })
        },

        savemodule: function (dom) {
            require(['save_exp'], function (save_exp) {
                save_exp.save({type: 'module', dom: dom});
            })
        },

        saveasmodule: function (dom) {
            var that = this;
            $.ajaxFn({
                url: ELN_URL + '?r=group/list-temp-group',
                success: function (data) {
                    if (data.status == 1) {
                        var groupHtml = that.getGroupHtml(data.data);

                        if ($('.save_name_modal.modal').length == 0) {
                            var temp = _.template(require('text!popup/save_module_name.html'))({groupHtml: groupHtml});
                            $('body').append(temp);
                        }
                        ;
                        $('.save_name_modal.modal').modal('show');

                    }
                }
            });

            $('body').off('click.save_name_modal').on('click.save_name_modal', '#save_name_modal_submit', function () {
                require(['save_exp'], function (save_exp) {
                    save_exp.save({type: 'module', dom: dom});
                });
            })
        },

        pdf: function (dom) {
            if (window.pdfDown.pdfExporting) {
                $.showAlert(mainLang('file_exporting'));
                return;
            }
            console.log('导出pdf加弹窗');
            $.ajaxFn({
                url: ELN_URL + '?r=setting/set-pdf-export',
                data: {},
                success: function (data) {
                    if (data.status == 1) {
                        $(".share-setting").remove();
                        $("body").append(data.data.file);
                        $(".share-setting").modal('show');

                        // console.log($(".top-group-tabs .on").data("id")+":::"+$(".opt-more span[cMethod='share_setting']").data("userid"));
                    }
                }
            });
            return false;


        },

        //设置实验核查人员
        submit_export_pdf: function (btn) {
            var paper_size = $("#paper_size").val();
            var marge_pdf = $('input[name="marge_pdf"]:checked').val();
            var ids
            if ($('.get_exp_ids:visible').length > 0) {
                ids = $('.get_exp_ids').data('ids');
            } else {
                ids = chooseId
            }
            let operate_from = $('.get_exp_ids').data('operate_from') || '';//导出操作来源
            $.closeModal();
            require(['wordPdf'], function (WordPdf) {
                config.wordPdf = config.wordPdf ? config.wordPdf : new WordPdf();
                //config.wordPdf.init(ids, 'exp', 'pdf', paper_size, marge_pdf, );
                config.wordPdf.newInit({
                        id: ids,
                        type: 'exp',
                        wordOrPdf: 'pdf',
                        paper_size: paper_size,
                        marge_pdf: marge_pdf,
                        export_module: null,
                        operate_from: operate_from
                    }
                );
            })
        },


        word: function (selector, ids) {
            if (window.pdfDown.pdfExporting) {
                $.showAlert(mainLang('file_exporting'));
                return;
            }
            var expIds = (ids && ids.length > 0) ? ids : chooseId;
            $.ajaxFn({
                url: ELN_URL + '?r=setting/set-word-export',
                data: {
                    id: expIds
                },
                success: function (data) {
                    if (data.status == 1) {
                        $(".share-setting").remove();
                        $("body").append(data.data.file);
                        $(".share-setting").modal('show');
                        // console.log($(".top-group-tabs .on").data("id")+":::"+$(".opt-more span[cMethod='share_setting']").data("userid"));
                    }
                }
            });
            return false;

        },

        // qiuhao ai tool按钮触发
        ai: function (selector, ids) {
            if (ENABLE_AI == 0) { // AI功能未开启
                //没有开通AI功能弹窗
                var html = '<p  style="margin: 15px 0;font-size: 16px; ">' + mainLang('tip-line1') + '</p>' +
                    '<ul><li style="font-size: 14px;">' + mainLang('tip-line2') + '<br> <span >' + mainLang("tip-line3") +
                    '</span></li>  <li style="font-size: 14px; margin-top: 10px;"> ' + mainLang('tip-line4') + '<br> <span>' + mainLang('tip-line5') +
                    '</span></li> </ul> <p  style="margin: 15px 0;font-size: 16px; ">' + mainLang('tip-line6') + '</p>';
                $.popContent(html, mainLang('tip-title'), function () {
                }, function () {
                }, false);
            } else {
                var expIds = (ids && ids.length > 0) ? ids : chooseId;
                if (expIds && expIds.length == 0) {
                    return $.showAlert(mainLang('select_exp'));
                }
                if (expIds && expIds.length > 5) {
                    return $.showAlert(mainLang('exceed_experiment_message'));
                }
                $.ajaxFn({
                    url: '?r=experiment/get-reference-context-for-ai',
                    type: 'post',
                    data: {
                        experiment_ids: expIds,
                    },
                    success: function (data) {
                        if (data.status == 1) {
                            require(['/ai-chat/ai-chat-lib.umd.js'], function (aiChat) {
                                $.loadCss('/ai-chat/style.css');
                                aiChat.setupApp('#ai-chat-box', {
                                    referenceContext: JSON.stringify(data.data),
                                });
                            });
                        }
                    }
                });
            }
            return false;
        },


        submit_export_word: function (btn) {
            var paper_size = 'A4';
            var marge_pdf = $('input[name="marge_pdf"]:checked').val();
            var export_module = $('input[name="export_module"]:checked').val();
            var expids = btn.attr('expids');
            var export_module_arr = {};
            if (export_module == 2) {
                var expidsArr = expids.split(",");
                expidsArr.forEach((item) => {
                    var item_arr = [];
                    $("input[name='exp_module_" + item + "']").each(function () {
                        if ($(this).is(":checked")) {
                            item_arr.push($(this).val());
                        }
                    });
                    console.log(item_arr);
                    export_module_arr[item] = item_arr;
                    console.log(export_module_arr[item]);
                });
                console.log(export_module_arr);
                export_module = JSON.stringify(export_module_arr);
                console.log(export_module);

            } else {
                export_module = 0;
            }
            var ids
            if ($('.get_exp_ids').length > 0) {
                ids = $('.get_exp_ids').data('ids');
            } else {
                ids = chooseId
            }
            let operate_from = $('.get_exp_ids').data('operate_from') || '';//导出操作来源
            $.closeModal();
            require(['wordPdf'], function (WordPdf) {
                config.wordPdf = config.wordPdf ? config.wordPdf : new WordPdf();
                //config.wordPdf.init(ids, 'exp', 'word', paper_size, marge_pdf, export_module);
                config.wordPdf.newInit({
                    id: ids,
                    type: 'exp',
                    wordOrPdf: 'word',
                    paper_size: paper_size,
                    marge_pdf: marge_pdf,
                    export_module: export_module,
                    operate_from: operate_from,
                });
            })
        },

        /**
         * 浏览器保存导出目录的勾选项
         * @param export_field
         */
        setExportMenuExcel: function (export_field) {
            var exportMenuExcelSetting = {
                export_field: JSON.stringify(export_field)
            };
            localStorage.setItem('eln_export_menu_excel_setting', JSON.stringify(exportMenuExcelSetting));
        },

        /**
         * 浏览器读取导出目录的勾选项
         * @returns {any}
         */
        getExportMenuExcel: function () {
            var exportMenuExcelSetting = JSON.parse(localStorage.getItem('eln_export_menu_excel_setting'));
            if (!exportMenuExcelSetting) {
                exportMenuExcelSetting = {
                    export_field: undefined
                };
            }
            return exportMenuExcelSetting;
        },
        excel: function (selector, ids) {
            if (window.pdfDown.pdfExporting) {
                $.showAlert(mainLang('file_exporting'));
                return;
            }
            var expIds = (ids && ids.length > 0) ? ids : chooseId;
            var last_checked_export_field = this.getExportMenuExcel().export_field;

            $.ajaxFn({
                url: ELN_URL + '?r=setting/set-excel-export',
                data: {
                    id: expIds,
                    last_checked_export_field: last_checked_export_field
                },
                success: function (data) {
                    if (data.status == 1) {
                        $('.modal.set-excel-export').remove();
                        $("body").append(data.data.file);
                        $('.modal.set-excel-export').modal('show');
                    }
                }
            });
            return false;
        },

        submit_export_excel: function (btn) {

            // var expids = btn.attr('expids');
            var export_field = [];

            var fieldList = $('.set-excel-export .exp-list-cols .col-item input');
            fieldList.each(function () {
                if ($(this).prop('checked')) {
                    var name = $(this).prop('name');
                    export_field.push(name);
                }
            });
            if (export_field.length === 0) {
                $.showAlert(mainLang('select'));
                return;
            }

            // 浏览器保存导出目录的勾选项
            this.setExportMenuExcel(export_field);

            var ids;
            if ($('.get_exp_ids').length > 0) {
                ids = $('.get_exp_ids').data('ids');
            } else {
                ids = chooseId
            }
            let operate_from = $('.get_exp_ids').data('operate_from') || '';//导出操作来源

            // 开始导出
            $.closeModal();
            require(['wordPdf'], function (WordPdf) {
                config.wordPdf = config.wordPdf ? config.wordPdf : new WordPdf();
                config.wordPdf.newInit({
                    id: ids,
                    type: 'exp',
                    wordOrPdf: 'excel',
                    paper_size: undefined,
                    marge_pdf: undefined,
                    export_module: undefined,
                    export_field: JSON.stringify(export_field),
                    operate_from: operate_from,
                });
            })
        },

        //        print: function(dom) {
        //            console.log('导出pdf加弹窗');
        //            $.ajaxFn({
        //                url: ELN_URL + '?r=setting/set-pdf-view',
        //                data: {
        //
        //                },
        //                success: function (data) {
        //                    if (data.status == 1) {
        //                        $(".share-setting").remove();
        //                        $("body").append(data.data.file);
        //                        $(".share-setting").modal('show');
        //                        // console.log($(".top-group-tabs .on").data("id")+":::"+$(".opt-more span[cMethod='share_setting']").data("userid"));
        //                    }
        //                }
        //            });
        //            return false;
        //
        //        },

        //PDF预览
        print: function (dom) {
            $.closeModal();
            var paper_size = 'A4';
            var marge_pdf = 1;
            require(['print'], function (print) {
                print.init(chooseId, paper_size, marge_pdf);
            })
        },
        //ls　20180705　统一一下这个鹰群选择的html渲染。
        getGroupHtml: function (data) {
            if (data.length == 0) {
                groupHtml = '<div class="no-group-wrap">' + mainLang('no group') + '</div>'
            } else {
                var groupHtmlArr = data.map(function (v, i) {
                    return '<div class="check">' +
                        '    <input class="middle mr5" type="checkbox" check_id="' + v.group_id + '" check_name="' + v.group_name + '">' +
                        '    <span class="check-name check_name">' + v.group_name + '</span>' +
                        '</div>';
                });
                groupHtml = groupHtmlArr.join('');
            }
            return groupHtml;
        },

        // 新建模板
        addmodule: function () {

            $.closeModal(); // add by hkk 2020/6/30

            var that = this;
            $.ajaxFn({
                url: ELN_URL + '?r=group/list-temp-group',
                success: function (data) {
                    if (data.status == 1) {
                        var groupHtml = that.getGroupHtml(data.data);
                        var html =
                            '<div class="copy_module_box">' +

                            '<div class="info_part mudole_info_part">' +
                            '   <label class="body_left">' + mainLang('group select') + '：</label>' +
                            '    <div class="data-box-ineln iblock">' +
                            '        <div class="data-box iblock">' +
                            '            <div class="view-checked-box checkbox_box">' +
                            '                <input class="input pr34 name-box check_input mouse check_department" name="group_ids" idbox="" type="text" value="" placeholder="' + mainLang('please select groups') + '" readonly="">' +
                            '                <span class="font-ico clear-ico clear_check_btn" style="display: none;"></span>' +
                            '                <div class="checked-box hide bottom-set" style="display: none;"> ' +
                            '                    <div class="checked-list-box">' +
                            '                    </div>' +
                            '                </div>' +
                            '                <div class="check-box checkbox_box hide" >' +
                            '                    <div class="check-search-box pl10 pr10">' +
                            '                        <input type="checkbox" class="checkbox_all middle">' +
                            '                        ' + mainLang('select_all') +
                            '                        <div style="width: 205px; float: right;">' +
                            '                            <input type="text" placeholder="' + mainLang('search') + '" class="input search-check-key search_check_key middle">' +
                            /* <button class="search-check-btn search_check_btn font-ico search-ico"></button>' + */
                            '                        </div>' +
                            '                    </div>' +
                            '                    <div class="check-list-box check_list_box">' + groupHtml +
                            '                    </div>' +
                            '                </div>' +
                            '            </div>' +
                            '        </div>' +
                            '    </div>' +
                            '    <span style="position: relative; ">\n' +
                            '                            <span class="iblock relative tip-box mt10 ajax en_hide" help_id="1" style="z-index: 1;left:-40px;top: -25px">\n' +
                            '                                 <i class="tip-ico mouse" style="margin-top:-15px"></i>\n' +
                            '                                 <div class="tip-con big-tip-con" style="top: 50px;left: 120px; right: auto; display: none;">\n' +
                            '                                 <i style="right: auto; left: 20px;"></i>\n' +
                            '                                 <p>' + mainLang('select　ok groups') + '</p>\n' +
                            '                                </div>\n' +
                            '                             </span>\n' +
                            '                        </span>' +
                            '</div>' +

                            '<div class="info_part mudole_info_part mt20">' +
                            '<label class="body_left">' + mainLang('temp_title') + '：</label>' +
                            '<input type="text" placeholder="' + mainLang('input_temp') + '" data-text="' + mainLang('new_temp') + '" class="save_modal_name default" name="title" maxLength="64">' +
                            '</div><br>' +
                            '<div class="info_part mudole_info_part">' +
                            '<label class="body_left">' + mainLang('temp_desc') + '：</label>' +
                            '<textarea type="text" maxlength="200" name="keywords" class="angle_input save_modal_describe iblock" value=""></textarea>' +
                            '</div> ' +
                            '</div>';

                        $.popContent(html, mainLang('new_temp'), function () {

                            var group_ids = $('.copy_module_box .data-box-ineln input[name=group_ids]:visible').attr('idbox') || '';
                            if ($.trim(group_ids) == '') {
                                $.showAlert(mainLang('please select groups'));
                                return;

                            } else {
                                group_ids = group_ids.split(',');
                            }

                            if ($.trim($('.copy_module_box .save_modal_name').val()) === '') {
                                $.showAlert(mainLang('must_temp_name'));
                                return;
                            }

                            $.ajaxFn({
                                url: ELN_URL + '?r=template/add-temp',
                                data: {
                                    insertData: {
                                        name: $('.copy_module_box .save_modal_name').val(),
                                        descript: $('.copy_module_box .save_modal_describe').val(),
                                        group_ids: group_ids,
                                        tfrom: 2,
                                        type: 1
                                    }
                                },
                                success: function (data) {
                                    if (data.status == 1) {
                                        var id = data.data.temp_id;
                                        require('get_html').genExpTempPage(id); // add by hkk 2020/6/28 走新标签路径
                                        $.closeModal();

                                        /* var id = data.data.temp_id;
                                         var url = '/?r=template/view-temp&id=' + id + '&temp_type=1';

                                         $.ajaxFn({
                                             type: 'GET',
                                             url: url,
                                             success: function(data) {
                                                 if (data.status == 1) {
                                                     $('.my_exp_list').removeClass('on');
                                                     var data = data.data;
                                                     $('.layout_right_box .exp_conetnt').html(data.expFile);
                                                     var name = data.moduleName;

                                                     /!*处理标题*!/
                                                     $('.my_exp_list, .my_exp_detial').removeClass('on');
                                                     var currentTab = $('.my_exp_detial.module[data-id="' + id + '"]');
                                                     if (currentTab.length > 0) {
                                                         currentTab.addClass('on');
                                                     } else {
                                                         $('.exp_title').append('<a class="iblock on my_exp_detial module" type="module" data-id="' + id + '"><span class="name">' + name + '</span><span class="close"></span></a>');
                                                     }
                                                     handleExpTitle();
                                                     $('.exp_main').show();
                                                     $('.bg_white').empty();

                                                     $('.click_module_tool').attr('data-id', id);
                                                     require(['exp_detial'], function(exp_detial) {
                                                         exp_detial();
                                                     });
                                                     //$(window).scrollTop(0);
                                                 }
                                             }
                                         })*/
                                    }
                                }
                            });
                        });
                    }
                }
            });
        },

        // 新建模板
        addsonmodule: function () {
            var html = '<div class="copy_module_box">' +
                '<div class="info_part mudole_info_part">' +
                '   <label class="body_left">' + mainLang('temp_title') + '：</label>' +
                '   <input type="text" placeholder="' + mainLang('input_temp') + '" class="save_modal_name default">' +
                '</div></div>';
            $.popContent(html, mainLang('add_son_module'), function () {
                var tempName = $.trim($('.copy_module_box .save_modal_name').val());
                if (tempName === '') {
                    $.showAlert(mainLang('must_temp_name'));
                    return;
                }

                $.ajaxFn({
                    url: ELN_URL + '?r=template/add-sub-temp',
                    data: {
                        insertData: {
                            name: tempName
                        },
                        tempReal: {
                            component_id: 2
                        }
                    },
                    success: function (res) {
                        if (res.status === 1) {
                            $.closeModal();
                            require('get_html').genSubTempPage(res.data.id); // add by hkk 2020/6/28 走新标签路径
                            //expTool.viewsonmodule(res.data.id)
                            /*if (res.data.need_approval) {
                                require(['template_list'], function (temp) {
                                    temp.submitAuditConfirm(res.data.id, true);
                                });
                            }*/
                        }
                    }
                });
            });
        },

        // 模板列表里 查看子模版
        viewsonmodule: function (id) {
            $.ajaxFn({
                url: ELN_URL + '?r=template/view-sub-temp',
                type: 'GET',
                data: {
                    id: id
                },
                success: function (res) {
                    if (res.status == 1) {
                        $('.exp_data_box').html(res.data.html);
                        $('.tool_data_box').html('');

                        $('.exp_title a.on').removeClass('on');
                        var findTab = $('.my_exp_detial.sub_temp[data-sub-temp="' + id + '"]');
                        if (findTab.length !== 0) {
                            findTab.addClass('on');
                        } else {
                            var tabName = res.data.name;
                            var newTab = '<a class="iblock on my_exp_detial sub_temp" data-sub-temp="' + id + '" title="' + tabName + '"><span class="name">' + tabName + '</span><span class="close"></span></a>'
                            $('.exp_title').append(newTab);
                        }

                        handleExpTitle();
                        $.closeModal();
                        $.closeLoading();
                    }
                }
            });
        },

        savesonmodule: function ($btn, autosave) {
            var tempName = $.trim($('.exp_conetnt.active .son_temp_name').val());
            var tempDesc = $.trim($('.exp_conetnt.active .son_temp_desc').val());
            if (tempName === '') {
                $.showAlert(mainLang('must_temp_name'));
                return;
            }

            var content = '';
            var id = $('.exp_conetnt.active .tinymce_textarea').attr('id');
            var editorObj = UE.getEditor(id);
            if (editorObj) {
                content = editorObj.getContent();
            }

            // 压缩数据
            var pako = require('pako1/pako');
            var zipContent = pako.gzip(JSON.stringify(content), {to: 'string', level: 6});
            zipContent = btoa(zipContent);

            $.ajaxFn({
                url: ELN_URL + '?r=template/save-sub-temp',
                noLoad: !!autosave,
                data: {
                    id: $btn.attr('data-id'),
                    descript: tempDesc,
                    name: tempName,
                    zip_content: zipContent,
                },
                success: function (res) {
                    if (res.status == 1) {
                        if (!autosave) {
                            if (res.data.need_approval) {
                                require(['template_list'], function (temp) {
                                    temp.submitAuditConfirm($btn.attr('data-id'), true, true);
                                });
                            } else {
                                $.showAlert(res.data.message || mainLang('save_success'));
                            }
                        }
                    } else {
                        $.showAlert(res.data.message);
                    }
                }
            });
        },

        // 保存InTable方法模板
        saveIntableTemp: function ($btn) {
            var tempName = $.trim($('.exp_conetnt.active .son_temp_name').val());
            var temDesc = $.trim($('.exp_conetnt.active .son_temp_desc').val());
            var tempId = $btn.attr('data-id');
            var $intable = $btn.parents('.exp_conetnt').find('.intable-iframe');
            if ($intable) {
                var xs = $intable[0].contentWindow.xs;
                if (xs) {
                    // bug35767 保存intable模板要加密
                    var content = xs.sheet.data.getData();
                    content = JSON.stringify(content);
                    var pako = require('pako1/pako');
                    content = btoa(pako.gzip(content, {to: 'string', level: 3}));
                    var img = xs.sheet.getCanvasImg();

                    $.ajaxFn({
                        url: ELN_URL + '?r=template/save-sub-temp',
                        data: {
                            id: tempId,
                            name: tempName,
                            descript: temDesc,
                            content: content,
                            img: img,
                        },
                        success: function (res) {
                            if (res.status == 1) {
                                if (res.data.need_approval) {
                                    require(['template_list'], function (temp) {
                                        temp.submitAuditConfirm(tempId, true, true);
                                    });
                                }
                                $.showAlert(res.data.message || mainLang('save_success'));
                            }
                        }
                    });
                } else {
                    // 新intable
                    const getIntableData = $intable[0].contentWindow.getIntableData;
                    if (getIntableData != null) {
                        var content = getIntableData();
                        // 因为是模板，所以只取第一个sheet的data
                        content = JSON.stringify(JSON.parse(content).data[0]);
                        var pako = require('pako1/pako');
                        content = btoa(pako.gzip(content, {to: 'string', level: 3}));
                        var img = $intable[0].contentWindow.getPreviewImg();

                        $.ajaxFn({
                            url: ELN_URL + '?r=template/save-sub-temp',
                            data: {
                                id: tempId,
                                name: tempName,
                                descript: temDesc,
                                content: content,
                                img: img,
                            },
                            success: function (res) {
                                if (res.status == 1) {
                                    if (res.data.need_approval) {
                                        require(['template_list'], function (temp) {
                                            temp.submitAuditConfirm(tempId, true, true);
                                        });
                                    }
                                    $.showAlert(res.data.message || mainLang('save_success'));
                                }
                            }
                        });
                    }
                }
            }
        },

        batchaddsonmodule: function (btn) {
            $.ajaxFn({
                url: ELN_URL + '?r=template/batch-add-son-temp',
                data: {},
                success: function (data) {
                    if (data.status == 1) {
                        $(".share-setting").remove();
                        $("body").append(data.data.file);
                        $(".share-setting").modal('show');
                    }
                }
            });
        },

        // 新建模板
        addsonmodulesubmit: function () {
            var data = {};
            var tempReal = {};
            data.name = $('#title').val();
            var editorObj = UE.getEditor('add_son_temp');
            data.content = editorObj.getContent();
            ;
            tempReal.component_id = 2;
            $.ajaxFn({
                url: ELN_URL + '?r=template/add-sub-temp',
                data: {
                    insertData: data,
                    tempReal: tempReal
                },
                success: function (data) {
                    if (data.status == 1) {
                        $.showAlert(data.data.message || mainLang('save_success'));
                        if (res.data.need_approval) {
                            require(['template_list'], function (temp) {
                                temp.submitAuditConfirm(res.data.id, true);
                            });
                        }
                    }
                }
            })
        },

        //复制模板
        copymodule: function (dom) {
            var that = this;
            $.ajaxFn({
                url: ELN_URL + '?r=group/list-temp-group',
                success: function (data) {
                    if (data.status == 1) {

                        var groupHtml = that.getGroupHtml(data.data);
                        var html = '<div class="copy_module_box">' +


                            '<div class="info_part mudole_info_part">' +
                            '   <label class="body_left">' + mainLang('group select') + '：</label>' +
                            '    <div class="data-box-ineln iblock">' +
                            '        <div class="data-box iblock">' +
                            '            <div class="view-checked-box checkbox_box">' +
                            '                <input class="input pr34 name-box check_input mouse check_department" name="group_ids" idbox="" type="text" value="" placeholder="' + mainLang('please select groups') + '" readonly="">' +
                            '                <span class="font-ico clear-ico clear_check_btn" style="display: none;"></span>' +
                            '                <div class="checked-box hide bottom-set" style="display: none;"> ' +
                            '                    <div class="checked-list-box">' +
                            '                    </div>' +
                            '                </div>' +
                            '                <div class="check-box checkbox_box hide" >' +
                            '                    <div class="check-search-box pl10 pr10">' +
                            '                        <input type="checkbox" class="checkbox_all middle">' +
                            '                        ' + mainLang('select_all') +
                            '                        <div class="" style="width: 205px;float: right;">' +
                            '                            <input type="text" placeholder="' + mainLang('search') + '" class="input search-check-key search_check_key middle">' +
                            //// '                            <button class="search-check-btn search_check_btn font-ico search-ico"></button>' +
                            '                        </div>' +
                            '                    </div>' +
                            '                    <div class="check-list-box check_list_box">' + groupHtml +
                            '                    </div>' +
                            '                </div>' +
                            '            </div>' +
                            '        </div>' +
                            '    </div>' +
                            '    <i class="ask-ico font-ico hover-show-info"><div style="width:340px;height:auto;line-height: 20px;font-size: 11px;white-space: normal;">' + mainLang('select　ok groups') + '</div></i>' +
                            '</div>' +


                            '<div class="info_part mudole_info_part mt20">' +
                            '<label class="body_left">' + mainLang('temp_title') + '：</label>' +
                            '<input type="text" placeholder="' + mainLang('input_temp') + '" data-text="' + mainLang('new_temp') + '" class="save_modal_name default" name="title" maxLength="64">' +
                            '</div><br>' +
                            '<div class="info_part mudole_info_part">' +
                            '<label class="body_left">' + mainLang('temp_desc') + '：</label>' +
                            '<textarea type="text" maxlength="200" name="keywords" class="angle_input save_modal_describe iblock" value=""></textarea>' +
                            '</div> ' +
                            '</div>';

                        $.popContent(html, mainLang('duplic_temp'), function () {


                            var group_ids = $('.copy_module_box .data-box-ineln input[name=group_ids]:visible').attr('idbox') || '';
                            if ($.trim(group_ids) == '') {
                                $.showAlert(mainLang('please select groups'));
                                return;

                            } else {
                                group_ids = group_ids.split(',');
                            }


                            $.ajaxFn({
                                url: ELN_URL + '?r=template/duplic-temp',
                                data: {
                                    temp_id: [dom.attr('data-id')],
                                    name: $('.copy_module_box .save_modal_name').val(),
                                    descript: $('.copy_module_box .save_modal_describe').val(),
                                    group_ids: group_ids
                                },
                                success: function (data) {
                                    if (data.status == 1) {
                                        $.closeModal();
                                        $.showAlert(mainLang('duplic_success'));


                                        require('get_html').genExpTempPage(data.data); // add by hkk 2020/6/23 走新标签路径

                                        /*   var id = data.data;
                                           var url = '/?r=template/view-temp&id=' + id + '&temp_type=1';
                                           var currentTab = $('.my_exp_detial.module[data-id="' + id + '"]');
                                           //检测标签
                                           if (!checkTopTab(currentTab)) {
                                               return;
                                           }
                                           */
                                        /*$.ajaxFn({
                                            type: 'GET',
                                            url: url,
                                            success: function(data) {
                                                if (data.status == 1) {
                                                    $('.my_exp_list').removeClass('on');
                                                    var data = data.data;
                                                    $('.layout_right_box .exp_conetnt').html(data.expFile);
                                                    var name = data.moduleName;

                                                    /!*处理标题*!/
                                                    $('.my_exp_list, .my_exp_detial').removeClass('on');
                                                    var currentTab = $('.my_exp_detial.module[data-id="' + id + '"]');
                                                    if (currentTab.length > 0) {
                                                        currentTab.addClass('on');
                                                    } else {
                                                        $('.exp_title').append('<a class="iblock on my_exp_detial module" type="module" data-id="' + id + '"><span class="name">' + name + '</span><span class="close"></span></a>');
                                                    }
                                                    handleExpTitle();
                                                    $('.exp_main').show();
                                                    $('.bg_white').empty();

                                                    $('.click_module_tool').attr('data-id', id);
                                                    require(['exp_detial'], function(exp_detial) {
                                                        exp_detial();
                                                    });
                                                    //$(window).scrollTop(0);
                                                }
                                            }
                                        })*/
                                    }
                                }
                            });
                        });
                    }
                }
            });


        },

        //设置必填项弹出层
        setrequiremodule: function (btn) {
            $.ajaxFn({
                url: ELN_URL + '?r=template/set-require',
                data: {
                    temp_id: $('.exp_conetnt.active #temp_id').val(),
                },
                success: function (data) {
                    if (data.status == 1) {
                        $(".print-settings").remove();
                        // if ($(".print-settings").length < 1) {
                        $("body").append(data.data);
                        // }
                        $(".print-settings h4 span").attr({
                            "dataUserId": btn.attr("data-userid"),
                            "dataGroupId": btn.parents("td").attr("group_id")
                        })
                        $(".print-settings h4 span").text(btn.parents("tr").find("td").eq(1).text());

                        $(".print-settings").modal('show');
                        $(".print-settings .modal-dialog").width('900px');
                    }
                }
            })

        },

        //设置必填项弹出层
        setstructdatamodule: function (btn) {
            $.ajaxFn({
                url: ELN_URL + '?r=template/set-struct-data',
                data: {
                    temp_id: $('.exp_conetnt.active #temp_id').val(),
                },
                success: function (data) {
                    if (data.status == 1) {
                        $(".print-settings").remove();
                        // if ($(".print-settings").length < 1) {
                        $("body").append(data.data);
                        // }
                        $(".print-settings h4 span").attr({
                            "dataUserId": btn.attr("data-userid"),
                            "dataGroupId": btn.parents("td").attr("group_id")
                        })
                        $(".print-settings h4 span").text(btn.parents("tr").find("td").eq(1).text());
                        $(".print-settings").modal('show');
                        $(".print-settings .modal-dialog").width('900px');
                    }
                }
            })

        },

        // 修订设置弹窗
        getRevisionSetting: function () {
            $.ajaxFn({
                url: ELN_URL + '?r=template/get-revision-setting',
                type: 'GET',
                data: {
                    template_id: $('.exp_conetnt.active #temp_id').val()
                },
                success: function (res) {
                    if (res.status == 1) {
                        $('.revision_setting').remove();
                        $('body').append(res.data.html);
                        $('.revision_setting').modal('show');
                    }
                }
            });
        },

        // 撤销模板审核
        cancelTemplateAudit: function () {
            var tempId = $('.exp_conetnt.active #temp_id').val();
            require(['template_list'], function (temp) {
                temp.cancelAudit(tempId, function () {
                    require('tab').reloadActiveTag();
                });
            })
        },

        startTemplateEdit: function () {
            var tempId = $('.exp_conetnt.active #temp_id').val();
            require(['template_list'], function (temp) {
                temp.startEdit(tempId, function () {
                    require('tab').reloadActiveTag();
                });
            });
        },


        //复制实验
        copy: function (dom) {
            var _expId = expId;
            //如果是disabled,不执行操作。只有选择的了一个的时候可以选择。
            //如果点了分页，那个这个disabled也是应该干掉的。
            if (dom.hasClass('disabled')) {
                return;
            }
            var bookHtml = '<span>' + mainLang('select_book') + '：</span><div class="copy_book_list1 form-control iblock">';
            //表示是在列表页面，需要从列表中拿选取的实验的id。
            if ($(".exp_list_table:visible").length > 0) {
                var $trDom = $('.exp_conetnt.active .choose input:checked').closest('tr');
                _expId = $.trim($trDom.attr('data-id'));
            }
            $.ajaxFn({
                url: ELN_URL + '?r=duplicate/get-current-book-by-exp',
                data: {
                    exp_id: _expId,
                },
                success: function (data) {
                    if (data.status === 1) {
                        var _bookId = data.data.bookId[0];
                        var result = data.data.bookList;

                        //判断是否可以选择部分复制
                        var checkeboxDisabled = data.tmpPower == 1 ? 'disabled' : '';
                        // 增加复制选择下拉框
                        bookHtml += '<div className="copy_book_list"><select>';
                        for (var i = 0; i < result.length; i++) {
                            if (result[i].id === _bookId) {
                                bookHtml += '<option selected="selected" data-groupid="' + result[i].group_id + '" value="' + result[i].id + '">' + result[i].book_code + '(' + result[i].name + ')' + ' </option>';
                            } else {
                                bookHtml += '<option data-groupid="' + result[i].group_id + '" value="' + result[i].id + '">' + result[i].book_code + '(' + result[i].name + ')' + ' </option>';
                            }
                        }
                        bookHtml += '</select></div></div>';

                        // 增加模块选择栏
                        var moduleHtml = '<div class="duplicatedModules"><span>' + mainLang('choose_duplicated_module') + '</span>';
                        moduleHtml += '<table><tbody><tr align="center" class="duplicated_list"><td><input ' + checkeboxDisabled + ' checked type="checkbox" name="duplicatedModuleAll" id="duplicatedModuleAll"></td>'
                            + '<td>' + mainLang('modula_name') + '</td>' + '<td><input checked type="checkbox" name="duplicatedModuleContentAll" id="duplicatedModuleContentAll"><label for="duplicatedModuleContentAll">' + mainLang('copy_data') + '</label>' +
                            '</td></tr>';
                        for (var i = 0; i < data.data.modules.length; i++) {
                            var moduleName = data.data.modules[i].name;
                            var moduleId = data.data.modules[i].id; // relay_id
                            if (!moduleName) {
                                if (data.data.modules[i].component_id === '1') {
                                    moduleName = 'Indraw'
                                } else if (data.data.modules[i].component_id === '2' || data.data.modules[i].component_id === '3') {
                                    moduleName = mainLang('lite')
                                } else if (data.data.modules[i].component_id === '6') {
                                    moduleName = mainLang('comment')
                                }
                            }
                            // moduleHtml += '<li class="iblock duplicatedModule"><input type="checkbox" name="duplicatedModule" value="' + moduleId + '" checked />' + moduleName + '</li>'
                            moduleHtml += '<tr><td><input type="checkbox" name="duplicatedModule" value="' + moduleId + '" checked ' + checkeboxDisabled + ' /></td><td>' + moduleName + '</td>'
                                + '<td><input type="checkbox" name="duplicatedModuleContent" value="' + moduleId + '" checked /></td></tr>'
                        }
                        moduleHtml += '</tbody></table><div>';
                        bookHtml += moduleHtml;

                        $.showContent(null, mainLang('duplic_exp'), bookHtml, function (event) {
                            //检测标签
                            if (!checkTopTab()) {
                                return;
                            }
                            var bookId = $('.copy_book_list1 select').val();
                            var from = dom.hasClass('history') ? 'history' : 'exp';

                            var duplicatedModuleIds = [];
                            var duplicatedModuleContent = [];
                            $('.duplicatedModules input[name="duplicatedModule"]:checked').each(function () {
                                var moduleId = $(this).val();
                                duplicatedModuleIds.push(moduleId);
                                if ($(this).closest('tr').find('input[name="duplicatedModuleContent"]').prop('checked')) {
                                    duplicatedModuleContent.push(moduleId);
                                }
                            });

                            var chooseAllModules = 1;
                            if (duplicatedModuleIds.length !== $('.duplicatedModules input[name="duplicatedModule"]').length) {
                                chooseAllModules = 0;
                            }

                            $.ajaxFn({
                                url: ELN_URL + '?r=duplicate/duplic',
                                data: {
                                    id: _expId,
                                    book_id: bookId,
                                    from: from,
                                    duplicatedModuleIds: duplicatedModuleIds,
                                    chooseAllModules: chooseAllModules,
                                    duplicatedModuleContent: duplicatedModuleContent,
                                },
                                success: function (data) {
                                    if (data.status != 1) {
                                        console.log(data.info);
                                    } else {
                                        $.closeModal();
                                        var cnt = parseInt($('.my_exp_list .count').text() || '0');
                                        $('.my_exp_list .count').text(cnt + 1);
                                        $('.my_exp_list').attr('title', $.trim($('.my_exp_list').text()));
                                        var expId = data.data.id;
                                        //成功之后直接跳转到查看单个实验的界面
                                        //window.detialExp.getDetial(event, expId);
                                        getHtml.genExpPage(expId, bookId) // add by hkk 2020/6/18
                                    }
                                }
                            });
                        }, mainLang('ok'));

                    }
                    if (data.status === 0) {
                        $.showAlert(mainLang('no_auth'));
                    }
                }
            })
            if (!_expId) {
                console.log('未拿到实验id,check this:', $trDom);
            }
        },


        // 设置合著
        coauthor: function () {
            $.ajaxFn({
                url: ELN_URL + '?r=experiment/coauthor-view',
                data: {
                    exp_id: expId
                },
                success: function (res) {
                    if (res.status != 1) {
                        $.popContent(res.info, mainLang('tips'), null, null, false)
                    } else {
                        $('body').append(res.data);
                        $('.coauthor-setting').modal('show');
                    }
                }
            });
        },

        sendAjax: function (url, data, callback) {
            $.ajaxFn({
                url: url,
                data: data,
                success: function (data) {
                    if (data.status == 1) {
                        callback(data);
                    }
                }
            })
        },

        // 审批
        approval: function ($dom) {
            // 通过（action == 'agree'）还是拒绝（action == 'refuse'）
            var action = $dom.attr('data-action');
            var approval_tab_type = $('.exp_conetnt.active .approval_tab.active').attr('type');

            // 审批提交处理函数
            var approvalSubmit = function () {
                var ajaxUrl = ELN_URL + '?r=approval/agree';
                var ajaxData = {};
                var checkedExpIds = [];

                if ($dom.attr('data-bussinessType')) { // 实验详情页进行单个审批
                    ajaxData.business_type = $dom.attr('data-bussinessType');
                    ajaxData.business_id = $dom.attr('data-bussinessId');
                    checkedExpIds.push($('.exp_conetnt.active #exp_id').val());
                } else { // 审批列表页进行批量审批
                    ajaxData.approval_id_arr = chooseId;
                    checkedExpIds = expIds;
                }

                var password = $('.pop_input_password:visible').val();
                if (!$.checkVal(password, 'noempty')) {
                    $.showAlert(mainLang('pass_must'));
                    return false;
                }

                ajaxData.password = $.passwordEncipher(password);

                if (action == 'refuse') {
                    ajaxUrl = ELN_URL + '?r=approval/refuse';
                    var remark = $('.pop_input_con:visible').val().trim();
                    if (remark == '') {
                        $.showAlert(mainLang('refuse_reason_empty'));
                        return false;
                    }
                    if (remark.length > 1000) {
                        $.showAlert(mainLang('refuse_reason_exceed', [1000]));
                        return false;
                    }
                    ajaxData.remark = remark;
                }

                $.ajaxFn({
                    url: ajaxUrl,
                    data: ajaxData,
                    success: function (res) {
                        if (res.status == 1) {

                            if (res.data === 'No need deal') { // add by hkk 2022/8/9
                                $.showAlert(mainLang('no_need_deal_tip'));
                            } else {
                                $.showAlert(mainLang('success'));
                            }

                            $('.pop_modal').modal('hide');
                            if ($dom.attr('data-bussinessType')) { // 实验详情页进行单个审批->移除审批按钮
                                // require('tab').reloadActiveTag();
                            } else { // 审批列表页进行批量审批->刷新列表
                                $('.exp_conetnt.active .current.page-btn').trigger('click');
                            }

                            // 去重
                            let result = []
                            let obj = {}
                            for (const i of checkedExpIds) {
                                if (!obj[i]) {
                                    result.push(i)
                                    obj[i] = 1
                                }
                            }

                            result.forEach(function (value) {
                                require(['tab'], function (tab) {
                                    tab.closeThisTag(tab.getTag("getExpContent", [value]))
                                })
                            })
                            // bug#34403 Plug--ELN--工单-设置删除审批，然后删除工单，复核人通过审批时，弹框不会自动消失，需优化
                            $('.close').click();
                            // added by xyx 2023.10.31 bug36805 审批结束后回到对应的审批页面位置
                            // 去到审批页面
                            $('.tag[data-func="getApprovalListContent"]').click();
                            //点击搜索键
                            $('.search_wo_ico.filter-approval-list').click();
                        }
                    },

                });
            };

            if (action == 'agree') { // 通过操作界面
                var html_approval_success =
                    `<div style="margin-bottom: 10px">
                        <span style="padding-left: 54px">${mainLang('approval_success')}</span>
                    </div>`;
                var html_pass =
                    `<div class="review enter-submit-box" style="min-height: initial;">
                        <label class="body_left">${mainLang('pass')}</label>
                        <div style="display: inline-block;position: relative">
                            <input type="password" class="angle_input iblock pop_input_password password-password" autocomplete="new-password"/>
                            <input type="text" class="angle_input iblock pop_input_password password-text"/>
                            <div class="visible-password">
                                <div class="visible-password-icon visible-icon invisible-icon"></div>
                            </div>
                        </div>
                    </div>`;
                // 实验分享、新建记录本、全文模板、仪器复核 提示语隐藏
                const approval_tab_type_check = ['10', '5', '6', '9'];
                if (approval_tab_type_check.includes(approval_tab_type)) {
                    html_approval_success = '';
                }
                var html = html_approval_success + html_pass;
            } else { // 拒绝操作界面
                var html = `
                    <div class="review ">
                        <div class="input_part">
                            <label class="body_left required">${mainLang('pass')}：</label>
                            <div style="display: inline-block;position: relative">
                                <input type="password" class="angle_input iblock pop_input_password  password-password" autocomplete="new-password"/>
                                <input type="text" class="angle_input iblock pop_input_password password-text"/>
                                <div class="visible-password">
                                    <div class="visible-password-icon visible-icon invisible-icon"></div>
                                </div>
                            </div>
                        </div>
                        <div class="input_part">
                            <label class="body_left required">${mainLang('reason')}：</label>
                            <textarea class="angle_input iblock pop_input_con"></textarea>
                        </div>
                    </div>`;
            }

            // 弹出操作界面
            $.popContent(html, $dom.attr('data-title'), function () {
                approvalSubmit();
            });
        },

        viewApproval: function ($dom) {
            var approvalId = $dom.closest('tr').attr('data-approvalId');

            var html = '查看审批流程详情失败';
            $.ajaxFn({
                url: ELN_URL + '?r=approval/view',
                data: {
                    'approval_id': approvalId
                },
                success: function (res) {
                    if (res.status == 1) {
                        html = res.data.file;
                        $.popContent(html, $dom.attr('data-title'), undefined, undefined, false);
                    }
                },
            });
        },

        // 实验列表 -> 显示/隐藏列
        show_hide_columns: function ($dom) {
            var html = '<ul class="ml30 exp-list-cols clear">';
            $('.exp_conetnt.active .exp_list_table .list_title td:not(:first-child)').each(function () {
                var tdClassArr = $(this).attr('class').split(' ');
                var colFlag = '';
                for (var i = 0; i < tdClassArr.length; i++) {
                    if (tdClassArr[i].indexOf('coldh') != -1) {
                        colFlag = tdClassArr[i];
                        break;
                    }
                }
                var checked = $(this).is(':visible') ? 'checked' : '';
                html += '<li class="col-item">' +
                    '<input type="checkbox" class="beauty-checkbox-big" id="' + colFlag + '" ' + checked + '/>' +
                    '<label for="' + colFlag + '">' + $(this).text() + '</label>' +
                    '</li>';
            });

            $.popContent(html, $dom.attr('data-title'), function () {
                var $table = $('.exp_conetnt.active .exp_list_table');
                var expListCols = [];
                $('.exp-list-cols .col-item :checkbox').each(function () {
                    if ($(this).is(':checked')) {
                        $table.find('td.' + $(this).attr('id')).show();
                        expListCols.push($(this).attr('id'));
                    } else {
                        $table.find('td.' + $(this).attr('id')).hide();
                    }
                });

                // 当列表没有数据时，调整没有数据行的colspan（bug#16973）
                $emptyTr = $table.find('.page-error-tr');
                if ($emptyTr.length === 1) {
                    $emptyTr.find('td').attr('colspan', expListCols.length + 1)
                }

                localStorage.setItem('expListCols', expListCols);
                $.closeModal();
            });
        },

        // 实验列表 -> 设置提醒
        set_reminder: function ($dom) {
            var expId = chooseId.length == 1 ? chooseId[0] : undefined;
            if ($dom.attr('data-id')) {
                expId = $dom.attr('data-id');
            }

            $.ajaxFn({
                url: ELN_URL + '?r=experiment/get-reminder-setting',
                type: 'GET',
                data: {
                    experiment_id: expId
                },
                success: function (res) {
                    if (res.status != 1) {
                        console.log(res.info);
                    } else {
                        $('body').append(res.data.html);
                        $('.experiment_reminder_setting .datetimepicker').datetimepicker({
                            format: 'yyyy-mm-dd',
                            autoclose: true,
                            minView: 2,
                            clearBtn: true,
                        });
                        $('.experiment_reminder_setting').modal('show');

                        if ($dom.attr('data-id')) {
                            $('.experiment_reminder_setting .exp_reminder_setting_submit').attr('data-id', expId);
                        }
                    }
                }
            });
            return false;
        },

        // 实验列表 -> 加入收藏夹
        add_to_favorites: function () {
            $.ajaxFn({
                url: ELN_URL + '?r=experiment/add-to-favorites',
                type: 'POST',
                data: {
                    experiment_ids: chooseId
                },
                success: function (res) {
                    if (res.status == 1) {
                        $.showAlert(mainLang('success'));

                        for (var i = 0; i < chooseId.length; i++) {
                            var $tr = $('.exp_conetnt.active .exp_list_table tr[data-id=' + chooseId[i] + ']');
                            var $iconWrap = $tr.find('.exp_list_ico');
                            if ($iconWrap.find('.favorites_ico').length == 0) {
                                $iconWrap.append(' <i class="favorites_ico" title="' + mainLang('favorited') + '"></i>');
                            }
                        }
                    }
                }
            });
        },
        // 实验列表 -> 移出收藏夹
        remove_from_favorites: function () {
            $.ajaxFn({
                url: ELN_URL + '?r=experiment/remove-from-favorites',
                type: 'POST',
                data: {
                    experiment_ids: chooseId
                },
                success: function (res) {
                    if (res.status == 1) {
                        $.showAlert(mainLang('success'));

                        // 刷新列表
                        require('tab').reloadActiveTag();
                    }
                }
            });
        },

        // 签名记录列表
        sign_off_list: function () {
            var expId = chooseId.length == 1 ? chooseId[0] : undefined;
            $.ajaxFn({
                url: ELN_URL + '?r=experiment/sign-off-list',
                type: 'GET',
                data: {
                    experiment_id: expId
                },
                success: function (res) {
                    console.log(res);
                    if (res.status != 1) {
                        console.log(res.info);
                    } else {
                        $('body').append(res.data.html);
                        $('.sign_off_list_modal').modal('show');
                    }
                }
            });
            return false;
        },

        // 清除评论->获取评论
        get_comments: function () {
            var expId = chooseId.length == 1 ? chooseId[0] : undefined;
            $.ajaxFn({
                url: ELN_URL + '?r=experiment/get-comments',
                type: 'GET',
                data: {
                    experiment_id: expId
                },
                success: function (res) {
                    if (res.status != 1) {
                        console.log(res.info);
                    } else {
                        $('body').append(res.data.html);
                        $('.clear_comments_modal').modal('show');
                    }
                }
            });
            return false;
        },

        // 创建工单
        add_collaboration: function () {
            var data = {
                exp_code: $('.tag_bar .exp_title .tag.on .name').text().trim()
            };
            require('get_html').genAddCollaborationContentPage(data);
        },

        // 编辑链接 jiangdm
        exp_link: function () {
            $.ajaxFn({
                url: ELN_URL + '?r=experiment/set-exp-link',
                type: 'POST',
                data: {
                    experiment_id: $('.exp_conetnt.active #exp_id').val(),
                },
                success: function (res) {
                    $('body').append(res.data.html);
                    $('.exp_link_modal.add-link').modal('show');
                    loadLinkList(res.data.links);
                }
            });
        },

        // 插入仪器数据 jiangdm 2022/11/4
        insert_instrument_data: function () {
            if ($(".auto_insert_inscada_box").length > 0) {
                $.showAlert(mainLang('autoing_tip'))
                return;
            }
            window.loadInstrumentPopup();
        },

        // 更换模板
        replace_template: function ($btn) {
            const currTempId = $btn.attr('data-id');
            $.ajaxFn({
                url: ELN_URL + '?r=template/list-for-exp',
                success: function (res) {
                    if (res.status == 1) {
                        var html = `
                            <div class="replace-template-box">
                                <div>${mainLang('replace_template_tip')}</div>
                                <div class="template-list-box mt10" style="height: 30px;">
                                    <select class="template-list-selector hide">
                                        <option value="0">${mainLang('empty_temp')}</option>
                                    </select>
                                </div>
                            </div>
                        `;
                        $.popContent(html, mainLang('replace_template'), function () {
                            const $selector = $('.replace-template-box').find('.template-list-selector');
                            var newTempId = $selector.val();
                            if (newTempId == currTempId) {
                                $.closeModal();
                                return;
                            }
                            $.ajaxFn({
                                url: ELN_URL + '?r=experiment/replace-template',
                                data: {
                                    exp_id: $('.exp_conetnt.active #exp_id').val(),
                                    template_id: newTempId
                                },
                                success: function (res) {
                                    if (res.status == 1) {
                                        $.showAlert(mainLang('success'));
                                        require('tab').reloadActiveTag();
                                    }
                                }
                            });
                        }, function () {
                            const templateList = res.data;
                            const $selector = $('.replace-template-box').find('.template-list-selector');
                            for (let i = 0; i < templateList.length; i++) {
                                const template = templateList[i];
                                $selector.append(`<option value="${template.id}">${template.name}</option>`);
                            }
                            $selector.val(currTempId);
                            $selector.fSelect({
                                placeholder: mainLang('select'),
                                noResultsText: mainLang('no_search_result'),
                                searchText: mainLang('search'),
                                showSearch: true
                            });
                        });
                    }
                }
            });
        }
    };

    //字母筛选
    var expLetter = {
        page: 1,
        val: '',
        init: function () {
            var that = this;
            if ($('.exp_letter_modal').length == 0) {
                var temp = _.template(require('text!popup/exp_letter.html'))();
                $('body').append(temp);
            }
            ;
            $('.exp_letter_modal').modal('show');


            //点击字母
            $('body').off('.exp_letter').on('click.exp_letter', '.letter_btn', function () {
                that.val = $(this).text().trim();
                that.page = 1;
                that.showData('init');
                $(this).addClass('on').siblings('.letter_btn').removeClass('on');
            });

            //初始数据 A
            $('.letter_box .letter_btn:eq(0)').trigger('click');

        },

        showData: function (type, page) {
            var that = this;
            $.ajaxFn({
                url: '/?r=experiment/list-person',
                data: {
                    search_word: that.val,
                    page: page || 1
                },
                success: function (data) {
                    if (1 != data.status) {
                        return;
                    }
                    if (data.data.length == 0) {
                        $('.letter_exp_list').html('<div class="text_center">' + mainLang('no_search_date') + '</div>');
                        $('.letter_exp_page').empty();
                        return;
                    }
                    var html = '';
                    for (var i = 0, len = data.data.experimentList.list.length; i < len; i++) {
                        var exp = data.data.experimentList.list[i];
                        html += '<a class="exp_detial block" data-id="' + exp.id + '" data-type="isPerson">' + exp.title + '</a>';
                    }
                    $('.letter_exp_list').html(html);
                    var cnt = data.data.cnt;
                    $('.letter_exp_page').attr('num', data.data.cnt);

                    if ('init' == type) {
                        $('.letter_exp_page').empty();
                    }
                    if (1 == that.page && cnt > 15) {
                        that.pageFn(data.data.page);
                    }
                }
            });
        },

        pageFn: function (page) {
            var that = this;
            var pageBox = $('.letter_exp_page');
            pageBox.pagination(pageBox.attr('num'), {
                select_page: [5, 10, 15, 20, 50, 100, 200, 500],
                current_page: page - 1,
                prev_text: mainLang('prev_page'),
                next_text: mainLang('next_page'),
                num_edge_entries: 2,
                num_display_entries: 4,
                callback: function (page_index, jq) {
                    that.showData(null, (page_index + 1));
                },
                items_per_page: 15
            });
        }

    };
    //日志
    var log = {
        type: 1,
        logtype: null,
        data: {
            'read-list': 1
        },
        total: 0,
        currentPage: 0,
        init: function () {
            var that = this;
            $('body').on('click', '.click_btn_log', function () {
                var type = $(this).data('logtype');
                that.type = type;
                that.gteTabData();
            });
            $('body').on('click', '.log_type_click', function () {
                var change = true
                if (that.type === $(this).data('type')) {
                    change = false
                }

                that.type = $(this).data('type');
                that.logtype = $(this).data('logtype');
                $('.log_list .' + that.logtype).removeClass('hide');
                $('.log_list .' + that.logtype).siblings('.log_part').addClass('hide');

                if ($('.log_list .' + that.logtype + ' a.block').length == 0) {
                    log.total = $('.exp_log_modal .page_box').attr('data-num');
                    that.gteTabData(1, null, that.logtype);
                } else {
                    if (change) {
                        var tempTotal = $('.exp_log_modal .page_box').attr('data-num');
                        $('.exp_log_modal .page_box').attr('data-num', log.total);
                        log.total = tempTotal;
                        var tempPage = $('.exp_log_modal .page_box').attr('data-page');
                        $('.exp_log_modal .page_box').attr('data-page', log.currentPage);
                        log.currentPage = tempPage;
                    }

                    that.pageFn();
                }
            });
        },

        gteTabData: function (page, nopage, logtype) {
            var that = this;
            var data = {
                type: that.type,
                id: $('.exp_conetnt.active #exp_id').val()
            };
            data.page = page || 1;
            that.sendAjaxFn(data, function (data) {
                if ($('.exp_log_modal').length == 0) {
                    $('body').append(data.data);
                }
                var html = $(data.data);
                if (logtype) {
                    var html = '';
                    var data_ = data.data.data;
                    var title;
                    if (that.type == 1) {
                        title = mainLang('show');
                    } else if (that.type == 2) {
                        title = mainLang('print');
                    } else {
                        title = mainLang('download');
                    }
                    if (data_.length == 0) {
                        html = mainLang('no') + ' ' + title + ' ' + mainLang('exp');
                    } else {
                        var userList = data.data.userList;
                        for (var i = 0; i < data_.length; i++) {
                            var userInfo = userList[data_[i].user_id];
                            // var who = userInfo.real_name + '(' + userInfo.name + ')';
                            var who = userInfo.real_name;

                            var fileName = data_[i].file_name;
                            var type = data_[i].type;
                            if (type == 4) {
                                html += '<a class="block" log-id="' + data_[i].id + '" href="javascript:void(0);">' + data_[i].create_time + ' ' + who + ' ' + mainLang('download_file') + ': ' + fileName + '</a>';
                            } else {

                                html += '<a class="block" log-id="' + data_[i].id + '" href="javascript:void(0);">' + data_[i].create_time + ' ' + who + ' ' + title + mainLang('le_exp') + '</a>';
                            }
                        }
                    }
                    $('.exp_log_modal .' + logtype).html(html);
                    $('.exp_log_modal .page_box').attr('data-num', data.data.total)
                    $('.exp_log_modal .page_box').attr('data-page', data.data.page);
                } else {
                    $('.exp_log_modal .log_list').html(html.find('.log_list').html());
                    log.type = 1;
                }
                $('.exp_log_modal').modal('show');
                if (!nopage) {
                    that.pageFn();
                }
            });
        },

        pageFn: function () {
            var that = this;
            var pageBox = $('.exp_log_modal .page_box');
            pageBox.pagination(pageBox.attr('data-num'), {
                select_page: [5, 10, 15, 20, 50, 100, 200, 500],
                prev_text: mainLang('prev_page'),
                next_text: mainLang('next_page'),
                num_edge_entries: 2,
                num_display_entries: 4,
                callback: function (page_index, jq) {
                    that.gteTabData((page_index + 1), 'nopage', $('.exp_log_modal .log_part:visible').attr('data-logtype'));
                },
                items_per_page: 15,
                current_page: pageBox.attr('data-page'),
            });
        },

        sendAjaxFn: function (data, callback) {
            var that = this;
            $.ajaxFn({
                url: ELN_URL + '?r=log/read-list',
                data: data,
                success: function (data) {
                    callback(data);
                }
            })
        }
    }
    log.init();

    var history = {
        init: function () {
            var that = this;
            //
            $('body').on('click', '.click_btn_his', function () {
                that.getData();
            });

            //对比
            $('body').on('click', '.contrast-btn', function () {
                var type = $(this).attr('dtype');
                that[type] ? that[type]($(this)) : '';
            });

            // 打开总痕迹详情列表  add by hkk 2019/12/3
            $('body').on('click', '.trace-detail-btn', function () {
                that.trace_exp_id = $(this).attr('data-id');
                that.compare_start_time = ""; //清空比较保存痕迹时的起止时间
                that.compare_end_time = ""; //清空比较保存痕迹时的起止时间
                that.detail(1, "yes");
            });

            // 搜索痕迹详情列表 add by hkk 2019/12/3
            $('body').on('click', '.search-trace-detail', function () {
                that.pageTrace = 1;
                that.detail(1, "no");
            });

            // 打开单个痕迹详情列表 介于两条保存之间的痕迹  add by hkk 2019/12/3
            $('body').on('click', '.single_trace_detail', function () {
                //获取之前痕迹的保存时间和当前保存时间
                var nextDom = $(this).parents('.single_history').next('.single_history');
                if (nextDom) {
                    that.compare_start_time = nextDom.attr('data-savetime');
                }
                const singleDetailStartTime = nextDom?.attr?.('data-savetime');

                that.compare_end_time = $(this).parents('.single_history').attr('data-savetime');
                const singleDetailEndTime = $(this).parents('.single_history').attr('data-savetime');
                that.single_trace_detail_compare_end_time = singleDetailEndTime;

                that.trace_exp_id = $(this).attr('data-expid');
                that.getSingleDetail({
                    traceExpId: that.trace_exp_id,
                    singleDetailStartTime,
                    singleDetailEndTime,
                });
                // that.detail(1, "yes");
            });

            // 根据指定的痕迹恢复实验内容
            $('body').on('click', '.revert_exp', function () {
                var expId = $(this).attr('data-exp-id');
                var hisId = $(this).attr('data-his-id');
                var html = `
                    <div class="replace-template-box">
                        <div>${mainLang('replace_template_tip')}</div>
                        <div class="template-list-box mt10" style="height: 30px;">
                            <select class="template-list-selector hide">
                                <option value="0">${mainLang('empty_temp')}</option>
                            </select>
                        </div>
                    </div>
                `;
                $.popContent(mainLang('recover_exp_tip'), mainLang('recover_exp'), function () {
                    $.ajaxFn({
                        type: 'POST',
                        url: '?r=experiment/recover-from-history',
                        data: {
                            experiment_id: expId,
                            history_id: hisId
                        },
                        success: function (res) {
                            if (res.status == 1) {
                                $.showAlert(mainLang('success'));
                                require('tab').reloadActiveTag();
                            }
                        }
                    })
                });
            });
        },

        //开始比对
        userContrast: function () {
            $('.user-contrast').show();
        },

        //对比查看
        view: function () {
            var path = this.getContrastPath();

            if (path) {
                var url = ELN_URL + '?r=compare/review-compare-v2' + path;
                window.open(url); //, "hispop"
                //location.href = '?r=file/review-compare&his_ids=' +  ids
            }
            /*if(ids){
                $.ajaxFn({
                    type: 'GET',
                    url: '?r=file/review-compare',
                    data: {
                        his_ids: ids
                    }
                })
            }*/
        },

        // add by hkk 2019/12/3 痕迹详情总览列表
        detail: function (page, needUpdateAllPage) {

            var that = this;

            var data = {
                exp_id: that.trace_exp_id,
                needUpdateAllPage: needUpdateAllPage,
                page: page || 1,
                limit: $('.pager-select:visible').val() || that.default_page_size || undefined,
                start_time: $('.trace_detail_page #start_time').val(),
                end_time: $('.trace_detail_page #end_time').val(),
                trace_detail: $.trim($('.trace_detail_page .trace_detail').val()),
                compare_start_time: that.compare_start_time,
                compare_end_time: that.compare_end_time,
            };

            $.ajaxFn({
                url: ELN_URL + '?r=history/get-trace-detail-page',
                data: data,
                success: function (data) {
                    if (data.status == 1) {

                        if (needUpdateAllPage === "yes") {
                            $(".trace_detail_page").remove();
                            $("body").append(data.data.file);
                            $(".trace_detail_page").modal('show');

                            // 调用日历插件
                            var dateOpts = {
                                format: 'yyyy-mm-dd hh:ii',
                                autoclose: true,
                                minView: 0, // 0精确到分钟 2精确到小时
                                clearBtn: true,
                            };
                            if ($.fn.datetimepicker) {
                                $('.trace_detail_page .datetimepicker').datetimepicker(dateOpts).on('click', function () {

                                    if ($('.trace_detail_page [name="end_time"]').val() != '') {
                                        var startTimer = $('.instrument_book_pop [name="end_time"]').val();
                                        $('.trace_detail_page [name="start_time"]').datetimepicker('setEndDate', startTimer);
                                    }

                                    if ($('.trace_detail_page [name="start_time"]').val() != '') {
                                        var endTimer = $('.instrument_book_pop [name="start_time"]').val();
                                        $('.trace_detail_page [name="end_time"]').datetimepicker('setStartDate', endTimer);
                                    }
                                })
                            }

                        } else {
                            $('.trace_detail_table').html(data.data.file);

                        }

                        // 调用分页插件
                        that.traceDetailPageFn();

                    }
                }
            });

        },

        /**
         * 获取单个保存实验记录的痕迹
         */
        getSingleDetail: function ({traceExpId, singleDetailStartTime, singleDetailEndTime}) {
            const that = this;

            const data = {
                exp_id: traceExpId,
                needUpdateAllPage: 'yes', // 整体更新整个弹框页面
                page: 1,
                limit: $('.pager-select:visible').val() || that.default_page_size || undefined,
                single_detail_start_time: singleDetailStartTime,
                single_detail_end_time: singleDetailEndTime,
            };
            $.ajaxFn({
                type: 'POST',
                url: ELN_URL + '?r=history/get-trace-detail-page',
                data: data,
            }).then(resp => {
                if (resp.status != 1) {
                    console.error('请求失败：%o', resp.message);
                    return;
                }
                const traceDetailPage = resp.data.file;
                // 更新痕迹页面
                $(".trace_detail_page").remove();
                $("body").append(traceDetailPage);
                $(".trace_detail_page").modal('show');

                // 调用日历插件
                const dateOpts = {
                    format: 'yyyy-mm-dd hh:ii',
                    autoclose: true,
                    minView: 0, // 0精确到分钟 2精确到小时
                    clearBtn: true,
                };
                $('.trace_detail_page .datetimepicker')
                    .datetimepicker?.(dateOpts)
                    .on('click', function () {
                        if ($('.trace_detail_page [name="end_time"]').val() != '') {
                            const startTimer = $('.instrument_book_pop [name="end_time"]').val();
                            $('.trace_detail_page [name="start_time"]').datetimepicker('setEndDate', startTimer);
                        }

                        if ($('.trace_detail_page [name="start_time"]').val() != '') {
                            const endTimer = $('.instrument_book_pop [name="start_time"]').val();
                            $('.trace_detail_page [name="end_time"]').datetimepicker('setStartDate', endTimer);
                        }
                    })

                // 调用分页插件
                that.traceDetailPageFn();
            })
        },

        getContrastPath: function () {
            var nowlen = 0;
            var nowDom = $(".history-checkbox-now");
            if (nowDom.prop('checked')) {
                exp_id = nowDom.val();
                nowlen = 1;
            }

            var ids = [];
            var checked = $('.history-checkbox:checked');

            if (checked.length + nowlen != 2) {
                $.showAlert(mainLang('two_compare'));
                return false;
            }
            $('.history-checkbox:checked').each(function () {
                //                if($(this).next('.history-checkbox:checked').length > 0){
                //                    ids += $(this).val();
                //                }else{
                //                    ids += $(this).val()+ ',';
                //                }
                ids.push($(this).val());
            });

            var path = '&his_ids=' + ids.toString();
            if (nowlen) {
                path = path + '&exp_id=' + exp_id;
            }
            return path;
        },

        traceDetailPageFn: function () {
            var that = this;
            var pageBox = $('.trace_detail_page .page_box');
            var page = that.pageTrace || 1;
            pageBox.pagination(pageBox.attr('data-num'), {
                select_page: [5, 10, 15, 20, 50, 100, 200, 500],
                prev_text: mainLang('prev_page'),
                next_text: mainLang('next_page'),
                num_edge_entries: 2,
                num_display_entries: 4,
                current_page: page - 1,
                callback: function (page_index, jq) {

                    that.pageTrace = page_index + 1;
                    that.detail((page_index + 1), "no");

                },
                items_per_page: pageBox.attr('data-limit'),
                default_page_size: pageBox.attr('data-limit')
            });
        },


    };
    history.init();

    //选择复核
    $('body').on('click', '.set_sign_person', function () {
        $('.sign_modal_body .sign_change').toggleClass('hide');
        $(this).toggleClass('review_');
    });
    var sign = {
        init: function () {
            var that = this;

            //打开弹窗
            $('body').on('click', '.click_btn[data-type="lock"]', function () {
                //获取当前激活的页面是否为实验
                var expIds = expTool.getData();
                if (expIds.length == 0) {
                    $.showAlert(mainLang('select_exp'));
                    return;
                }
                $.ajaxFn({
                    url: ELN_URL + '?r=experiment/sync-inventory-data',
                    data: {
                        source: 'experiment',
                        exp_id: expIds
                    },
                    type: 'post',
                    success: function (res) {
                        // 根据后台返回的配置，决定是否直接进入复核关闭流程 jiangdm 2022/3/25
                        if (res.status == 0) {
                            that.submitClose();
                        } else {
                            $(".sync-inventory-modal").remove();
                            $("body").append(res.data.file);
                            $(".sync-inventory-modal").modal('show');
                        }
                    }
                });
            });

            // 复核关闭生成的同步入库界面关闭后，显示复核关闭页面 jiangdm 2022/3/25
            $('body').on('hide.bs.modal', '.sync-inventory-modal.need-confirm', function () {
                that.submitClose();
            })

            //提交sign
            $('body').on('click', '#sign_submit', function () {
                that.submitBtn($(this));
            });

            //生成数字证书
            $('body').on('click', '.generate_certificate', function () {
                $('.pfx_box .input_part').removeClass('hidden');
            });


            $('body').on('click', '.sign_change input.sr', function () {
                $('.sign_modal_body [name="sign_object"]:eq(0)').prop('checked', false);
                $('.sign_modal_body [name="sign_object"]:eq(1)').prop('checked', true);
            })
            $('body').on('change', '.sign_change select.sr', function () {
                $(this).siblings('label').trigger('click');
            });
        },

        // 重新封装的复核关闭流程，里面的内容没动 jiangdm 2022/3/25
        submitClose: function () {
            var tagId = $('.exp_title .tag.on').attr('data-id');
            var tagFunc = $('.exp_title .tag.on').attr('data-func');
            //如果当前页面为实验验证实验是否已经保存
            if (tagFunc === 'getExpContent') {
                //如果实验已保存 则继续执行
                //如果实验未保存 先保存实验再继续

                require(['save_exp'], function (save_exp) {
                    var isSameMol = true; //是相同的结构式 没有修改
                    var activeIndraw = $('.exp_conetnt.active .chendraw iframe')[0];
                    if (activeIndraw && activeIndraw.contentWindow && activeIndraw.contentWindow.indraw) {
                        isSameMol = $('.chendraw iframe')[0].contentWindow.indraw.isSameMol;
                    }

                    var saveBtn = $('.exp_conetnt.active .tool_btn[data-type="save"]');
                    if (saveBtn.length > 0 && (save_exp.isModified() || !isSameMol)) {
                        save_exp.save({
                            success: function () {

                                if (expTool.getData().length == 0) {
                                    $.showAlert(mainLang('select_exp'));
                                    return;
                                }
                                var canContinue = true;
                                $('.exp_conetnt.active .exp_list_table .checkbox:checked').each(function () {
                                    var tr = $(this).parents('tr');
                                    if (tr.find('.signed').length > 0 || tr.find('.countersigned').length > 0 || tr.find('.waiting').length > 0) {
                                        $.showAlert(mainLang('select_sign_exp_re_select'));
                                        canContinue = false;
                                    }
                                });
                                if (!canContinue) {
                                    return;
                                }
                                var ids = expTool.getData(),
                                    urlIds = '';
                                for (var i = 0, len = ids.length; i < len; i++) {
                                    urlIds += '&checkedArr[]=' + ids[i];
                                }
                                var url = $('.a_lock_click').attr('href') + urlIds;
                                //add by wy 2023/3/30 将本地存储的审批节点数据发送给后端
                                var witness_sign_approval_nodes;
                                if (localStorage.getItem('witness_sign_approval_nodes')) {
                                    witness_sign_approval_nodes = localStorage.getItem('witness_sign_approval_nodes');
                                }
                                url += '&witness_sign_approval_nodes=' + witness_sign_approval_nodes;

                                //! 试验复核为一级复核 #1/3 //bug#33906
                                const isSingleWitness = $('.single-signer .visible-user-selector-box .visible-user-input').length > 0;
                                const single_witness_approvers = localStorage.getItem(SINGLE_WIT_APPROVERS);
                                if (single_witness_approvers) {
                                    url += `&${SINGLE_WIT_APPROVERS}=${single_witness_approvers}`;
                                }
                                //! 批量复核试验
                                $.ajaxFn({
                                    type: 'GET',
                                    url: url,
                                    success: function (data) {
                                        if (data.status != 1) {
                                            $.showContent('warning', '', data.info);
                                            return;
                                        }
                                        ;
                                        $('body').append(data.data);
                                        $('.sign_modal').on('show.bs.modal', function () {
                                            $('.sign_modal').find('.signer_select').fSelect({
                                                placeholder: mainLang('select'),
                                                noResultsText: mainLang('no_search_result'),
                                                searchText: mainLang('search'),
                                                showSearch: true
                                            });
                                        })
                                        $('.sign_modal').modal('show');
                                    }
                                });
                            },
                            dom: saveBtn,
                            silent: true,
                            keepModal: true,
                            autoSave: true,
                        });
                    } else {
                        if (expTool.getData().length == 0) {
                            $.showAlert(mainLang('select_exp'));
                            return;
                        }
                        var canContinue = true;
                        $('.exp_conetnt.active .exp_list_table .checkbox:checked').each(function () {
                            var tr = $(this).parents('tr');
                            if (tr.find('.signed').length > 0 || tr.find('.countersigned').length > 0 || tr.find('.waiting').length > 0) {
                                $.showAlert(mainLang('select_sign_exp_re_select'));
                                canContinue = false;
                            }
                        });
                        if (!canContinue) {
                            return;
                        }
                        var ids = expTool.getData(),
                            urlIds = '';
                        for (var i = 0, len = ids.length; i < len; i++) {
                            urlIds += '&checkedArr[]=' + ids[i];
                        }
                        var url = $('.a_lock_click').attr('href') + urlIds;
                        //add by wy 2023/3/30 将本地存储的审批节点数据发送给后端
                        var witness_sign_approval_nodes;
                        if (localStorage.getItem('witness_sign_approval_nodes')) {
                            witness_sign_approval_nodes = localStorage.getItem('witness_sign_approval_nodes');
                        }
                        url += '&witness_sign_approval_nodes=' + witness_sign_approval_nodes;

                        //! 试验复核为一级复核 #2/3 //bug#33906
                        const isSingleWitness = $('.single-signer .visible-user-selector-box .visible-user-input').length > 0;
                        const single_witness_approvers = localStorage.getItem(SINGLE_WIT_APPROVERS);
                        if (single_witness_approvers) {
                            url += `&${SINGLE_WIT_APPROVERS}=${single_witness_approvers}`;
                        }
                        //! 实验页面单个复核
                        $.ajaxFn({
                            type: 'GET',
                            url: url,
                            success: function (data) {
                                if (data.status != 1) {
                                    $.showContent('warning', '', data.info);
                                    return;
                                }
                                ;
                                $('body').append(data.data);
                                $('.sign_modal').on('show.bs.modal', function () {
                                    $('.sign_modal').find('.signer_select').fSelect({
                                        placeholder: mainLang('select'),
                                        noResultsText: mainLang('no_search_result'),
                                        searchText: mainLang('search'),
                                        showSearch: true
                                    });
                                })
                                $('.sign_modal').modal('show');
                            }
                        });
                    }
                });
            } else {
                //如果不是实验 走原来的逻辑

                if (expTool.getData().length == 0) {
                    $.showAlert(mainLang('select_exp'));
                    return;
                }
                var canContinue = true;
                $('.exp_conetnt.active .exp_list_table .checkbox:checked').each(function () {
                    var tr = $(this).parents('tr');
                    if (tr.find('.signed').length > 0 || tr.find('.countersigned').length > 0 || tr.find('.waiting').length > 0) {
                        $.showAlert(mainLang('select_sign_exp_re_select'));
                        canContinue = false;
                    }
                });
                if (!canContinue) {
                    return;
                }
                var ids = expTool.getData(),
                    urlIds = '';
                for (var i = 0, len = ids.length; i < len; i++) {
                    urlIds += '&checkedArr[]=' + ids[i];
                }
                var url = $('.a_lock_click').attr('href') + urlIds;
                //add by wy 2023/3/30 将本地存储的审批节点数据发送给后端
                var witness_sign_approval_nodes;
                if (localStorage.getItem('witness_sign_approval_nodes')) {
                    witness_sign_approval_nodes = localStorage.getItem('witness_sign_approval_nodes');
                }
                url += '&witness_sign_approval_nodes=' + witness_sign_approval_nodes;

                //! 试验复核为一级复核 #3/3 //bug#33906一级复核也需要保存上次所选的复核人 mod dx
                const isSingleWitness = $('.single-signer .visible-user-selector-box .visible-user-input').length > 0;
                const single_witness_approvers = localStorage.getItem(SINGLE_WIT_APPROVERS);
                if (single_witness_approvers) {
                    url += `&${SINGLE_WIT_APPROVERS}=${single_witness_approvers}`;
                }
                $.ajaxFn({
                    type: 'GET',
                    url: url,
                    success: function (data) {
                        if (data.status != 1) {
                            $.showContent('warning', mainLang('tip_'), data.info);
                            return;
                        }
                        $('body').append(data.data);
                        $('.sign_modal').on('show.bs.modal', function () {
                            $('.sign_modal').find('.signer_select').fSelect({
                                placeholder: mainLang('select'),
                                noResultsText: mainLang('no_search_result'),
                                searchText: mainLang('search'),
                                showSearch: true
                            });
                        })
                        $('.sign_modal').modal('show');
                    }
                });
            }
        },

        submitSign: function (data) {
            var $submitBtn = $('#sign_submit');
            if ($submitBtn.hasClass('disabled')) {
                return;
            }
            $submitBtn.addClass('disabled');

            var that = this;
            that.sendAjax(ELN_URL + '?r=sign/sign-experiment', data, function (res) {
                $submitBtn.removeClass('disabled');
                $.closeModal();
                $.showAlert(mainLang('success'));

                if ($('.exp_list_table:visible').length > 0) { // 实验列表页进行批量提交复核->刷新列表状态
                    // 点击当前分页刷新页面即可记录分页以及滚动条
                    $('.exp_conetnt.active .current.page-btn').trigger('click')
                } else { // 实验详情页进行单个提交复核->重新打开实验
                    require('tab').reloadActiveTag();
                }
            }, function (res) {
                $submitBtn.removeClass('disabled');
                //输错不隐藏
                // $.closeModal();
                if (res.data && res.data.tipType && res.data.tipType == 'popContent') {
                    $.popContent(data.info, mainLang('tip_'), undefined, undefined, false);
                } else {
                    $.showAlert(res.info.infoStr || res.info);
                }
            });
        },

        submitPfx: function (data) {
            var that = this;
            that.sendAjax(ELN_URL + '?r=sign/create-pfx', {
                group_ids: data.checkedArr,
                password: data.password
            }, function (data) {
                if (data.status == 1) {
                    $('.sign_box').removeClass('hidden');
                    $('.pfx_box').addClass('hidden');
                    $('#sign_submit').attr('datatype', 'sign');
                }
            });
        },

        submitBtn: function (obj) {
            var that = this;
            var password;
            if (obj.attr('datatype') == 'pfx') {
                password = $('.pfx_box .password_input').val();
            } else {
                password = $('.sign_box .password_input').val();
            }

            if (!$.checkVal(password, 'noempty')) {
                $.showAlert(mainLang('pass_must'));
                return;
            }

            password = $.passwordEncipher(password);

            // 生成数字证书
            if (obj.attr('datatype') == 'pfx') {
                that.submitPfx({
                    password: password
                });
                return;
            }

            // 获取要签名的实验ID列表（批量或者单个）
            var expIds = expTool.getData();
            if (expIds.length == 0) {
                $.showAlert(mainLang('select_exp'));
                return;
            }

            // add by hkk 2019/3/1  增加实验结果和实验评星传给数据库存储
            var exp_result = $("input[name='labResult']:checked").val();
            var exp_star = $("#score-result").val();

            var $reasonBox = $('.sign_modal .reason-select-input-box');
            var $reasonDom = $('.sign_modal .reason-textarea');
            if (!$reasonDom.is(':visible')) {
                $reasonDom = $('.sign_modal [name=submit_reason]');
            }
            var reason = $reasonDom.val().trim();
            if (reason === '' && $reasonBox.hasClass('required')) {
                $.showAlert(mainLang('pls_input_reason'));
                return;
            }

            // 关闭实验请求的数据
            var signData = {
                password: password,
                exp_ids: expIds,
                exp_result: exp_result ? exp_result : 0, // add by hkk 2019/3/1
                exp_star: exp_star ? exp_star : 0, // add by hkk 2019/3/1
                reason: reason
            };
            // 是否需要审核
            signData.witness = $('.input_part.sign_change:visible').length > 0 ? 1 : 0;

            // 审核节点
            signData.approval_nodes = [];

            /** 一级复核时选择的复核人 */
            let singleWitness = [];

            // 一级复核
            if ($('.single-signer .signer_select').length > 0) {
                signData.approval_nodes.push({
                    approval_user_ids: [$('.signer_select').val()]
                });
            }

            if ($('.single-signer .visible-user-selector-box .visible-user-input').length > 0) {
                var userIds = $('.single-signer .visible-user-selector-box .visible-user-input').attr('idbox');
                if (userIds !== '') {
                    userIds = userIds.split(',');
                    signData.approval_nodes.push({
                        approval_user_ids: userIds
                    });
                }
                singleWitness = userIds;
            }

            // 多级级复核
            var errMsg = '';
            var $nodes = $('.multiple-signers .approval-node-box');
            $nodes.each(function (index) {
                var node = {};
                var $approvalSelect = $(this).find('.approval_select');
                node.approval_user_ids = $approvalSelect.val();
                //如果群内权限-审批设置里没有打开复合签名必须选择复核人，那么就不需要在复核关闭里选择复核人 by zwm 2022/12/09
                if (signData.witness && !node.approval_user_ids) {
                    errMsg = mainLang('pls_select_node_approval', [index + 1]);
                    return false;
                }
                var $coapprovalSelect = $(this).find('.coapproval_select');
                if ($coapprovalSelect.length === 1) {
                    node.coapproval_user_ids = $coapprovalSelect.val();
                    if (!node.coapproval_user_ids) {
                        errMsg = mainLang('pls_select_node_coapproval', [index + 1]);
                        return false;
                    }
                }
                //如果复核关闭里没有打开选择复核人员就不传选择的复核人 by zwm 2022/12/09
                if (signData.witness) {
                    signData.approval_nodes.push(node);
                }
            });
            if (errMsg) {
                $.showAlert(errMsg);
                return;
            }

            // 如果实验有变动先保存
            require(['save_exp'], function (save_exp) {
                var isSameMol = true; //是相同的结构式 没有修改
                var activeIndraw = $('.exp_conetnt.active .chendraw iframe')[0];
                if (activeIndraw && activeIndraw.contentWindow && activeIndraw.contentWindow.indraw) {
                    isSameMol = $('.chendraw iframe')[0].contentWindow.indraw.isSameMol;
                }

                var saveBtn = $('.exp_conetnt.active .tool_btn[data-type="save"]');
                if (saveBtn.length > 0 && (save_exp.isModified() || !isSameMol)) {
                    save_exp.save({
                        success: function () {
                            that.submitSign(signData);
                        },
                        dom: saveBtn,
                        silent: true,
                        keepModal: true,
                        autoSave: true,
                    });
                } else {
                    that.submitSign(signData);
                }
                //add by wy 2023/3/30 保存复核关闭节点数据到本地存储
                var witness_sign_approval_nodes = JSON.stringify(signData.approval_nodes);
                localStorage.setItem('witness_sign_approval_nodes', witness_sign_approval_nodes);
                //! 如果仅一级复核,保存选择的审批人设置 bug#33906
                if (singleWitness.length > 0) {
                    localStorage.setItem(SINGLE_WIT_APPROVERS, JSON.stringify(singleWitness));
                }
            });
        },

        sendAjax: function (url, data, callback, errorCallback) {
            $.ajaxFn({
                url: url,
                data: data,
                success: function (data) {
                    if (data.status == 1) {
                        callback(data);
                    } else {
                        errorCallback(data);
                    }
                }
            })
        }
    };
    sign.init();

    // begin 复核栏实验结果点击事件 add by hkk 2019/2/28

    $("body").on("click", ".lab-score li", function () {

        //清空所有
        $(".lab-score li").css("background-image", "url(../../image/star-hollow.png)");

        //填充星星
        for (var j = 1; j <= $(this).attr("number"); j++) {
            var id_selector = "#score-result" + j;
            $(id_selector).css("background-image", "url(../../image/star-yellow.png)");
        }

        //把结果存在右边隐藏的input框里
        $("#score-result").val($(this).attr("number"));
    });

    //end

    // 预审
    var pretrial = {
        init: function () {
            $('body').on('click', '#pretrial_submit', function () {
                var approvalNodes = [];
                if ($('.pretrial_modal .approval-node-box').length != 0) {
                    approvalNodes = require('approval').getNodes();
                    if (approvalNodes.length == 0) {
                        $.showAlert(mainLang('pls_set_approval_node'));
                        return false;
                    }
                }

                var $reasonBox = $('.pretrial_modal .reason-select-input-box');
                var reason = $('.pretrial_modal .reason-textarea').val().trim();
                if (reason === '' && $reasonBox.hasClass('required')) {
                    $.showAlert(mainLang('pls_input_reason'));
                    return false;
                }

                $.ajaxFn({
                    type: 'POST',
                    url: '?r=experiment/submit-pretrial',
                    data: {
                        exp_id: expId,
                        node_arr: approvalNodes,
                        reason: reason
                    },
                    success: function (res) {
                        if (res.status == 1) {
                            require('tab').reloadActiveTag();
                        }
                    }
                });
            });
        },
        pretrial: function () {
            $.ajaxFn({
                type: 'GET',
                url: '?r=experiment/pretrial-view',
                data: {
                    exp_id: expId
                },
                success: function (res) {
                    if (res.status == 1) {
                        $('body').append(res.data);
                        $('.pretrial_modal').modal('show');
                    }
                }
            });
        },
        cancel_pretrial: function (dom) {
            $.popContent(mainLang('confirm_cancel_pretrial'), mainLang('cancel_pretrial'), function () {
                $.ajaxFn({
                    url: ELN_URL + '?r=experiment/cancel-pretrial',
                    data: {
                        exp_id: $('.exp_conetnt.active #exp_id').val()
                    },
                    success: function (res) {
                        if (res.status == 1) {
                            $.showAlert(mainLang('success'));
                            require('tab').reloadActiveTag();
                        }
                    }
                });
            });
        }
    };
    pretrial.init();

    // 签字
    var signing = {
        // add by szq 2020/5/25 签字模块添加全选反选
        select: function () {
            $('body').on('click', '.select_all_sign', function () {
                if ($(this).prop('checked')) {
                    $('#exp-module-list span input').prop('checked', true);
                } else {
                    $('#exp-module-list span input').prop('checked', false);
                }
            })
            $('body').on('click', '#exp-module-list span input', function () {
                if ($('#exp-module-list span input').length === $('#exp-module-list span input:checked').length) {
                    $('.select_all_sign').prop('checked', true)
                } else {
                    $('.select_all_sign').prop('checked', false)
                }
            })
        },
        init: function () {
            $('body').on('click', '#signing_submit', function () {
                var moduleArr = [];
                $('#exp-module-list :checkbox:checked').each(function () {
                    moduleArr.push($(this).attr('data-id'))
                });
                if (moduleArr.length == 0) {
                    $.showAlert(mainLang('select_signing_modules'));
                    return false;
                }

                var $reasonBox = $('.signing_modal .reason-select-input-box');
                var reason = $('.signing_modal .reason-textarea').val().trim();
                if (reason === '' && $reasonBox.hasClass('required')) {
                    $.showAlert(mainLang('pls_input_reason'));
                    return false;
                }

                // 判断各级审批节点是否填写
                var approvalNodes = [];
                var errMsg = '';
                var $nodes = $('.signing_modal .scoped-approver .approval-node-box');
                $nodes.each(function (index) {
                    var node = {};
                    var $approvalSelect = $(this).find('.approval_select');
                    node.approval_user_ids = $approvalSelect.val();
                    if (!node.approval_user_ids) {
                        errMsg = mainLang('pls_select_node_approval', [index + 1]);
                        return false;
                    }
                    var $coapprovalSelect = $(this).find('.coapproval_select');
                    if ($coapprovalSelect.length === 1) {
                        node.coapproval_user_ids = $coapprovalSelect.val();
                        if (!node.coapproval_user_ids) {
                            errMsg = mainLang('pls_select_node_coapproval', [index + 1]);
                            return false;
                        }
                    }
                    approvalNodes.push(node);
                });
                if (errMsg) {
                    $.showAlert(errMsg);
                    return;
                }

                // 完全自主选择审批人的情况
                if ($('.signing_modal .non-scoped-approver .approval-node-box').length != 0) {
                    approvalNodes = require('approval').getNodes();
                }

                if (approvalNodes.length == 0) {
                    $.showAlert(mainLang('select_signing_users'));
                    return false;
                }

                $.ajaxFn({
                    type: 'POST',
                    url: '?r=experiment/submit-signing',
                    data: {
                        exp_id: expId,
                        module_arr: moduleArr,
                        node_arr: approvalNodes,
                        reason: reason
                    },
                    success: function (res) {
                        if (res.status == 1) {
                            $.showAlert(mainLang('signing_submitted'));
                            $('.signing_modal').modal('hide');
                            // 重新加载实验以激活冻结编辑模块功能的弹框
                            require('tab').reloadActiveTag();
                        }
                    }
                });
            });
        },
        signing: function () {
            $.ajaxFn({
                type: 'GET',
                url: '?r=experiment/signing-view',
                data: {
                    exp_id: expId
                },
                success: function (res) {
                    if (res.status == 1) {
                        $('body').append(res.data);
                        $('.signing_modal').on('show.bs.modal', function () {
                            $('.signing_modal').find('.signer_select').fSelect({
                                placeholder: mainLang('select'),
                                noResultsText: mainLang('no_search_result'),
                                searchText: mainLang('search'),
                                showSearch: true
                            });
                        });
                        $('.signing_modal').modal('show');
                    }
                }
            });
        }
    };
    signing.select();
    signing.init();

    // 重开
    var reopen = {
        init: function () {
            $('body').on('click', '.tool_nav a.edit', function () {
                $.ajaxFn({
                    type: 'GET',
                    url: '?r=experiment/reopen-view',
                    data: {
                        exp_id: expId
                    },
                    success: function (res) {
                        if (res.status == 1) {
                            var html = res.data.html;
                            $.popContent(html, mainLang('update_tips'), function () {
                                var password = $('.password_input[name="reopen_password"]').val();
                                if (!$.checkVal(password, 'noempty')) {
                                    $.showAlert(mainLang('pass_must'));
                                    return false;
                                }

                                password = $.passwordEncipher(password);

                                var update_comment = $('[name="update_comment"]').val().trim();
                                if (update_comment == '') {
                                    $.showAlert(mainLang('input_reason'));
                                    return;
                                }
                                window.update_comment = update_comment;

                                $.ajaxFn({
                                    noload: true,
                                    noTipError: true,
                                    url: '/?r=experiment/reopen',
                                    data: {
                                        exp_id: $('.exp_conetnt.active #exp_id').val(),
                                        password: password,
                                        comment: update_comment
                                    },
                                    success: function (res) {
                                        if (res.status == 1) {
                                            require('tab').reloadActiveTag(); // 重新加载实验
                                        } else {
                                            $.showAlert(res.info);
                                        }
                                    }
                                });
                            });
                        }
                    },
                    noLoad: true
                });
            });
        },
        cancel_reopen: function (dom) {
            $.popContent(mainLang('confirm_cancel_reopen'), mainLang('cancel_reopen'), function () {
                $.ajaxFn({
                    url: ELN_URL + '?r=experiment/cancel-reopen',
                    data: {
                        exp_id: $('.exp_conetnt.active #exp_id').val()
                    },
                    success: function (res) {
                        if (res.status == 1) {
                            require('tab').reloadActiveTag(); // 重新加载实验
                        } else {
                            $.showAlert(res.info);
                        }
                    }
                });
            });
        }
    }
    reopen.init();

    // 模块重新编辑
    var moduleReedit = {
        init: function () {
            $('body').on('click', '.req_for_edit', function (event) {
                event.preventDefault();
                var $modulPart = $(this).parents('.modul_part');
                var moduleId = $modulPart.find('.modul_part_id').attr('data-id') ? $modulPart.find('.modul_part_id').attr('data-id') : 0;

                $.ajaxFn({
                    url: ELN_URL + '?r=experiment/req-reedit-view',
                    data: {
                        exp_id: $('.exp_conetnt.active #exp_id').val(),
                        module_id: moduleId
                    },
                    success: function (res) {
                        if (res.status == 1) {
                            var data = res.data;
                            $('body').append(data.html);
                            $('.reedit_modal').modal('show');
                        } else {
                            $.showAlert(res.info);
                        }
                    }
                });
            });

            $('body').on('click', '#reedit_submit', function () {
                var password = $('.reedit_modal .password_input').val();
                if (!$.checkVal(password, 'noempty')) {
                    $.showAlert(mainLang('pass_must'));
                    return false;
                }

                password = $.passwordEncipher(password);

                var moduleArr = [];
                if ($(this).attr('module_id')) {
                    moduleArr.push($(this).attr('module_id'));
                } else {
                    $('.reedit_modal .module-list-box :checkbox:checked').each(function () {
                        moduleArr.push($(this).attr('data-id'))
                    });
                }

                if (moduleArr.length == 0) {
                    $.showAlert(mainLang('select_edit_modules'));
                    return false;
                }

                var $reasonDom = $('.reedit_modal .reason-textarea');
                if (!$reasonDom.is(':visible')) {
                    $reasonDom = $('.reedit_modal [name=submit_reason]');
                }
                var reason = $reasonDom.val().trim();
                if (reason === '') {
                    $.showAlert(mainLang('pls_input_reason'));
                    return false;
                }

                $.ajaxFn({
                    type: 'POST',
                    url: '?r=experiment/req-reedit',
                    data: {
                        password: password,
                        exp_id: $('.exp_conetnt.active #exp_id').val(),
                        module_arr: moduleArr,
                        reason: reason
                    },
                    success: function (res) {
                        if (res.status == 1) {
                            for (var i = 0; i < moduleArr.length; i++) {
                                var moduleId = moduleArr[i];
                                var $btn = $('#modul_line_' + moduleId).find('.req_for_edit');
                                $btn.addClass('cancel_req_for_edit').removeClass('req_for_edit').text(mainLang('cancel_req_for_edit'));
                            }
                            $.showAlert(mainLang('req_submitted'));
                            $('.reedit_modal').modal('hide');
                        }
                    }
                });
            });

            $('body').on('click', '.cancel_req_for_edit', function (event) {
                var $btn = $(this);
                event.preventDefault();
                var $modulPart = $(this).parents('.modul_part');
                var moduleId = $modulPart.find('.modul_part_id').attr('data-id') ? $modulPart.find('.modul_part_id').attr('data-id') : 0;

                if (moduleId == 0) {
                    return false;
                }

                $.ajaxFn({
                    url: ELN_URL + '?r=experiment/cancel-req-reedit',
                    data: {
                        exp_id: $('.exp_conetnt.active #exp_id').val(),
                        module_id: moduleId
                    },
                    success: function (res) {
                        if (res.status == 1) {
                            $btn.addClass('req_for_edit').removeClass('cancel_req_for_edit').text(mainLang('req_for_edit'));
                        } else {
                            $.showAlert(res.info);
                        }
                    }
                });
            });
        }
    };
    moduleReedit.init();

    // 合著
    $('body').on('click', '.add-coauthor-setting', function () {
        $('.coauthor-setting-line:last').after($('#coauthor-setting-line-temp').html());
        $('.user-selector').each(function () {
            if ($(this).parents('#coauthor-setting-line-temp').length == 0) {
                $(this).fSelect({
                    placeholder: mainLang('Co-author'),
                    numDisplayed: 3,
                    onNumDisplayed: false,
                    overflowText: '{n} selected',
                    noResultsText: mainLang('search_no_results'),
                    searchText: mainLang('search'),
                    showSearch: true,
                    selectAll: true,
                });
            }
        });
        $('.module-selector').each(function () {
            if ($(this).parents('#coauthor-setting-line-temp').length == 0) {
                $(this).fSelect({
                    placeholder: mainLang('Component'),
                    numDisplayed: 3,
                    onNumDisplayed: false,
                    overflowText: '{n} selected',
                    noResultsText: mainLang('search_no_results'),
                    searchText: mainLang('search'),
                    showSearch: true,
                    selectAll: true,
                });
            }
        });
    });

    $('body').on('click', '.del-coauthor-setting', function () {
        $(this).parent().remove();
    });


    $('body').on('click', '.coauthor_submit_btn', function () {
        var setting = [];
        var valid = true;
        $('.coauthor-setting-line').each(function () {
            var userIds = $(this).find('.user-selector').val();
            var moduleIds = $(this).find('.module-selector').val();
            if (userIds || moduleIds) {
                if (!userIds || userIds == '') {
                    valid = false;
                    $.showAlert(mainLang('pls_select_coauthor'));
                    return false;
                }
                if (!moduleIds || moduleIds == '') {
                    valid = false;
                    $.showAlert(mainLang('pls_select_coauthor_module'));
                    return false;
                }

                setting.push({
                    user_ids: userIds,
                    module_ids: moduleIds
                });
            }
        });

        if (!valid) {
            return;
        }

        $.ajaxFn({
            url: ELN_URL + '?r=experiment/coauthor-submit',
            data: {
                exp_id: expId,
                setting: setting
            },
            success: function (res) {
                if (res.status != 1) {
                } else {
                    $('.coauthor-setting').modal('hide');
                }
            }
        });
    });

    // 清除评论
    $('body').on('click', '.clear_comments_submit_btn', function () {
        $.ajaxFn({
            url: ELN_URL + '?r=experiment/clear-comments',
            type: 'POST',
            data: {
                experiment_id: expId
            },
            success: function (res) {
                if (res.status != 1) {
                    console.log(res.info);
                } else {
                    $('.clear_comments_modal').modal('hide');
                    $.showAlert(mainLang('clear_comments_tip'));
                    require('tab').reloadActiveTag();
                }
            }
        });
    });

    // 设置前后实验
    var set_route = {
        init: function () {
            $('body').on('click', '.set-route', function () {
                var currentExpId = $(this).attr('exp_id') || $('.exp_conetnt.active #exp_id').val();
                var currentExpNum = $(this).attr('exp_num') || $('.exp_conetnt.active .info_part .book_name').html();
                var _this = this;

                $.popContent(_.template(require('text!popup/set_route_exp.html'))(), mainLang('set_pre_post_exp'), function () {
                    var beforeExpNumArr = [];
                    var afterExpNumArr = [];
                    $('.exp-num-input.before').each(function () {
                        var expNum = $.trim($(this).val());
                        if (expNum != '') {
                            beforeExpNumArr.push(expNum);
                        }
                    });
                    $('.exp-num-input.after').each(function () {
                        var expNum = $.trim($(this).val());
                        if (expNum != '') {
                            afterExpNumArr.push(expNum);
                        }
                    });
                    // $('.exp-num-input').each(function () {
                    //     var expNum = $.trim($(this).val());
                    //     if (expNum != '') {
                    //         expNumArr.push(expNum);
                    //     }
                    // });
                    $.ajaxFn({
                        url: ELN_URL + '?r=experiment/set-route',
                        type: 'POST',
                        data: {
                            exp_id: currentExpId,
                            before_exp_num_arr: beforeExpNumArr,
                            after_exp_num_arr: afterExpNumArr
                            // exp_num_arr: expNumArr
                        },
                        success: function (res) {
                            if (res.status == 1) {
                                if (this.data.exp_num_arr && this.data.exp_num_arr.length > 1) {
                                    $(_this).addClass('active')
                                } else {
                                    $(_this).removeClass('active')
                                }
                                $.closeModal();
                            }
                        }
                    });
                }, function () {
                    $('.exp-num-input.current_exp_curr').val(currentExpNum)
                    $('.exp-num-input.current_exp').val(currentExpNum);
                    $.ajaxFn({
                        url: ELN_URL + '?r=experiment/get-route',
                        type: 'GET',
                        data: {
                            exp_id: currentExpId
                        },
                        success: function (res) {
                            if (res.status == 1) {
                                var beforeExpList = res.data['before_exps'];
                                var afterExpList = res.data['after_exps'];
                                var count = beforeExpList.length + afterExpList.length;
                                if (count > 0) {
                                    var html = '';
                                    for (var i = 0; i < beforeExpList.length; i++) {
                                        var html2 = '<div class="exp-num-input-box"><input type="text" class="exp-num-input before" value="' + beforeExpList[i].num + '" placeholder="' + mainLang('input_exp_num') + '"/><span class="del prev-del"></span> </div><div class="route_link_img ml5"></div>';
                                        $('.left-route-exp-box').append(html2)
                                    }
                                    for (var i = 0; i < afterExpList.length; i++) {
                                        var html2 = '<div class="exp-num-input-box"><input type="text" class="exp-num-input after" value="' + afterExpList[i].num + '" placeholder="' + mainLang('input_exp_num') + '"/><span class="del prev-del"></span> </div><div class="route_link_img ml5"></div>';
                                        $('.right-route-exp-box').append(html2)
                                    }
                                    $('.route-exps-box').html(html);
                                }
                            }
                        }
                    });
                });

                return false; // 阻止事件传递，否则在实验列表中点击会打开实验
            });

            $('body').on('click', '.add-route-exp-btn', function () {
                if ($(this).hasClass('add-prev')) {
                    // var html = '<div class="exp-num-input-box"><input type="text" class="exp-num-input" placeholder="' + mainLang('input_exp_num') + '"/><span class="del prev-del"></span> </div><div class="route_link_img ml5"></div><div class="arrow-box">-></div>';
                    // $('.exp-num-input.current_exp').parent().before(html);
                    var html2 = '<div class="exp-num-input-box"><input type="text" class="exp-num-input before" placeholder="' + mainLang('input_exp_num') + '"/><span class="del prev-del"></span> </div><div class="route_link_img ml5"></div>';
                    $('.left-route-exp-box').append(html2)
                } else {
                    // var html = '<div class="arrow-box">-></div><div class="exp-num-input-box"><input type="text" class="exp-num-input" placeholder="' + mainLang('input_exp_num') + '"/><span class="del next-del"></span></div><div class="route_link_img ml5"></div>';
                    // $('.exp-num-input.current_exp').parent().after(html);
                    var html2 = '<div class="exp-num-input-box"><input type="text" class="exp-num-input after" placeholder="' + mainLang('input_exp_num') + '"/><span class="del prev-del"></span> </div><div class="route_link_img ml5"></div>';
                    $('.right-route-exp-box').append(html2)
                }
            });

            //feature:实验关系总览
            $('body').on('click', '.export_as_img_btn', function () {
                var expNum = $(".current_exp_curr").val();
                if (!expNum) {
                    return;
                }
                $.ajaxFn({
                    noLoad: true,
                    url: ELN_URL + '?r=experiment/get-exp-id-by-exp-num',
                    type: 'GET',
                    data: {
                        exp_num: expNum
                    },
                    success: function (res) {
                        if (res.status === 1) {
                            if (res.data) { // 获取到了非空id
                                $.closeModal(); //关闭当前设置路线窗口
                                require('get_html').getRelationshipPage(res.data);
                            }
                        }
                    }
                })
            });

            $('body').on('click', '.exp-num-input-box .del', function () {
                $parentDom = $(this).parent();
                if ($(this).hasClass('prev-del')) {
                    $parentDom.next().remove();
                } else {
                    $parentDom.prev().remove();
                }
                // $parentDom.next().remove(); // add by hkk 2019/3/20 多加了链接图(在input框后面)后也得删除
                $parentDom.remove();

            });

            $('body').on('mouseover', '.exp-num-input', function () {
                var $inputObj = $(this);
                var expNum = $.trim($inputObj.val());
                var $abbrInfoBox = $('.exp-abbr-info-box');

                if (expNum == '') {
                    $abbrInfoBox.html('');
                    return false;
                }

                $inputObj.attr('title', expNum);

                if (expNum == $inputObj.attr('data-num') && $inputObj.data('data-abbr') != '') {
                    $abbrInfoBox.html($inputObj.data('data-abbr'));
                    return false;
                }

                $.ajaxFn({
                    noLoad: true,
                    url: ELN_URL + '?r=experiment/get-abbr-info-by-exp-num',
                    type: 'GET',
                    data: {
                        exp_num: expNum
                    },
                    success: function (res) {
                        if (res.status == 1) {
                            var abbrInfo = res.data ? res.data : '';
                            $abbrInfoBox.html(abbrInfo);
                            $inputObj.attr('data-num', expNum).data('data-abbr', abbrInfo);
                        }
                    }
                });
            });

            $('body').on('mouseout', '.exp-num-input', function () {
                $('.exp-abbr-info-box').html('');
            });

            // add by hkk 2019/3/20 设置前后实验 加上链接图标 点击关闭当前窗口，弹出相应实验
            $('body').on('click', '.route_link_img', function () {
                // 获取对应实验编号
                var expNum = $(this).prev().find('.exp-num-input').val();
                if (!expNum) {
                    return;
                }
                $.ajaxFn({
                    noLoad: true,
                    url: ELN_URL + '?r=experiment/get-exp-id-by-exp-num',
                    type: 'GET',
                    data: {
                        exp_num: expNum
                    },
                    success: function (res) {
                        if (res.status === 1) {
                            if (res.data) { // 获取到了非空id
                                $.closeModal(); //关闭当前设置路线窗口
                                require('get_html').genExpPage(res.data);
                            }
                        }
                    }
                })
            });
        },
    };
    set_route.init();

    //add by wy 2023/5/10 复制弹框中的全选功能
    $('body').on('click', '.pop_modal #duplicatedModuleAll', function () {
        var $modal = $(this).closest('.pop_modal');
        if ($(this).prop('checked')) {
            $modal.find('input[name="duplicatedModule"]').prop('checked', true);
        } else {
            $modal.find('input[name="duplicatedModule"]').prop('checked', false);
            $modal.find('input[name="duplicatedModuleContentAll"]').prop('checked', false);
            $modal.find('input[name="duplicatedModuleContent"]').prop('checked', false);
        }
    });
    $('body').on('click', '.pop_modal input[name="duplicatedModule"]', function () {
        var $modal = $(this).closest('.pop_modal');
        var checkboxes = $modal.find('input[name="duplicatedModule"]');
        $modal.find('#duplicatedModuleAll').prop('checked', checkboxes.length == checkboxes.filter(':checked').length)
        if (!$(this).prop('checked')) {
            $(this).closest('tr').find('input[name="duplicatedModuleContent"]').prop('checked', false);
            var contentCheckboxes = $modal.find('input[name="duplicatedModuleContent"]');
            $modal.find('#duplicatedModuleContentAll').prop('checked', contentCheckboxes.length == contentCheckboxes.filter(':checked').length)
        }
    })

    $('body').on('click', '.pop_modal input[name="duplicatedModuleContent"]', function () {
        var $modal = $(this).closest('.pop_modal');
        var contentCheckboxes = $modal.find('input[name="duplicatedModuleContent"]');
        $modal.find('#duplicatedModuleContentAll').prop('checked', contentCheckboxes.length == contentCheckboxes.filter(':checked').length)
        if ($(this).prop('checked')) {
            $(this).closest('tr').find('input[name="duplicatedModule"]').prop('checked', true);
            var checkboxes = $modal.find('input[name="duplicatedModule"]');
            $modal.find('#duplicatedModuleAll').prop('checked', checkboxes.length == checkboxes.filter(':checked').length)
        }
    })

    $('body').on('click', '.pop_modal #duplicatedModuleContentAll', function () {
        var $modal = $(this).closest('.pop_modal');
        if ($(this).prop('checked')) {
            $modal.find('input[name="duplicatedModuleContent"]').prop('checked', true);
            $modal.find('input[name="duplicatedModuleAll"]').prop('checked', true);
            $modal.find('input[name="duplicatedModule"]').prop('checked', true);
        } else {
            $modal.find('input[name="duplicatedModuleContent"]').prop('checked', false);
        }
    });
    $('body').on('click', '.pop_modal input[name="duplicatedModuleContent"]', function () {
        var $modal = $(this).closest('.pop_modal');
        var checkboxes = $modal.find('input[name="duplicatedModuleContent"]');
        $modal.find('#duplicatedModuleContentAll').prop('checked', checkboxes.length == checkboxes.filter(':checked').length)
    })

    $('body').on('click', '.toggle-revision-trace', function () {
        var revisionTraceClass = '.revision-trace{display:none !important;}';
        if ($(this).hasClass('show')) {
            revisionTraceClass = '.revision-trace{display:inline !important;}table.revision-trace {display:table !important;}td.revision-trace {display:table-cell !important;}';
            $(this).text(mainLang('hide_revision_trace'));
            $(this).removeClass('show');
        } else {
            $(this).text(mainLang('show_revision_trace'));
            $(this).addClass('show');
        }
        $('[modul_line].tinymce_line').each(function () {
            var $module = $(this).find('[modul_part]');
            var id = $('.tinymce_textarea', $module).attr('id');
            var editor = UE.getEditor(id);
            if (editor) {
                UE.utils.cssRule('revision-trace', revisionTraceClass, editor.document);
            }
        });
    });

    // 导出物料数据
    $('body').on('click', '#export_materials_data', function () {
        if (window.pdfDown && window.pdfDown.pdfExporting) {
            $.showAlert(mainLang('file_exporting'));
            return;
        }

        // 初始化进度球
        function initWaveLoading(waiting) {
            pdfDown.waveLoading = waveLoadingFn();
            pdfDown.pdfExporting = true;
            $("#export-canvas-wrap").show().html('<canvas id="export-canvas" width="110" height="110"></canvas>');
            pdfDown.waveLoading.init({
                showText: true,
                alpha: 0,
                textSize: '24px',
                bgColor: '#fff',
                color: '#1387ff',
                target: document.querySelector("#export-canvas"),
                callback: function () {
                    $("#export-canvas-wrap").hide();
                    pdfDown.waveLoading = null;
                }
            });
            pdfDown.waveLoading.drawWaiting();
            pdfDown.waveLoading.setWaiting(waiting || 0);
        }

        // 获取导出进度
        function getExportProgess() {
            $.ajaxFn({
                url: ELN_URL + '?r=download/get-exp-progress',
                noLoad: true,
                success: function (res) {
                    if (res.status != 0) {
                        if (pdfDown.waveLoading) {
                            if (res.data.waiting != null) {
                                pdfDown.waveLoading.setWaiting(parseFloat(res.data.waiting));
                            } else {
                                pdfDown.waveLoading.setWaiting(0);
                                pdfDown.waveLoading.setProgress(parseFloat(res.data.progress));
                            }
                        }

                        if (res.data.progress == null || parseInt(res.data.progress) < 100) {
                            setTimeout(function () {
                                getExportProgess();
                            }, 3000);
                        } else {
                            $("#export-canvas-wrap").hide();
                            location.href = ELN_URL + '?r=download/book&book_id=' + res.data.book_id + '&id=' + res.data.id;
                            pdfDown.book_id = res.data.book_id;
                            pdfDown.pdfExporting = false;
                            pdfDown.waveLoading = null;
                        }
                    }
                }
            });
        }

        var exportCondition = {};
        var group_id = $('.exp_conetnt.active .detail_list_box [name="select_group"]').val();
        if (group_id > 0) {
            exportCondition.group_id = group_id;
        }
        var project_id = $('.exp_conetnt.active .detail_list_box [name="select_project"]').val();
        if (project_id > 0) {
            exportCondition.project_id = project_id;
        }
        var create_time_start = $('.exp_conetnt.active .detail_list_box .create_time [name="start_time"]').val();
        if (create_time_start) {
            exportCondition.create_time_start = create_time_start;
        }
        var create_time_end = $('.exp_conetnt.active .detail_list_box .create_time [name="end_time"]').val();
        if (create_time_end) {
            exportCondition.create_time_end = create_time_end;
        }
        var update_time_start = $('.exp_conetnt.active .detail_list_box .update_time [name="edit_start_time"]').val();
        if (update_time_start) {
            exportCondition.update_time_start = update_time_start;
        }
        var update_time_end = $('.exp_conetnt.active .detail_list_box .update_time [name="edit_end_time"]').val();
        if (update_time_end) {
            exportCondition.update_time_end = update_time_end;
        }
        var template_id = $('.exp_conetnt.active .detail_list_box [name="exp_template"]').val();
        if (template_id > 0) {
            exportCondition.template_id = template_id;
        }

        $.ajaxFn({
            url: ELN_URL + '?r=file/export-materials-data',
            data: {
                export_condition: exportCondition
            },
            type: 'POST',
            success: function (res) {
                if (res.status == 1) {
                    initWaveLoading();
                    getExportProgess();
                }
            }
        });
    });

    // add by wy 2023/4/13 新增按回车提交数据的功能
    $('body').on('keydown', '.enter-submit-box .pop_input_password', function (event) {
        if (event.keyCode == 13) {
            var $modalDialog = $(this).closest('.modal-dialog');
            var $submitBtn = $modalDialog.find('.modal-footer button.submit');
            $submitBtn.trigger('click');
        }
    });

    return expTool;
});
