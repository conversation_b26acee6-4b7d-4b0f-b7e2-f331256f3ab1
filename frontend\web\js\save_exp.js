define(function (require) {
    const {
        ModuleStatusEnum,
    } = require('./components/save_exp/save_exp_const');

    var autoSaveInterval = 6000; // 自动保存检测时间间隔（毫秒）
    var saveLocked = false;
    var previousData;

    var getModuleData = function($module, index) {
        var type = $module.data('type');

        var moduleConfig, column_config, totalWidth, orderIds;  // add by hkk 2019/10/12

        const $moduleDataStore = $('.module_data_store', $module);
        const expId = $moduleDataStore.data('exp-id') || $('.module_data_store', '.exp_conetnt.active').attr('data-exp-id');

        // 模块与实验关联表experiment_relay.id
        const expModuleRelayId = Number($module.find('.modul_part_id').attr('data-id') ?? 0);
        // 定义模块的数据格式
        var moduleData = {
            info: {
                component_id: $module.find('.modul_part_id').val() ? parseInt($module.find('.modul_part_id').val().split('_')[1], 10) : '',
                component_type: $module.find('.modul_part_id').attr('component_type') ? parseInt($module.find('.modul_part_id').attr('component_type')) : '',
                name: $module.find('.title_input').val(),
                real_id: $module.find('.modul_part_id').attr('data-id') ? $module.find('.modul_part_id').attr('data-id') : '',
                exp_module_relay_id: expModuleRelayId,
                class: parseInt(index || '0') + 1,
            },
            data: {}
        };

        switch (type) {
            case 'abstract_data':
            case 'operation_data':
            case 'discuss_data':
            case 'lite_data':
            case 'editor_data':
                var id = $('.tinymce_textarea', $module).attr('id');
                var switch_home_page_dir_action = $('.switch_home_page_dir', $module).attr('data-action');
                var is_home_page_dir = ('cancel' === switch_home_page_dir_action);
                var editorObj = UE.getEditor(id);
                if (editorObj) {
                    moduleData.info.height = editorObj.getHeight() ? editorObj.getHeight() : '0';
                    moduleData.data.html = editorObj.getContent();
                    moduleData.data.links = editorObj.getLinks(); // 获取文本编辑器内链接 Jiangdm
                    moduleData.info.height = editorObj.getHeight() ? editorObj.getHeight() : '0';
                    moduleData.info.config = JSON.stringify({
                        is_home_page_dir: is_home_page_dir
                    });
                }
                break;
            case 'chem_data':

                /** @type {InMaterial} */
                const InMaterial = require('in-material');
                const [materialData, err] = InMaterial.getMaterialFormatDB(expId);

                let saveMaterialData = materialData;
                if (null != err) {
                    // 加载物料表的初始数据, 并转化为后端保存的格式
                    const currMaterialDataJson = $.findInActiveTab(`[name='material-data']`).text();
                    const currMaterialData = JSON.parse(currMaterialDataJson);
                    const material = InMaterial.createMaterialFromDatabase(expId, currMaterialData);
                    const materialFormatDatabase = InMaterial.getMaterialFormatDatabase(material)

                    // 物料表加载未完成
                    if (err === InMaterial?.materialErr?.GetMaterialErr?.MaterialNotLoaded) {
                        console.error(`实验: ${expId}. 物料表未加载完成, 使用实验初始的物料数据`);
                        saveMaterialData = materialFormatDatabase;
                    }
                    // 物料表数据异常
                    if (err === InMaterial?.materialErr?.GetMaterialErr?.MaterialStoreErr) {
                        console.error(`实验: ${expId}. 物料表store未加载, 使用实验初始物料数据`);
                        saveMaterialData = materialFormatDatabase;
                    }
                }
                if (null == saveMaterialData) {
                    $.showAlert('Err: experiment material data error, cannot save');
                    return moduleData;
                }
                moduleData.data.in_material = saveMaterialData;

                // 单位相关数据
                moduleData.data.chem_data = {
                    mass_unit: $module.find('.substrates_mass_unit').val(),
                    nb_unit: $module.find('.substrates_nb_unit').val(),
                    volume_unit: $module.find('.substrates_volume_unit').val(),
                    solvent_mass_unit: $module.find('.solvent_mass_unit').val(),
                    solvent_volume_unit: $module.find('.solvent_volume_unit').val(),
                    produce_theo_unit: $module.find('.product_theo_unit').val(),
                    produce_mass_unit: $module.find('.product_mass_unit').val(),
                    produce_nb_unit: $module.find('.product_nb_unit').val(),
                    product_yield_type: $module.find('.cal_yield_input').val()
                };

                // 结构相关数据
                moduleData.data.chem_data.smiles = $('.exp_conetnt.active .all_smiles').val();
                // moduleData.data.chem_data.html_data = $('.html_data').val();
                // moduleData.data.chem_data.svg_data = $('.svg_data').val();
                //var indraw = document.getElementById('detial_indraw') && document.getElementById('detial_indraw').contentWindow.indraw;
                // var indraw = $('.exp_conetnt.active #detial_indraw')[0] && $('.exp_conetnt.active #detial_indraw')[0].contentWindow.indraw;

                const $iframeInDraw = $('.exp_conetnt.active #detial_indraw'); //! indraw的window对象
                const indrawWindow = $iframeInDraw[0] && $iframeInDraw[0].contentWindow;
                const indraw = $iframeInDraw[0] && $iframeInDraw[0].contentWindow.indraw;

                if (indraw) {
                    moduleData.data.chem_data.indraw_data = indraw.getMultiMol();
                    var pngData = indraw.getCanvasImage(true);
                    // 画布为空时pngData可能是undefined
                    moduleData.data.chem_data.png_data = pngData ? pngData : '';
                } else {
                    //如果indraw没有加载成功，那么不从编辑器中拿数据，而是直接拿之前的数据。
                    moduleData.data.chem_data.indraw_data = $('.exp_conetnt.active .indraw_data').val() || '';
                    moduleData.data.chem_data.png_data = $('.exp_conetnt.active .png_data').val() || '';
                }

                //moduleData.data.chem_data.show_details = $('.details-switch').hasClass('show') ? 1 : 0;
                moduleData.data.chem_data.show_details = $('.exp_conetnt.active .collapse_material_ico').hasClass('show') ? ($('.exp_conetnt.active .details-switch').hasClass('show') ? 1 : 0) : 2; //  modified by hkk 2019/9/19
                moduleData.data.chem_data.height = $module.find('#detial_indraw').height();
                moduleData.info.height = $module.find('#detial_indraw').height();

                // 物料表数据
                var chem_type = ['catalysts', 'solvent', 'substrates', 'product', 'condition'];
                for (var i = 0; i < chem_type.length; i++) {
                    var type = chem_type[i];
                    moduleData.data[type + '_data'] = [];
                    $module.find('.tr_.' + type).each(function () {
                        // var formData = $.formSerializeFn($(this));

                        const $tr = $(this);
                        const formData = $tr.serializeMaterialDataForm({
                            nameAttr: 'name',
                            valFnArr: [($el) => $el.attr('cal_value'), ($el) => $el.val()],
                        });

                        if (type === 'product') { // add by hkk 2022/3/21,CMS编号只后台保存
                            delete formData.product_cms_code;
                            delete formData.product_cms_id;
                        }

                        if ($(this).find('textarea[property="mol"]').length > 0) {
                            var molStr = $(this).find('textarea[property="mol"]').val();
                            formData[type + '_mol'] = decodeURIComponent(molStr);
                        }
                        if (type != 'condition') {
                            formData.name_isinput = $(this).find('[property="name"]').attr('isinput') || '0';
                            formData.nb_isinput = $(this).find('[property="nb"]').attr('isinput') || '0';
                            formData.mass_isinput = $(this).find('[property="mass"]').attr('isinput') || '0';
                            formData.volume_isinput = $(this).find('[property="volume"]').attr('isinput') || '0';
                            formData.eq_isinput = $(this).find('[property="equivalent"]').attr('isinput') || '0';
                            formData.yield_isinput = $(this).find('[property="yield"]').attr('isinput') || '0';
                            formData.come_from = $(this).find('[property="name"]').attr('come_from') || '';
                        }

                        //add by hkk 2019/3/15  改变手写输入标记的存储规则，直接存储到一个字符串中
                        var userInputString = '',isBase = 0;
                        if( !!$(this).find('[property="name"]').hasClass("userInputData")){
                            userInputString +="name,";
                        }
                        if( !!$(this).find('[property="equivalent"]').hasClass("userInputData")){
                            userInputString +="eq,";
                        }
                        if( !!$(this).find('[property="nb"]').hasClass("userInputData")){
                            userInputString +="nb,";
                        }
                        if( !!$(this).find('[property="molweight"]').hasClass("userInputData")){
                            userInputString +="molweight,";
                        }
                        if( !!$(this).find('[property="mass"]').hasClass("userInputData")){
                            userInputString +="mass,";
                        }
                        if( !!$(this).find('[property="dengsity"]').hasClass("userInputData")){
                            userInputString +="density,";
                        }
                        if( !!$(this).find('[property="volume"]').hasClass("userInputData")){
                            userInputString +="volume,";
                        }
                        if( !!$(this).find('[property="temperature"]').hasClass("userInputData")){
                            userInputString +="temperature,"; //反应物浓度
                        }
                        if( !!$(this).find('[property="pressure"]').hasClass("userInputData")){
                            userInputString +="pressure,";   //反应物纯度
                        }
                        if( !!$(this).find('[property="theo"]').hasClass("userInputData")){
                            userInputString +="theo,";      //产物理论质量
                        }
                        if( !!$(this).find('[property="yield"]').hasClass("userInputData")){
                            userInputString +="yield,";
                        }
                        if( !!$(this).find('[property="purity"]').hasClass("userInputData")){
                            userInputString +="purity,"; //产物纯度
                        }
                        if( !!$(this).find('[property="ratio"]').hasClass("userInputData")){
                            userInputString +="ratio,"; //产物纯度
                        }

                        formData.user_input_fields = userInputString.substring(0,userInputString.length-1);//去掉最后的逗号

                        // 增加是否基准物
                        if( $(this).find('[property="is_base"]')[0] && $(this).find('[property="is_base"]')[0].checked){
                            isBase = 1
                        }
                        formData.is_base = isBase;
                        //end

                        // add by hkk 2019/9/5 增加保存额外列信息
                        var define_value = [];
                        $(this).find('.addedColumnValue').each(function () {
                            define_value.push({
                                name: $(this).parent().attr('data-name'),
                                value:$(this).val(),
                            })
                        });
                        formData.define_value = JSON.stringify(define_value);

						// 保存仪器数据相关信息 jiangdm 2022/11/4
                        var reference = $(this).find('input.materiel_mass_input, input.product_mass_input');
                        var refItems = {};
                        if (reference.hasClass('instrument-data-input')) {
                            refItems.mass = {
                                batch_number: reference.data('batch_number'),
                                data_id: reference.data('data_id'),
                                instrument_type: reference.data('instrument_type'),
                                modified: reference.hasClass('modified') ? 1 : 0,
                            }
                        }
                        formData.instrument_data_info = JSON.stringify(refItems);

                        moduleData.data[type + '_data'].push(formData);
                    });
                }

                // 反应明细数据
                moduleData.data['details_data'] = [];
                $('.exp_conetnt.active .details_tr').each(function () {
                    var formData = $.formSerializeFn($(this));

                    // add by hkk 2019/7/1 增加保存上传模块数据
                    var filesArray = [];
                    $(this).find('.single_detail_file').each(function(){
                        var allFileData = $.formSerializeFn($(this));
                        var fileData = {
                            dep_path: allFileData.dep_path ? allFileData.dep_path : '',
                            save_name: allFileData.save_name ? allFileData.save_name : '',
                            real_name: allFileData.file_name ? allFileData.file_name : ''
                        };
                        filesArray.push(fileData)
                    });

                    formData.detail_upload_files = JSON.stringify(filesArray);

                    //删除多余生成的数组元素
                    delete formData['save_name'];
                    delete formData['dep_path'];
                    delete formData['files[]'];
                    delete formData['file_name']
                    // end by hkk 2019/7/1

                    // add by hkk 2019/9/10 增加保存额外列信息
                    var define_value = [];
                    $(this).find('.addedColumnValue').each(function () {
                        define_value.push({
                            name: $(this).parent().attr('data-name'),
                            value:$(this).val(),
                        })
                    });
                    formData.define_value = JSON.stringify(define_value);


                    moduleData.data['details_data'].push(formData);
                });


                // ADD BY HKK 2019/9/6 新增物料表列是否隐藏和额外列配置
                var substratesConfig = [];
                var solventConfig = [];
                var conditionConfig = [];
                var productConfig = [];
                var detailConfig = [];

	            $('.exp_conetnt.active .substrates_title td, .exp_conetnt.active .solvent_title td, .exp_conetnt.active .condition_title td, .exp_conetnt.active .product_title td, .exp_conetnt.active .details-title td').each(function () {

                    var item = $(this);
                    if (typeof (item.attr("data-name")) == "undefined") {
                        return
                    }


                    var totalWidth =  parseFloat(document.defaultView.getComputedStyle(item.parents('tr')[0],null).width);
                    var config = {
                        name: item.attr('data-name'),
                        show: !item.hasClass('hide'),
                        width:parseFloat(document.defaultView.getComputedStyle(item[0],null).width) * 100 / totalWidth  +'%' , //(parseFloat(item.css('width')) - 8 ) + 'px'
                       // width: parseFloat(item.width()) * 100 / totalWidth  +'%' , //(parseFloat(item.css('width')) - 8 ) + 'px'
                        //width: parseFloat(item.css('width')) * 100 / totalWidth  +'%' , //(parseFloat(item.css('width')) - 8 ) + 'px'
                        define: item.hasClass('addedMaterialColumn'),
                        dict_id: item.attr('data-id'),
                    };


                    if (item.parent().hasClass('substrates_title')) {
                        substratesConfig.push(config)
                    } else if (item.parent().hasClass('solvent_title')) {
                        solventConfig.push(config)
                    } else if (item.parent().hasClass('condition_title')) {
                        conditionConfig.push(config)
                    } else if (item.parent().hasClass('product_title')) {
                        productConfig.push(config)
                    } else if (item.parent().hasClass('details-title')) {
                        detailConfig.push(config)
                    }

                });

                moduleData.data.chem_data.material_column_config = JSON.stringify({
                    'substratesConfig':substratesConfig,
                    'solventConfig':solventConfig,
                    'conditionConfig':conditionConfig,
                    'productConfig':productConfig,
                    'detailConfig':detailConfig,
                })

                break;
            case 'tlc_data':
                moduleData.data.tlc_data = {};
                // indraw部分
                var tlcIndraw = $('.exp_conetnt.active #indraw-tlc')[0] && $('.exp_conetnt.active #indraw-tlc')[0].contentWindow.indraw;
               // var tlcIndraw = document.getElementById('indraw-tlc') && document.getElementById('indraw-tlc').contentWindow.indraw;
                if (tlcIndraw) {
                    moduleData.data.tlc_data.indraw_data = tlcIndraw.getMultiMol() ? tlcIndraw.getMultiMol() : '';
                    moduleData.data.tlc_data.png_data = tlcIndraw.getCanvasImage(true) ? tlcIndraw.getCanvasImage(true) : '';
                } else {
                    // 如果indraw没有加载成功，那么不从编辑器中拿数据，而是直接拿之前的数据。
                    moduleData.data.tlc_data.indraw_data = $('.exp_conetnt.active .indraw_tlc_data').val() || '';
                    moduleData.data.tlc_data.png_data = '';
                }
                moduleData.data.tlc_data.height = $module.find('#indraw-tlc').height();
                moduleData.info.height = $module.find('#indraw-tlc').height();

                // 表格部分
                moduleData.data.tlc_data.table_data = [];
                $module.find('.tlcData').each(function () {
                    var formData = $.formSerializeFn($(this));
                    moduleData.data.tlc_data.table_data.push(formData);
                });


                // BEGIN add by hkk 2019/10/12 存储列配置

                // 模块配置信息
                moduleConfig = {};

                // 列宽配置
                totalWidth =  $('.exp_conetnt.active .tlc_table').width();
                column_config = [];
                $module.find('.tlc_table th').each(function (index) {
                    var item = $(this);
                    var config = {
                        name: item.attr('data-name'),
                        show: !item.hasClass('hide'),
                        width: parseFloat(item.css('width')) * 100 / totalWidth  +'%' ,
                    };
                    column_config.push(config);
                });

                moduleConfig.column_config = column_config;
                moduleData.info.config = JSON.stringify(moduleConfig);
                //END


                break;
            case 'reference_data':
            case 'biology_data':
                var selector = type == 'reference_data'? '.referenceData' : '.biologyData';
                moduleData.data.table_data = [];
                $module.find(selector).each(function () {
                    var formData = $.formSerializeFn($(this));

                    // modified by hkk 2019/10/4/14   新增参考文献支持多文件上传合
                    var filesArray = [];
                    $(this).find('.single_detail_file').each(function(){
                        var allFileData = $.formSerializeFn($(this));
                        var fileData = {
                            dep_path: allFileData.dep_path ? allFileData.dep_path : '',
                            save_name: allFileData.save_name ? allFileData.save_name : '',
                            real_name: allFileData.file_name ? allFileData.file_name : ''
                        };
                        filesArray.push(fileData)
                    });
                    formData.upload_file = JSON.stringify(filesArray);

                    moduleData.data.table_data.push(formData);
                });

                // BEGIN  add by hkk 2019/10/11  存储列配置

                // 模块配置信息
                moduleConfig = {};

                // 列宽配置
                var tableSelector = type === 'reference_data'? '.reference_table' : '.biological_table';
                // totalWidth =  $(tableSelector).width();
                totalWidth = $module.find(tableSelector).width(); // modified by hkk 2021/5/7
                column_config = [];
                $module.find(tableSelector +' th').each(function () {
                    var item = $(this);
                    var config = {
                        name: item.attr('data-name'),
                        show: !item.hasClass('hide'),
                        width: parseFloat(item.css('width')) * 100 / totalWidth  +'%' ,
                    };
                    column_config.push(config);
                });
                moduleConfig.column_config = column_config;
                moduleData.info.config = JSON.stringify(moduleConfig);
                // END

                break;
            case 'upload_img_data':
                // 模块配置信息
                moduleConfig = {};
                orderIds = []; //图片的顺序
                $module.find('.image_list .file_up_box').each(function () {
                    orderIds.push($(this).attr('fileid'));
                })
                moduleConfig.orderIds = orderIds;
                moduleData.info.config = JSON.stringify(moduleConfig);
                break;
            case 'upload_file_data':
                moduleData.data.file_data = [];
                break;
            case 'comment':
                break;
            case 'define_data':
                var defineData = getData.define_data($module, 'define_data');
                moduleData.data = {
                    field_key: defineData.field_key,
                    field_value: defineData.field_value,
                    instrument_ref: defineData.instrument_ref,
                };

                // BEGIN add by hkk 2019/10/12 存储列配置

                // 模块配置信息
                moduleConfig = {};

                // 列宽配置
                // totalWidth =  $('.exp_conetnt.active .define_table').width();
                // totalWidth = $module.find('.define_table').width(); // modified by hkk 2021/5/7 精确当当前模块的表格宽度
                totalWidth = $module.width() - 2; // 如果模块被折叠，上面的width可能不准(bug#26163) by zhuhuajun 2021/5/7
                column_config = [];
                $module.find('.define_table th').each(function (index) {
                    var item = $(this);
                    var name = item.attr('data-name') ? item.attr('data-name'): 'define_field'+(index+1); // 只有上传文件和加列字段有data-name
                    var config = {
                        name: name,
                        show: !item.hasClass('hide'),
                        width: parseFloat(item.css('width')) * 100 / totalWidth  +'%' ,
                    };
                    column_config.push(config);
                });

                moduleConfig.column_config = column_config;
                moduleData.info.config = JSON.stringify(moduleConfig);
                //END


                break;
            case 'custom_table_data':
                var $customTable = $module.find('.customTableData');
                var table = $customTable.handsontable('getInstance');
                table.validateCells(function (valid) {
                    if (valid !== true) {
                        return false;
                    }
                });
                var collection = table.getPlugin('mergeCells').mergedCellsCollection;
                var customTableFun = require('./custom_table');
                var settings = customTableFun.saveTableSettings(table, false);
                settings.mergedCells = collection.mergedCells;
                delete settings['data'];
                var tableData = table.getData().map((x, index1) => x.map((y, index2) => {
                    if (y && typeof y === 'string' && y.indexOf('=') !== -1 && (y.split('=').length - 1) === 1) {
                        var integleExtra = table.getCellMeta(index1, index2).integleExtra;
                        var cacheIntegleExtra = {'integleExtra': Object.assign(integleExtra, {formulaParse: y})};
                        table.setCellMetaObject(index1, index2, cacheIntegleExtra);
                        y = integleExtra && integleExtra.formula === true ? table.getCell(index1, index2).innerHTML : y;
                    }
                    return y;
                }));
                moduleData.data = {
                    settings: JSON.stringify(settings),
                    data: JSON.stringify(tableData)
                };
                break;
            case 'excel_data':
                moduleData.data.content = $module.find('.excel_module').getExcel();
                break;
            case 'xsheet_data': // add by hkk 2020/3/11

                moduleData.data = {};
                var xsheet = $module.find('.detail_xsheet');
                if (xsheet) {
                    var xs = $module.find('.detail_xsheet')[0].contentWindow.xs;
                    if (xs) {
                        moduleData.data.data = JSON.stringify(xs.sheet.getSheets());
                        moduleData.data.setting = JSON.stringify(xs.sheet.getSetting());
                        moduleData.data.links = xs.sheet.getLinks(); // 获取intable内链接 jiangdm
                        if (xs.sheet.getConditionEmailData().length > 0) {
                            moduleData.data.emailData = JSON.stringify(xs.sheet.getConditionEmailData());
                        }

                    } else {
                        var getIntableData = $module.find('.detail_xsheet')[0].contentWindow.getIntableData;
                        var getTrace = $module.find('.detail_xsheet')[0].contentWindow.getTrace;
                        var fileHasChange = $module.find('.detail_xsheet')[0].contentWindow.fileHasChange;

                        var getConditionEmailData = $module.find('.detail_xsheet')[0].contentWindow.getConditionEmailData;
                        if (getIntableData != null) {
                            // 新intable逻辑
                            var trace = getTrace();
                            moduleData.data.trace = trace;
                            moduleData.data.fileHasChange = fileHasChange;
                            if(fileHasChange || !previousData || !previousData.base_data) {
                                var intableData = getIntableData();
                                moduleData.data.data = intableData;
                                $module.find('.detail_xsheet')[0].contentWindow.fileHasChange = false;
                            } else {
                                var tmpCurrentData = {
                                    'experiment_id': $('.exp_conetnt.active #exp_id').val(),
                                    'template_id': $('.exp_conetnt.active #template_id').val(),
                                }
                                var previousType = previousData.base_data.experiment_id > 0 ? 'exp' : 'template';
                                var currentDataType = tmpCurrentData.experiment_id > 0 ? 'exp' : 'template';
                                if (!previousData || !previousData.base_data || previousData.module_data.length <= index || !previousData.module_data[index] || !previousData.module_data[index]['data']['data']) {
                                    moduleData.data.data = getIntableData();
                                }
                                else if (currentDataType !== previousType) {
                                    moduleData.data.data = getIntableData();
                                }
                                else if (currentDataType === 'exp' && previousData.base_data.experiment_id !== tmpCurrentData.experiment_id) {
                                    moduleData.data.data = getIntableData();
                                }
                                else  if (currentDataType === 'template' && previousData.base_data.template_id !== tmpCurrentData.template_id) {
                                    moduleData.data.data = getIntableData();
                                } else {
                                    moduleData.data.data = previousData.module_data[index]['data']['data'];
                                }
                            }


                            if (getConditionEmailData && getConditionEmailData().length > 0) {
                                moduleData.data.emailData = JSON.stringify(getConditionEmailData());
                            }
                        }
                        else {
                            //如果xsheet没有加载成功，那么不从sheet中拿数据，而是直接拿之前的数据。
                            let dataString = $module.find('.xsheet_data_string').text() || '';
                            // 之前的数据可能是加密压缩的，需要解密保存
                            var pako = top.require('pako1/pako');
                            while (dataString && dataString !== "" && dataString !== '{}' && dataString !== '""' &&
                            dataString[0] !== '[' && dataString[0] !== '{') {
                                // InTable数据被压缩了，需解压还原
                                dataString = pako.inflate(atob(dataString), { to: 'string' });
                            }
                            moduleData.data.data = dataString;
                        }
                    }
                    var config = {
                        height: xsheet.height(), // modified by hkk 2020/7/10 记录实时高度
                    };
                    moduleData.info.config = JSON.stringify(config);

                    // 刚打开实验或模板时，如果触发保存，可能InTable加载了，但数据还未加载完成
                    if (!moduleData.data.data) {
                        // 抛出异常，取消保存
                        throw {
                            message: 'missed intable data'
                        };
                    }
                }
                break;
        }
        return moduleData;
    };

    var getAllModuleData = function() {
        var moduleDataArr = [];
        $('.exp_conetnt.active [modul_line]').each(function (index, dom) {
            var $module = $(this).find('[modul_part]');
            moduleData = getModuleData($module, index);
            moduleDataArr.push(moduleData);
        });

        return moduleDataArr;
    };

    var data = {};
    var getData = {
        //UEditor lcy20181112
        getUEditorData: function (modul, type, nosubmit, index) {
            var tinymce_textarea = $('.tinymce_textarea', modul),
                id = tinymce_textarea.attr('id');
            // var tmp = tinymce && tinymce.get(id) ? tinymce.get(id).getContent() : '';
            var editorObj = UE.getEditor(id);
            if (editorObj) {
                var html = editorObj.getContent();
                var height = editorObj.getHeight() ? editorObj.getHeight() : '0';
            }

            var data_ = this.public_data(modul, index);
            data_.content = html;
            data_.height = height;
            //为空的时候
            return data_;
        },

        //摘要 可多个组件
        abstract_data: function (modul, type, nosubmit, index) {
            data.abstract_data = data.abstract_data ? data.abstract_data : [];
            var data_ = this.getUEditorData(modul, type, nosubmit, index);
            data.abstract_data.push(data_);
        },

        //实验操作
        operation_data: function (modul, type, nosubmit, index) {
            data.operation_data = data.operation_data ? data.operation_data : [];
            var data_ = this.getUEditorData(modul, type, nosubmit, index);
            data.operation_data.push(data_);
        },

        //结果与讨论
        discuss_data: function (modul, type, nosubmit, index) {
            data.discuss_data = data.discuss_data ? data.discuss_data : [];
            var data_ = this.getUEditorData(modul, type, nosubmit, index);
            data.discuss_data.push(data_);
        },

        //富文本编辑器
        diy_data: function (modul, type, nosubmit, index) {
            data.diy_data = data.diy_data ? data.diy_data : [];
            var data_ = this.getUEditorData(modul, type, nosubmit, index);
            data.diy_data.push(data_);
        },

        //文本编辑模块内容
        lite_data: function (modul, type, nosubmit, index) {
            data.lite_data = data.lite_data ? data.lite_data : [];
            var data_ = this.getUEditorData(modul, type, nosubmit, index);
            data.lite_data.push(data_);

        },

        //提取 评论数据 可多个组件
        comment_data: function (modul, type, nosubmit, index) {
            data[type] = data[type] ? data[type] : [];
            var data_ = this.public_data(modul, index);
            data_.relay_id = $('.comment_modlue', modul).attr('com-relay-id');
            data_.comment_ids = [];
            $('[name="comment_id"]', modul).each(function () {
                data_.comment_ids.push($(this).val());
            });
            data_.praise_ids = [];
            $('[name="praise_id"]', modul).each(function () {
                data_.praise_ids.push($(this).val());
            });
            data[type].push(data_);
        },

        //提取 化学计量数据 组件只能有一个
        chem_data: function (modul, type, nosubmit, index) {
            data[type] = data[type] ? data[type] : {};
            var chem_type = ['catalysts', 'solvent', 'substrates', 'product', 'condition'];
            var data_ = this.public_data(modul, index);
            data.chem_detail = data_;
            for (var i = 0; i < chem_type.length; i++) {
                getData.stechiometry_data(modul, chem_type[i]);
            }
            this.inDraw_data(modul, type);
            this.details_data();
        },

        //结构式数据
        inDraw_data: function (modul, type, nosubmit, index) { //$('.svg_data')
            var data_ = $.formSerializeFn(modul.find('.exp_condition'));
            data_.tep_unit = modul.find('.exp_condition [name="tep_unit"]').val();
            data_.time_unit = modul.find('.exp_condition [name="time_unit"]').val();
            data_.pressure_unit = modul.find('.exp_condition [name="pressure_unit"]').val();
            data[type] = data_;
            data[type].mass_unit = modul.find('.substrates_mass_unit').val(); // mass单位；1:mg; 2:g; 3:kg
            data[type].nb_unit = modul.find('.substrates_nb_unit').val(); // 物质的量单位 1：μmol;2:mmol;3:mol
            data[type].volume_unit = modul.find('.substrates_volume_unit').val(); // 体积单位 1:μL ; 2:mL ; 3: L
            data[type].reagent_mass_unit = modul.find('.reagent_mass_unit').val(); // mass单位；1:mg; 2:g; 3:kg
            data[type].reagent_nb_unit = modul.find('.reagent_nb_unit').val(); // 物质的量单位 1：μmol;2:mmol;3:mol
            data[type].reagent_volume_unit = modul.find('.reagent_volume_unit').val(); // 体积单位 1:μL ; 2:mL ; 3: L
            data[type].solvent_mass_unit = modul.find('.solvent_mass_unit').val(); // mass单位；1:mg; 2:g; 3:kg
            data[type].solvent_volume_unit = modul.find('.solvent_volume_unit').val(); // 体积单位 1:μL ; 2:mL ; 3: L
            data[type].produce_theo_unit = modul.find('.product_theo_unit').val(); // 产物的theo单位；1:mg; 2:g; 3:kg
            data[type].produce_mass_unit = modul.find('.product_mass_unit').val(); // 产物的mass单位；1:mg; 2:g; 3:kg
            data[type].produce_nb_unit = modul.find('.product_nb_unit').val(); // 产物的物质的量单位 1：μmol;2:mmol;3:mol
            data[type].product_yield_type = modul.find('.cal_yield_input').val();
            data[type].html_data = $('.exp_conetnt.active .html_data').val(); //jsdraw.getHtml? jsdraw.getHtml() : null;
            data[type].smiles = $('.exp_conetnt.active .all_smiles').val(); //jsdraw && jsdraw.getSmiledata ? jsdraw.getSmiledata() : null;
            data[type].svg_data = $('.exp_conetnt.active .svg_data').val();
            //var indraw = document.getElementById('detial_indraw') && document.getElementById('detial_indraw').contentWindow.indraw;
            var indraw = $('.exp_conetnt.active #detial_indraw')[0] && $('.exp_conetnt.active #detial_indraw')[0].contentWindow.indraw;

            // ls20171013修改。在indraw没加载好的时候保存或缓存。就拿原来的数据
            if (indraw) {
                data[type].indraw_data = indraw.getMultiMol();
                data[type].png_data = indraw.getCanvasImage(true);
            } else {
                //如果indraw没有加载成功，那么不从编辑器中拿数据，而是直接拿之前的数据。
                data[type].indraw_data = $('.exp_conetnt.active .indraw_data').val() || '';
                data[type].png_data = $('.exp_conetnt.active .png_data').val() || '';
            }
            // data[type].indraw_data = indraw ? indraw.getMultiMol() : '';
            // data[type].png_data = indraw ? indraw.getCanvasImage(true) : ' ';

           // data[type].show_details = $('.details-switch').hasClass('show') ? 1 : 0;
            data[type].show_details = $('.exp_conetnt.active .collapse_material_ico').hasClass('show') ? ($('.exp_conetnt.active .details-switch').hasClass('show') ? 1 : 0) : 2; // modified by hkk 2019/9/19 加上物料表整个隐藏值2

            data[type].height = modul.find('#detial_indraw').height();
        },

        //化学计算数据        催化剂 溶剂 底物 产物
        stechiometry_data: function (modul, type, nosubmit, index) {
            var field = type + '_data';
            data[field] = [];
            modul.find('.tr_.' + type).each(function () {
                var obj = $(this);
                var formdata = $.formSerializeFn(obj);

                if (type != 'condition') {
                    formdata.name_isinput = obj.find('[property="name"]').attr('isinput') || '0';
                    formdata.nb_isinput = obj.find('[property="nb"]').attr('isinput') || '0';
                    formdata.mass_isinput = obj.find('[property="mass"]').attr('isinput') || '0';
                    formdata.volume_isinput = obj.find('[property="volume"]').attr('isinput') || '0';
                    formdata.eq_isinput = obj.find('[property="equivalent"]').attr('isinput') || '0';
                    formdata.yield_isinput = obj.find('[property="yield"]').attr('isinput') || '0';
                    formdata.come_from = obj.find('[property="name"]').attr('come_from') || '';
                }

                if (type == 'product') {
                    formdata.analy_data = [];
                    obj.find('.delete_analisis').each(function () {
                        var data = $.formSerializeFn($(this));
                        formdata.analy_data.push(data);
                    });
                }
                data[field].push(formdata);
            });
        },

        // 反应明细数据
        details_data: function () {
            data['details_data'] = [];
            $('.exp_conetnt.active .details_tr').each(function () {
                var obj = $(this);
                var formdata = $.formSerializeFn(obj);
                data['details_data'].push(formdata);
            });
        },

        //提取电子表格数据
        excel_data: function (modul, type, nosubmit, index) {
            data[type] = {};
            var content = modul.find('.excel_module').getExcel();
            var data_ = this.public_data(modul, index);
            data[type] = data_;
            if (!content) {
                //ls20171026优化。如果电子表格没有加载成功。则页面获取原始数据。
                var oriData = $.trim(modul.find('.testexceldata').val());
                if (oriData) {
                    data_.content = eval('(' + oriData + ')');
                } else {
                    data_.content = '';
                }
                return;
            }
            data_.content = content;
        },

        //上传文件数据
        upload_file_data: function (modul, type, nosubmit, index) {
            this.upload_file(modul, 'upload_file_data', nosubmit, index);
        },

        //上传图片数据
        upload_img_data: function (modul, type, nosubmit, index) {
            this.upload_file(modul, 'upload_img_data', nosubmit, index);
        },

        //微信上传数据
        wechat_pic_data: function (modul, type, nosubmit, index) {
            this.upload_file(modul, 'wechat_pic_data', nosubmit, index);
        },

        //公共 提取上传数据
        upload_file: function (modul, type, nosubmit, index) {
            data[type] = data[type] ? data[type] : [];
            var file_info = [];
            modul.find('.file_up_box').each(function () {
                var obj = $(this);
                var pushObj = {
                    'dep_path': obj.find('[name="dep_path"]').val(),
                    'save_name': obj.find('[name="save_name"]').val(),
                    'file_name': obj.find('[name="file_name"]').val()
                };
                if ('wechat_pic_data' == type) {
                    var remark = $.trim(obj.find('.img_remark textarea').val());
                    pushObj.remark = remark;
                }
                file_info.push(pushObj);
            });
            var data_ = this.public_data(modul, index);
            data_.file_info = file_info;
            data[type].push(data_);
        },

        //参考文献 数据
        reference_data: function (modul, type, nosubmit, index) {
            data[type] = data[type] ? data[type] : [];
            var content = [];
            modul.find('.referenceData').each(function () {
                var obj = $(this);
                var formdata = $.formSerializeFn(obj);
                formdata.type = 1;
                formdata.status = 1;
                content.push(formdata);
            });

            var data_ = this.public_data(modul, index);
            data_.content = content;
            data[type].push(data_);
        },

        //仪器耗材
        biology_data: function (modul, type, nosubmit, index) {
            data[type] = data[type] ? data[type] : [];
            var content = [];
            modul.find('.biologyData').each(function () {
                var obj = $(this);
                var formdata = $.formSerializeFn(obj);
                formdata.type = 1;
                formdata.status = 1;
                content.push(formdata);
            });
            var data_ = this.public_data(modul, index);
            data_.content = content;
            data[type].push(data_);
        },

        // 自定义表格
        custom_table_data: function (modul, type, nosubmit, index) {
            data.custom_table_data = data.custom_table_data ? data.custom_table_data : [];
            var that = this;
            modul.find('.customTableData').each(function () {
                var table = $(this).handsontable('getInstance');
                var collection = table.getPlugin('mergeCells').mergedCellsCollection;
                var settings = Object.assign(Object.getPrototypeOf(table.getSettings()));
                for (var set in settings) {
                    if (!settings.set) {
                        delete set;
                    }
                }
                table.getCellsMeta().forEach(function (c) {
                    for (var property in c) {
                        if ($.isFunction(property)) {
                            delete property;
                        }
                    }
                    delete c.instance;
                    settings.cell.push(save_exp.deepClone(c)); // modified by hkk 2019/11/27 用另一种深拷贝);
                });
                settings.rowHeights = [];
                settings.colWidths = [];
                var countRows = table.countRows();
                var countCols = table.countCols();
                for (var i = 0; i < countRows; i++) {
                    settings.rowHeights.push(table.getRowHeight(i));
                }
                for (var j = 0; j < countCols; j++) {
                    settings.colWidths.push(table.getColWidth(j));
                }
                settings.mergedCells = collection.mergedCells;
                delete settings['data'];
                settings['contextMenu'] = true;
                var data_ = that.public_data(modul, index);
                data_.data = JSON.stringify(table.getData());
                data_.settings = JSON.stringify(settings);
                data[type].push(data_);
            });
        },

        //自定义模块。还需要处理。 ls 20161125
        define_data: function (modul, type, nosubmit, index) {
            data[type] = data[type] ? data[type] : [];
            var field_value = [];
            var field_key = {};
            var instrument_ref = [];
            field_key['upload_file'] = mainLang['file'];
            modul.find('.defineData').each(function () {
                var save_name = $(this).find('input[name=\'save_name\']').val();
                var dep_path = $(this).find('input[name=\'dep_path\']').val();
                if ($(this).find('input[name=\'file_name\']').length > 0) {
                    var file_name = $(this).find('input[name=\'file_name\']').val();
                }

                if ($(this).find('input[name=\'real_name\']').length > 0) {
                    var file_name = $(this).find('input[name=\'real_name\']').val();
                }

                var obj = $(this);
                var inputs = $(this).find('input.form-control'); // 去掉readonly条件(bug#9426)
                var formdata = {};
                var refItems = {};

                //是否表头
                var isTh = !!($(this).find('th').length > 0);
                var val;
                var key;
                var i = 1; //字段过滤　表头为空的会被过滤。
                inputs.each(function (index, el) {

                    if($(this).hasClass('detail_upload_file')){ // add by hkk 2019/11/27 需要排除上传文件列框里的多个input 下面已经重写到upload_file_data属性里了
                        return
                    }
                    if($(this).hasClass('wms_product_id')){ // add by hkk 2019/11/27 需要排除上传文件列框里的多个input 下面已经重写到upload_file_data属性里了
                        return
                    }
                    if($(this).hasClass('wms_batch_id')){ // add by hkk 2019/11/27 需要排除上传文件列框里的多个input 下面已经重写到upload_file_data属性里了
                        return
                    }
                    if($(this).hasClass('wms_sync_status')){ // jiangdm 2022/3/25
                        return
                    }
                    if($(this).hasClass('wms_inventory_id')){
                        return
                    }
                    var box;
                    if (isTh) {
                        box = $(this).closest('th');
                    } else {
                        box = $(this).closest('td');
                    }
                    if (box.find('input').length > 1) {
                        return;
                    }
                    val = $(this).val().trim();

					// 保存仪器数据相关信息 jiangdm 2022/11/4
                    if ($(this).hasClass('instrument-data-input')) {
                        refItems[i] = {
                            batch_number: $(this).data('batch_number'),
                            data_id: $(this).data('data_id'),
                            instrument_type: $(this).data('instrument_type'),
                            modified: $(this).hasClass('modified') ? 1 : 0,
                        };
                    }

                    //表头
                    if (isTh) {
                        key = 'field' + i;
                        field_key[key] = val;
                        i++;
                        //内容值
                    } else {
                        //添加数据
                        key = 'data' + i;
                        formdata[key] = val;
                        i++;
                    }
                });

                // formdata['upload_file_data'] = {
                //     'save_name': save_name,
                //     'file_name': file_name,
                //     'dep_path': dep_path
                // };

                // modified by hkk 2019/10/4/14   新增多文件上传
                var filesArray = [];
                $(this).find('.single_detail_file').each(function(){
                    var allFileData = $.formSerializeFn($(this));
                    var fileData = {
                        dep_path: allFileData.dep_path ? allFileData.dep_path : '',
                        save_name: allFileData.save_name ? allFileData.save_name : '',
                        real_name: allFileData.file_name ? allFileData.file_name : ''
                    };
                    filesArray.push(fileData)
                });
                formdata['upload_file_data'] = filesArray;
                formdata['wms_product_id'] = $(this).find('.wms_product_id').val();
                formdata['wms_batch_id'] = $(this).find('.wms_batch_id').val();
                formdata['wms_sync_status'] = $(this).find('.wms_sync_status').val();
                formdata['wms_inventory_id'] = $(this).find('.wms_inventory_id').val();
                formdata['instrument_id'] = $(this).find('.beginOrEndOperateRecordByExp').attr('data-id'); // add by hkk 2021/4/30 添加仪器的id
                formdata['instrument_running_id'] =  $(this).find('.beginOrEndOperateRecordByExp').attr('data-running-id') ? $(this).find('.beginOrEndOperateRecordByExp').attr('data-running-id') : ''; // add by hkk 2022/6/17 添加仪器运行记录id
                formdata['instrument_data_info'] = JSON.stringify(refItems);
                if (!isTh) {
                    field_value.push(formdata);
                }
            });

            var data_ = this.public_data(modul, index);
            data_.field_value = field_value;
            data_.field_key = field_key;
            data[type].push(data_);
            return data_;
        },

        //提取公共数据 class名  描述 标题
        public_data: function (modul, index) {
            var data = {};
            //data.class = modul.attr('class');
            //data.describe_text = modul.find('.describe_text').val();
            data.name = modul.find('.title_input').val();
            data.component_id = modul.find('.modul_part_id').val() ? modul.find('.modul_part_id').val().split('_')[1] : '';
            data.real_id = modul.find('.modul_part_id').attr('data-id') ? modul.find('.modul_part_id').attr('data-id') : '';
            if (index || index == 0) {
                data.class = parseInt(index || '0') + 1;
            }
            return data;
        },

        // 提取TLC模块数据
        tlc_data: function (modul, type, nosubmit, index) {
            data['tlc'] = this.public_data(modul, index);
            data['tlc'].indraw_data = '';
            data['tlc'].png_data = '';
            data['tlc'].table_data = [];

            // indraw部分
           // var tlcIndraw = document.getElementById('indraw-tlc') && document.getElementById('indraw-tlc').contentWindow.indraw;
            var tlcIndraw = $('.exp_conetnt.active #indraw-tlc')[0] && $('.exp_conetnt.active #indraw-tlc')[0].contentWindow.indraw;

            if (tlcIndraw) {
                data['tlc'].indraw_data = tlcIndraw.getMultiMol() ? tlcIndraw.getMultiMol() : '';
                data['tlc'].png_data = tlcIndraw.getCanvasImage(true) ? tlcIndraw.getCanvasImage(true) : '';
            } else {
                // 如果indraw没有加载成功，那么不从编辑器中拿数据，而是直接拿之前的数据。
                data['tlc'].indraw_data = $('.exp_conetnt.active .indraw_tlc_data').val() || '';
                data['tlc'].png_data = '';
            }
            data['tlc'].height = modul.find('#indraw-tlc').height();

            // 表格部分
            modul.find('.tlcData').each(function () {
                var obj = $(this);
                var formdata = $.formSerializeFn(obj);
                data['tlc'].table_data.push(formdata);
            });
        },

        // 获取自定义项的json数据 add by hkk 2019/10/16
        define_item_data:function () {

            var define_data = [];
            $('.exp_conetnt.active .exp_info .added_define_item_part').each(function () {
                var item = $(this);
                var data = {
                    title: item.find('input').attr('data-title'),
                    value: item.find('input').val(),
                    dict_id: item.find('input').attr('data-id') ? item.find('input').attr('data-id') : '',
                    is_require: item.find('input').attr('data-is_require') ? item.find('input').attr('data-is_require') : 0,
                    is_struct: item.find('input').attr('data-is_struct') ? item.find('input').attr('data-is_struct') : 0,
                };
                define_data.push(data)
            })

            return JSON.stringify(define_data);

        }

    };

    var save_exp = {
        save: function (settings) {
            var retries = 0;
            setTimeout(function timer() {
                if (saveLocked) {
                    setTimeout(timer, 500);
                } else {
                    if(!settings || settings.type != 'module') {
                        save_exp.lockSave();
                    }
                    try {
                        save_exp._save(settings);
                    } catch (e) {
                        uploadExceptionLog(e);
                        /*if (++retries < 10) {
                            setTimeout(timer, 500);
                        }*/
                    } finally {
                        save_exp.unlockSave();
                    }
                }
            }, 0);
        },

        offlineNotice: null,

        beforeUnloadHandler: function (e) {
            e.preventDefault();
            e.returnValue = '内容未保存，是否关闭';
            return '内容未保存，是否关闭';
        },

        //add by wh 2023/8/28 封装一下index.js里的方法，利于对window.onbeforeunload进行操作
        handleBeforeUnload: function(event) {
            event = event || window.event;
            // 刷新缓存标签
            var eln_tabs = {
                html_data: $('.exp_title').prop("outerHTML"),
                user_id: USERID,
                lang: lang,
            }
            localStorage.setItem("eln_tabs", JSON.stringify(eln_tabs));

            // 实验发生变动
            if (save_exp.isEditableExperiment() && save_exp.isModified()) {
                event.preventDefault();
                event.returnValue = '';

                save_exp.saveExperiment({autoSave: true});
            }

            // 释放所有实验锁
            require(['tab'], function (tab) {
                var expIds = [];
                $(".exp_title .tag[data-func='getExpContent']").each(function () {
                    var expId = JSON.parse($(this).data('funcparams'));
                    expIds.push(expId);
                });
                if (expIds.length > 0) {
                    tab.unlockExp(expIds);
                }
            })
        },

        _save: function (settings) {
            var settings = settings || {};
            var type = settings.type;
            var nosubmit = settings.nosubmit;
            var success = settings.success;
            var dom = settings.dom;
            var silent = settings.silent;
            var keepModal = settings.autoSave || settings.keepModal;
            var error = settings.error;
            var autoSave = settings.autoSave;
            var setCollaboaration =  settings.setCollaboration; // add by hkk 2022/3/23 intable设置合著完成标记

            setData();

            function setData () {
                var url;
                if (type != 'exp' && ($('.layout_right').attr('data-type') == 'module' || type == 'module')) { //模板
                    data = {};
                    if (dom && dom.data('type') == 'savemodule') { //保存模板
                        url = ELN_URL + '?r=template/add-temp';
                    } else { //保存为模板
                        //检测标签
                        if (!checkTopTab()) {
                            return;
                        }
                        url = ELN_URL + '?r=experiment/save-as-temp';
                    }
                    data.id = $('.exp_conetnt.active #exp_id').val();
                    data.insertData = {
                        'name': $('.exp_conetnt.active .exp_info [name="title"]').val(),
                        'tfrom': $('.exp_conetnt.active #mudole_come_from').val(),
                        'type': 1,
                        'descript': $('.exp_conetnt.active .save_modal_describe').val() || '',
                        'status': 1,
                        'define_item':getData.define_item_data(), // add by hkk 2019/10/16
                    };

                    var group_ids = '';
                    if ($('.save_name_modal_body .data-box-ineln input[name=group_ids]:visible').length > 0) {
                        group_ids = $('.save_name_modal_body .data-box-ineln input[name=group_ids]:visible').attr('idbox') || '';
                    } else {
                        group_ids = $('.exp_conetnt.active .exp_info .data-box-ineln input[name=group_ids]:visible').attr('idbox') || '';
                    }

                    $('.save_name_modal').modal('show');

                    if (dom.attr('data-id') && dom.attr('data-id') != 0) {
                        url = ELN_URL + '?r=template/update-temp';
                        data.temp_id = dom.attr('data-id');
                        group_ids = $('.exp_conetnt.active .exp_info [name="group_ids"]').attr('idbox')
                        if (autoSave) {
                            data.auto_save = 1;
                        }
                    }

                    if (!silent && $.trim(group_ids) == '') {
                        $.showAlert(mainLang('please select groups'));
                        return;
                    } else {
                        data.insertData.group_ids = group_ids.split(',');
                    }

                    save_exp.add_temp_submit(data, url, dom, silent, success, autoSave);
                    return;
                } else { //实验
                    // 下面这段好像没有用，先注释掉 by zhu huajun at 2019-02-18
                    /*var emptyReferenceName = false;
                    $('.referenceData.data_part input[type="text"]').each(function (index, dom) {
                        var tr = $(dom).closest('referenceData');
                        if ($(this).val() == '' && $('[name="name"]', tr).val() == '') {
                            $('[name="name"]', tr).focus();
                            emptyReferenceName = true;
                            $.showAlert(mainLang('ref_name_empty'));
                        }
                    });
                    if (emptyReferenceName) {
                        return;
                    }*/
                    var data = save_exp.getExperimentData(dom);
                    var tempPreviousData = save_exp.deepClone(data);
                    if (autoSave)
                        data.base_data.auto_save = 1;

                    if (!nosubmit) {
                        function sendAjax (body, firstCall) {
                            var pako = require('pako1/pako'); // 引入加密的库

                            if (firstCall) {
                                if (body.module_data && body.module_data.length) {
                                    // 对InTable模块的内容进行加密压缩
                                    for (let i = 0; i < body.module_data.length; i++) {
                                        var mod = body.module_data[i];
                                        if (mod.info.component_id == 19) {
                                            mod.data.data = btoa(pako.gzip(mod.data.data, {to: 'string', level: 3}));
                                        }
                                    }
                                }
                            }

                            var name = data.base_data.exp_page || '';
                            var bodyStr = JSON.stringify(body);
                            var binaryString = pako.gzip(bodyStr, {to: 'string', level: 3}); // 加密
                            //var unzipResult = pako.inflate(binaryString);  //解压
                            binaryString = btoa(binaryString);// base64加密

                            $.ajaxFn({
                                url: ELN_URL + '?r=experiment/save-experiment',
                                data: {
                                    tempData: binaryString
                                },
                                noLoad: silent,
                                noTipError: true,
                                errFn: function () {
                                    error && error();
                                    if (!save_exp.offlineNotice && save_exp.isModified()) {
                                        save_exp.offlineNotice = Toastify({
                                            text:  mainLang('offline_notice'),
                                            duration: -1,
                                            escapeMarkup: false,
                                            gravity: 'bottom',
                                            position: 'left',
                                        }).showToast();
                                    }
                                    window.addEventListener('beforeunload', save_exp.beforeUnloadHandler);
                                },
                                success: function (res) {
                                    // 实验自动保存成功显示提示，有些情况下客户数据丢失，可能是因为没有触发自动保存，或者自动保存的请求被拦截
                                    // 可临时放开这个成功提示，如果在编辑实验过程中没有看到提示，可联系开发排查
                                    /*if (autoSave && res.status == 1) {
                                        Toastify({
                                            text: "实验自动保存成功",
                                            duration: 1000,
                                            close: true,
                                            gravity: 'bottom',
                                            position: 'left',
                                        }).showToast();
                                    }*/

                                    if (save_exp.offlineNotice) {
                                        save_exp.offlineNotice.hideToast();
                                        save_exp.offlineNotice = null;
                                        Toastify({
                                            text: "网络已连接",
                                            duration: 6000,
                                            close: true,
                                            gravity: 'bottom',
                                            position: 'left',
                                        }).showToast();
                                        window.removeEventListener('beforeunload', save_exp.beforeUnloadHandler);
                                    }
                                    var data = res.data;
                                    var expId = data ? data.exp_id : '';

                                    if (res.status != 1) {
                                        // 如果是在弹窗中点击的保存
                                        if (body.from_dialog) {
                                            $.popContent(res.info, mainLang('tip_'), null, null, false);
                                            return;
                                        }

                                        // 弹窗，用于输入密码和原因
                                        if (data && data.pop_dialog && !body.from_dialog) {
                                            $.showContent('warning', mainLang('save'), data.dialog_html, function () {
                                                body.password = $('.save-dialog .password_input').val().trim();
                                                if (body.password === '') {
                                                    $.showAlert(mainLang('pls_input_password'));
                                                    return false;
                                                }
                                                body.password = $.passwordEncipher(body.password);

                                                var $reasonDom = $('.save-dialog .reason-textarea');
                                                if (!$reasonDom.is(':visible')) {
                                                    $reasonDom = $('.save-dialog [name=submit_reason]');
                                                }
                                                body.reason = $reasonDom.val().trim();
                                                if (body.reason === '') {
                                                    $.showAlert(mainLang('pls_input_reason'));
                                                    return false;
                                                }

                                                body.from_dialog = true;

                                                sendAjax(body);
                                            });
                                            return;
                                        }

                                        // $.closeModal();

                                        $.popContent(res.info, mainLang('tip_'), null, null, false, null, function () {
                                            if (data && data.action === 'reload') {
                                                require('tab').reloadActiveTag();
                                            }
                                        });

                                        if (data && 1 == data.signed) {
                                            $('.pop_modal .cancel,.pop_modal .close').hide();
                                            //隐藏取消按钮
                                            $.popContent(res.info, mainLang('tip_'), function () {
                                                $.closeModal();
                                                $('.pop_modal .cancel,.pop_modal .close').show();
                                                window.detialExp.getDetial(null, expId || 0, $('.exp_conetnt.active #exp_page').val());
                                            });
                                        }
                                        return;
                                    }

                                    var newExp = false;
                                    if ($('.exp_conetnt.active #exp_id').val() == 0) {
                                        newExp = true;
                                        $('.exp_conetnt.active #exp_id').val(expId);
                                    }

                                    $('.exp_conetnt.active #exp_step').val('1');
                                    if (data && data.isNew == 1) {
                                        expId = 0;
                                        var num = parseInt($('.my_exp_list .count').text());
                                        $('.my_exp_list .count').text(num + 1);
                                        $('.my_exp_list').attr('title', $.trim($('.my_exp_list').text()));
                                    }

                                    // 比较保存成功返回的实验id和当前打开的实验id, 在切换实验的时候两者可能不一致
                                    // if (expId == $('.exp_conetnt.active #exp_id').val()) {
                                    //     //重新赋值ID
                                    //     $('.exp_conetnt.active .modul_part_id').each(function(index) {
                                    //         var relayId = (data && data.relay_ids && data.relay_ids[index]) ? data.relay_ids[index] : 0;
                                    //         $(this).attr('data-id', relayId);
                                    //         if( $(this).parents('.exp_conetnt.active .modul_part').attr('data-type') === 'xsheet_data'){
                                    //             $(this).parents('.exp_conetnt.active .modul_part').find('iframe')[0].contentWindow.xs.relayId = relayId
                                    //         }
                                    //     });
                                    //     // save_exp.init_module_id(data.relay_ids);
                                    //     //重新赋值ID完成
                                    // }

                                    $('.layout_right').attr('data-type', 'save_exp'); // ??

                                    if (!silent) {

                                        if (setCollaboaration) {
                                            $.showAlert(mainLang('save_collaboration_success'));
                                        } else {
                                            $.showAlert(mainLang('save_success'));
                                        }

                                        $('.new_exp').attr({
                                            'data-id': expId,
                                            'id': expId,
                                            'class': 'iblock on my_exp_detial detial'
                                        }).html('<span class="name">' + name + '</span><span class="close"></span>');

                                        //重新取实验
                                        if (newExp || config.newComment) {

                                            require('tab').reloadActiveTag(); // add by hkk 2020/6/22
                                            //window.detialExp.getDetial(null, expId || 0, $('.exp_conetnt.active #exp_page').val());

                                            config.newComment = false;
                                        }
                                    } else {
                                        $('.new_exp').attr({
                                            'data-id': expId,
                                            'id': expId,
                                            'class': 'iblock my_exp_detial detial'
                                        }).html('<span class="name">' + name + '</span><span class="close"></span>');
                                    }

                                    $('.exp_conetnt.active #exp_version').val(data.exp_version);

                                    // 很多操作的弹窗会触发保存，保存成功之后才触发弹窗上的下一步操作。
                                    // 这个操作可能成功也可以失败,由该弹窗的逻辑来控制。
                                    // 所以传入一个参数keepModal。
                                    if (!keepModal) {
                                        $.closeModal();
                                    }

                                    //previousData = JSON.parse(JSON.stringify(body)); // delete by hkk
                                    previousData = tempPreviousData; // modified by hkk 2019/11/27 用另一种深拷贝

                                    success && $.isFunction(success) ? success() : '';
                                }
                            }, null, true, autoSave);
                        }
                        //如果为空则弹出提示
                        var exp_step = $('.exp_conetnt.active #exp_step').val();
                        if (exp_step == '2' || exp_step == '5') {
                            data.update_comment = window.update_comment;
                        }
                        // changed at 24/05/07 这里必须isModified在前，不然手动保存不会触发函数末尾的痕迹保存
                        if (save_exp.isModified(data) || !autoSave) {
                            console.log('save_exp',data);
                            sendAjax(data, true);
                        } else {
                            success && success();
                        }
                    }
                }

                if (nosubmit) {
                    success(data);
                }
            }
        },

        getExperimentData: function (dom) {
            var data = {};

            data.base_data = {
                'experiment_id': dom ? dom.attr('data-id') || '0' : $('.exp_conetnt.active #exp_id').val(),
                'book_id': $('.exp_conetnt.active #book_id').val(),
                'exp_page': $('.exp_conetnt.active #exp_page').val(),
                'template_id': $('.exp_conetnt.active #template_id').val(),
               // 'keywords': HtmlEncode($('.exp_conetnt.active .exp_info [name="keywords"]').val()),
               // 'title':  HtmlEncode($('.exp_conetnt.active .exp_info [name="title"]').val()),
 				'keywords': $('.exp_conetnt.active .exp_info [name="keywords"]').val(),
                'title': $('.exp_conetnt.active .exp_info [name="title"]').val(),
                'project_id': $('.exp_conetnt.active .exp_info [name="project_id"]').val() || null,
                'project_progress': $('.exp_conetnt.active .exp_info [name="project_progress"]').val() || null,
                'task_id': $('.exp_conetnt.active .exp_info [name="task_id"]').val() || null,
                'company_id': window.COMPANY_ID,
                'weather_json': JSON.stringify({ // modified by hkk 2019/3/27 新增保存天气温度湿度
                    'create_weather': $(".exp_conetnt.active #new-input-weather").val(),
                    'create_temperature': $(".exp_conetnt.active #new-input-temperature").val(),
                    'create_humidity': $(".exp_conetnt.active #new-input-humidity").val(),
                }),
                'define_item':getData.define_item_data(),
                'last_tab_open_time': $('.exp_conetnt.active .last_tab_open_time').val(),
            };

            data.module_data = getAllModuleData();

            // FIXME: InDraw数据可能出现前后空白，当前先清除，具体原因待分析。
            for (var i = 0; i < data.module_data.length; ++i) {
                var indrawData = data.module_data[i]['data'] &&
                        data.module_data[i]['data']['chem_data'] &&
                        data.module_data[i]['data']['chem_data']['indraw_data'];

                if (indrawData) {
                    data.module_data[i]['data']['chem_data']['indraw_data'] = indrawData.trim();
                }
            }

            return data;
        },

        /**
         *
         * @param data
         * @param type: 'exp' || 'template'
         * @returns {boolean}
         */
        isModified: function (data, type) {
            type = type || 'exp';
            if (!data && type === 'exp') {

                console.log('init opening exp...')

                data = save_exp.getExperimentData();
            }
            var currentData = save_exp.deepClone(data);

            // 没有previousData，认为是进入ELN后初次打开实验或模板，此时不触发自动保存，相关bug: #7505 & #7644
            if (!previousData || !previousData.base_data) {
                previousData = currentData;
                return false;
            }

            // previousData和currentData不匹配，认为是切换标签页，此时不触发自动保存
            var previousType = previousData.base_data.experiment_id > 0 ? 'exp' : 'template';
            if (type !== previousType) {
                previousData = currentData;
                return false;
            }
            if (type === 'exp' && previousData.base_data.experiment_id !== currentData.base_data.experiment_id) {
                previousData = currentData;
                return false;
            }
            if (type === 'template' && previousData.base_data.template_id !== currentData.base_data.template_id) {
                previousData = currentData;
                return false;
            }

            // 数据比较时忽略auto_save标记
            if (currentData.base_data.auto_save) {
                previousData.base_data.auto_save = currentData.base_data.auto_save;
            } else {
                delete previousData.base_data.auto_save;
            }

            for (var i = 0; i < currentData.module_data.length; ++i) {
                // InDraw数据中包含时间戳，在做数据比较前需要先予以替换
                var indrawData = currentData.module_data[i]['data'] &&
                    currentData.module_data[i]['data']['chem_data'] &&
                    currentData.module_data[i]['data']['chem_data']['indraw_data'];

                var previousIndrawData = previousData &&
                    previousData.module_data[i] &&
                    previousData.module_data[i]['data'] &&
                    previousData.module_data[i]['data']['chem_data'] &&
                    previousData.module_data[i]['data']['chem_data']['indraw_data'];

                if (indrawData && previousIndrawData) {
                    var match = indrawData.match(/  InDraw (\d{10})2D /);
                    if (match) {
                        previousData.module_data[i]['data']['chem_data']['indraw_data'] = previousIndrawData.replace(/  InDraw \d{10}2D /, '  InDraw ' + match[1] + '2D ');
                    }
                }

                // 处理 xsheet data，数据比对只记录内容变化
                var InTableData = currentData.module_data[i]['info']
                    && currentData.module_data[i]['info']['component_id']
                    && currentData.module_data[i]['info']['component_id'] === 19
                    && currentData.module_data[i]['data']['data'];
                if (InTableData) {
                    var newData = JSON.parse(InTableData);

                    if(!Array.isArray(newData)){
                        // newData.data = newData.data.map((item)=>{
                        //     return {
                        //         name: item.name,
                        //         data: item.data,
                        //         celldata: item.celldata,
                        //     }
                        // });
                        // currentData.module_data[i]['data']['data'] = JSON.stringify(newData);
                        if(Array.isArray(currentData.module_data[i]['data']['trace']) && currentData.module_data[i]['data']['trace'].length == 0){
                            delete currentData.module_data[i]['data']['trace'];
                        }
                        // 取消痕迹对比对的影响
                    }else{
                        var newData1 = [];
                        for (var j = 0; j < newData.length; j++) {
                            newData1.push(// add by hkk 2022/3/23 合著参与自动保存
                                {
                                    name: newData[j].name,
                                    data: {rows: newData[j].data.rows, collaborations: newData[j].data.collaborations}
                                }
                            )
                        }
                        currentData.module_data[i]['data']['data'] = JSON.stringify(newData1);
                    }
                }

                var previousInTableData = previousData && previousData.module_data[i]
                    && previousData.module_data[i]['info']
                    && previousData.module_data[i]['info']['component_id']
                    && previousData.module_data[i]['info']['component_id'] === 19
                    && previousData.module_data[i]['data']['data'];
                if (previousInTableData) {
                    var newData2 = JSON.parse(previousInTableData);
                    if(!Array.isArray(newData2)){
                        // newData2.data = newData2.data.map((item)=>{
                        //     return {
                        //         name: item.name,
                        //         data: item.data,
                        //         celldata: item.celldata,
                        //     }
                        // });
                        // previousData.module_data[i]['data']['data'] = JSON.stringify(newData2);
                        delete previousData.module_data[i]['data']['trace'];
                        // 取消痕迹对比对的影响
                    }else{
                        var newData3 = [];
                        for (var k = 0; k < newData2.length; k++) {
                            newData3.push(
                                {
                                    name: newData2[k].name,
                                    data: {rows: newData2[k].data.rows, collaborations: newData2[k].data.collaborations}
                                }
                            )
                        }
                        previousData.module_data[i]['data']['data'] = JSON.stringify(newData3);
                    }
                }

            }
            if (JSON.stringify(previousData) == JSON.stringify(currentData)) {
                previousData = currentData;
                return false;
            } else {
                // TODO: 生产环境可以关闭
                if (type === 'exp') {
                    save_exp.saveExperimentTraceDetail(previousData, currentData, currentData.base_data.experiment_id); // add by hkk 2019/11/25 记录痕迹详情
                }
                return true;
            }
        },

        // 判断当前激活的tab页是否为模板
        isTemplateTab: function() {
            return $('.exp_title .tag.on').attr('data-func') === 'getExpTempContent';
        },

        isEditableExperiment: function () {
            var tab = document.querySelector('.exp_title .tag.on');
            var button = document.querySelector('.exp_conetnt.active .tool_data_box .tool_btn[data-type="save"]');
            return tab && button && $('.exp_conetnt.active #exp_edit').val() == 1; // add by hkk 2020/6/17 加唯一标记
        },

        isEditableTemplate: function () {
            var tab = document.querySelector('.exp_title .tag.on');
            var saveBtn = document.querySelector('.exp_conetnt.active .tool_data_box .tool_btn[data-type="savemodule"]'); // add by hkk 2020/6/17 加唯一标记
            return tab && saveBtn;
        },

        saveExperiment: function (settings) {
            settings = settings || {};
            if (!settings.type) {
                settings.type = save_exp.isTemplateTab() ? 'module' : 'exp';
            }
            if (typeof settings.silent === 'undefined') {
                settings.silent = true;
            }
            if ((settings.type == 'exp' && save_exp.isEditableExperiment()) || (settings.type == 'module' && save_exp.isEditableTemplate())) {
                save_exp.save(settings);
            } else {
                settings.success && settings.success();
            }
        },

        refreshAutoSaveCache: function () {
            var data = save_exp.getExperimentData();
            //previousData = JSON.parse(JSON.stringify(data)); // modified by hkk
            previousData = save_exp.deepClone(data); // add by hkk 2019/11/27 用另一种深拷贝，否则丢失一些值为undefined对象

        },

        lockSave: function () {
            saveLocked = true;
        },

        unlockSave: function () {
            saveLocked = false;
        },

        startAutoSaveTimer: function () {
            setTimeout(function timer() {
                // 加上loading_box判断是为了防止实验在刷新过程中进行保存实验, 否则会出现保存失败的提示(bug#34224)
                if (!saveLocked && !$('.modal-dialog').is(':visible') && !$('.loading_box').is(':visible')) {
                    try {
                        save_exp.saveExperiment({
                            type: $('.exp_title .tag.on').attr('data-func') === 'getExpTempContent' ? 'module' : 'exp',  // add by hkk 2020/6/23
                            dom: $('.exp_title .tag.on').attr('data-func') === 'getExpTempContent' ? $('.exp_conetnt.active .tool_data_box .tool_btn[data-type="savemodule"]') : '',
                            autoSave: true
                        });
                    } catch (e) {
                        uploadExceptionLog(e); // 上传异常信息
                    } finally {
                        setTimeout(timer, autoSaveInterval);
                    }
                } else {
                    setTimeout(timer, autoSaveInterval);
                }
            }, autoSaveInterval);

         /*   setTimeout(function timer2() {
                console.log('模板保存定时器')
                if (!saveLocked && !$('.modal-dialog').is(':visible')) {
                    try {
                        save_exp.saveExperiment({
                            autoSave: true,
                            type: 'module',
                            dom: $('.exp_conetnt.active .tool_data_box .tool_btn[data-type="savemodule"]'),
                            success: function () {
                                setTimeout(timer2, autoSaveInterval);
                            },
                            error: function () {
                                setTimeout(timer2, autoSaveInterval);
                            }
                        });
                    } catch (e) {
                        setTimeout(timer2, autoSaveInterval);
                    }
                } else {
                    setTimeout(timer2, autoSaveInterval);
                }
            }, autoSaveInterval);*/

          /*  // 方法模板的自动保存
            setTimeout(function timer3() {
                var $saveBtn = $('[data-type="savesonmodule"]');
                if ($saveBtn.length && !$('.modal-dialog').is(':visible')) {
                    try {
                        require(['tool'], function (expTool) {
                            expTool.savesonmodule($saveBtn, 1);
                            setTimeout(timer3, autoSaveInterval);
                        });
                    } catch (e) {
                        setTimeout(timer3, autoSaveInterval);
                    }
                } else {
                    setTimeout(timer3, autoSaveInterval);
                }
            }, autoSaveInterval);*/
        },

        //init modul id
        init_module_id: function (data) {
            if (data.data[0].data.chem_data.base_data.chem_relay.relay_id != undefined) {
                $('input[value=module_1]').attr('data-id', data.data[0].data.chem_data.base_data.chem_relay.relay_id);
            }
            //模块2
            if (data.data[0].data.operation_data.length > 0) {
                $('input[value=module_2]').each(function (i, el) {
                    $(el).attr('data-id', data.data[0].data.operation_data[i].id);
                });
            }
            //模块3
            if (data.data[0].data.discuss_data.length > 0) {
                $('input[value=module_3]').each(function (i, el) {
                    $(el).attr('data-id', data.data[0].data.discuss_data[i].id);
                });
            }
            //模块4
            if (data.data[0].data.diy_data.length > 0) {
                $('input[value=module_4]').each(function (i, el) {
                    $(el).attr('data-id', data.data[0].data.diy_data[i].id);
                });
            }
            //模块5
            if (data.data[0].data.excel_data.new_excel_id > 0 && data.data[0].data.excel_data.new_excel_id != 'undefined') {
                $('input[value=module_5]').attr('data-id', data.data[0].data.excel_data.new_excel_id);
            }
            //模块6
            if (data.data[0].data.comment_data.length > 0) {
                $('input[value=module_6]').each(function (i, el) {
                    $(el).attr('data-id', data.data[0].data.comment_data[i].id);
                });
            }
            //模块7
            if (data.data[0].data.uploadfile_data.relay_info.length > 0) {
                $('input[value=module_7]').each(function (i, el) {
                    $(el).attr('data-id', data.data[0].data.uploadfile_data.relay_info[i].id);
                });
            }
            //模块8
            if (data.data[0].data.uploadimg_data.relay_info.length > 0) {
                $('input[value=module_8]').each(function (i, el) {
                    $(el).attr('data-id', data.data[0].data.uploadimg_data.relay_info[i].id);
                });
            }
            //模块9
            if (data.data[0].data.reference_data != '') {
                $('input[value=module_9]').attr('data-id', data.data[0].data.reference_data.relay_info.id);
            }
            //模块10
            if (data.data[0].data.biology_data != '') {
                $('input[value=module_10]').attr('data-id', data.data[0].data.biology_data.relay_info.id);
            }
            //模块11
            if (data.data[0].data.lite_data.length > 0) {
                $('input[value=module_11]').each(function (i, el) {
                    $(el).attr('data-id', data.data[0].data.lite_data[i].id);
                });
            }
            //模块12
            if (data.data[0].data.abs_data.length > 0) {
                $('input[value=module_12]').each(function (i, el) {
                    $(el).attr('data-id', data.data[0].data.abs_data[i].id);
                });
            }
            //模块13
            if (data.data[0].data.define_data != '') {
                $('input[value=module_13]').each(function (i, el) {
                    $(el).attr('data-id', data.data[0].data.define_data.relay_info[i].id);
                });
            }
            //模块16 TLC
            if (data.data[0].data.tlc_data != '') {
                //$("input[value=module_16]").attr('data-id', data.data[0].data.tlc_data.relay_info.id);
            }
        },

        //获取组件信息 打开模板
        getModuleAssembly: function (id, temp_type) {
            var temp_type = temp_type || '1';
            if (temp_type == 1) {
                var url = '/?r=template/view-temp&id=' + id + '&temp_type=' + temp_type;
                require('get_html').genExpTempPage(id); // add by hkk 2020/6/23 走新标签路径
                return;
            }

            var currentTab = $('.my_exp_detial.module[data-id="' + id + '"]');
            //检测标签
            if (!checkTopTab(currentTab)) {
                return;
            }

            $.ajaxFn({
                type: 'GET',
                url: url,
                success: function (data) {
                    if (data.status == 1) {
                        //子模板
                        if (temp_type == 2) {
                            var html = '<div class="sub_temp_pop">';
                            html += '<div class="clear"><div class="fl">' + mainLang('sub_name') + '：</div><div class="sub_temp_con">' + data.data.name + '</div></div>';
                            html += '<div class="clear"><div class="fl">' + mainLang('sub_desc') + '：</div><div class="sub_temp_con">' + data.data.descript + '</div></div>';
                            html += '<div class="clear"><div class="fl">' + mainLang('sub_content') + '：</div><div class="sub_temp_con">' + data.data.content + '</div></div>';
                            html += '</div>';
                            //require(['exp_data', 'exp_add']); //stechiometry
                            var html = _.template(require('text!popup/sub_temp.html'), {variable: 'data'})({'html': html});
                            $('body').append(html);
                            $('.sub_temp_modal').modal('show');
                            return;
                        }


                        // todo 获取模板走新的路径

                        $('.my_exp_list').removeClass('on');
                        var data = data.data;
                        $('.layout_right_box .exp_conetnt').html(data.expFile);
                        var name = data.moduleName;

                        /*处理标题*/
                        $('.my_exp_list, .my_exp_detial').removeClass('on');
                        var currentTab = $('.my_exp_detial.module[data-id="' + id + '"]');
                        if (currentTab.length > 0) {
                            currentTab.addClass('on');
                        } else {
                            $('.exp_title').append('<a class="iblock on my_exp_detial module" type="module" data-id="' + id + '"><span class="name">' + name + '</span><span class="close"></span></a>');
                        }
                        handleExpTitle();


                        $('.exp_main').show();
                        $('.bg_white').empty();

                        $('.click_module_tool').attr('data-id', id);
                        require(['exp_detial'], function (exp_detial) {
                            exp_detial();
                        });
                        //$(window).scrollTop(0);
                    }
                }
            });
        },

        add_temp_submit: function (data, url, dom, silent,successFunc, autoSave) {
            var data = data;
            data.base_data = {
                'book_id': $('.exp_conetnt.active #book_id').val(),
                'exp_page': $('.exp_conetnt.active #exp_page').val(),
                'template_id': dom.attr('data-id'),
                //'keywords':HtmlEncode($('.exp_conetnt.active .exp_info [name="keywords"]').val()),
                //'title':HtmlEncode($('.exp_conetnt.active .exp_info [name="stitle"]').val()),
                'keywords': $('.exp_conetnt.active .exp_info [name="skeywords"]').val(),
                'title': $('.exp_conetnt.active .exp_info [name="stitle"]').val(),
                'project_id': $('.exp_conetnt.active .exp_info [name="project_id"]').val(),
                'project_progress': $('.exp_conetnt.active .exp_info [name="project_progress"]').val(),
                'weather_json': JSON.stringify({ // modified by hkk 2019/3/27 新增保存天气温度湿度
                    'create_weather':$(".exp_conetnt.active #new-input-weather").val(),
                    'create_temperature':$(".exp_conetnt.active #new-input-temperature").val(),
                    'create_humidity':$(".exp_conetnt.active #new-input-humidity").val(),
                }),
                'define_item':getData.define_item_data(), // add by hkk 2019/10/16
            };

            data.module_data = getAllModuleData();

            var nameObj = $('.save_modal_name:visible');
            var name = nameObj.val().trim();
            var descript = $('.save_modal_describe:visible').val();
            if (name == '') {
                $.showAlert(mainLang('input_module_name'));
                nameObj.focus();
                return;
            }

            data.insertData.name = name;
            data.insertData.descript = descript;
            var name = data.insertData.name;

            // console.time('isModified')
            var isModified = save_exp.isModified(data, 'template');
            // console.timeEnd('isModified')
            if (autoSave && !isModified) {
                return;
            }

            var tempPreviousData = save_exp.deepClone(data);

            const pako = require('pako1/pako');
            if (data.module_data && data.module_data.length) {
                // 对InTable模块的内容进行加密压缩
                // var pako = require('pako1/pako'); // 引入加密的库
                for (let i = 0; i < data.module_data.length; i++) {
                    var mod = data.module_data[i];
                    if (mod.info.component_id == 19) {
                        mod.data.data = btoa(pako.gzip(mod.data.data, {to: 'string', level: 3}));
                    }
                }
            }

            /// bug#1317,将实验保存为模板时,需要将模块数据编码以防止被xss过滤破坏内容
            /// 包括[experiment/save-as-temp,template/update-temp]
            if (!data.encode_type) {
                const moduleDataJSON = JSON.stringify(data);
                const zipStr = pako.gzip(moduleDataJSON, {to: 'string', level: 3});
                const binStr = btoa(zipStr);
                data.encode_type = 'gzip';
                data.module_data = binStr;
            }


            $.ajaxFn({
                url: url,
                data: data,
                noLoad: silent,
                success: function (res) {
                    if (res.status == 1) {
                        if(!silent) {
                            $.showAlert(mainLang('save_success'));
                        }
                        if (url == ELN_URL + '?r=template/add-temp') {
                            $('.new_module').remove();
                            window.templateUpdate=true;
                        }

                        $tempId = res.data.temp_id;
                        if(url != ELN_URL + '?r=template/update-temp') {
                            save_exp.getModuleAssembly($tempId);
                        }
                        if(url === ELN_URL + '?r=template/update-temp' && !autoSave) {
                            $('.exp_title .tag.on').attr('title', name);
                            $('.exp_title .tag.on .name').text(name);
                        }
                        if (!autoSave) {
                            $.closeModal();
                            if (res.data.need_approval) {
                                require(['template_list'], function (temp) {
                                    temp.submitAuditConfirm(res.data.temp_id, true, url === ELN_URL + '?r=template/update-temp');
                                });
                            }
                        }
                        previousData = tempPreviousData;
                    }
                    successFunc && $.isFunction(successFunc) ? successFunc() : '';
                }
            }, null, true, autoSave);
        },

        getModuleData: getModuleData,

        //记录痕迹详情 add by hkk 2019/11/27
        saveExperimentTraceDetail: function (previousData,currentData,exp_id) {
            require(['bin/deep-diff.min'], function () {
                function getNameFromFilesJson(fileString) {
                    var name = "";
                    JSON.parse(fileString).forEach(function (item,index,array) {
                        name  +=  item.real_name + ","
                    });
                    return name
                }

                function getFirstKey(obj) {		//obj为我们的对象
                    for (var key in obj) {
                        return key;
                    }
                }

                function getCoordsFromXY(x, y) {
                    const alphabets = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'];

                    function stringAt(index) {
                        let str = '';
                        let cindex = index;
                        while (cindex >= alphabets.length) {
                            cindex /= alphabets.length;
                            cindex -= 1;
                            str += alphabets[parseInt(cindex, 10) % alphabets.length];
                        }
                        const last = index % alphabets.length;
                        str += alphabets[last];
                        return str;
                    }

                    return `${stringAt(x)}${y + 1}`;
                }

                const expIdNum = Number(exp_id);

                /**
                 * 记录实验操作痕迹的数组
                 * @type {modSaveExpTypes.ExpTraceRecord[]}
                 */
                const expTraceRecordList = [];

                // 0-> 先比较基础信息变化 (项目任务暂时没做)
                var baseDataDiff = DeepDiff.diff(previousData.base_data, currentData.base_data);
                var baseDataTrace = "";
                if(baseDataDiff){
                     // 记录基础模块变化痕迹详情
                    baseDataDiff.forEach(function(item){

                        if (item.path[0] === "title") { // 编辑了标题
                            const operateTrace = jsLang["exp_title_change"] + "(" + item.lhs + " -> " + item.rhs + ");##;";
                            baseDataTrace += operateTrace;
                            expTraceRecordList.push({exp_id: expIdNum, operate_trace: operateTrace});
                        }
                        else if (item.path[0] === "keywords") { // 编辑了关键字
                            const operateTrace = jsLang["exp_keywords_change"] + "(" + item.lhs + " -> " + item.rhs + ");##;";
                            baseDataTrace += operateTrace;
                            expTraceRecordList.push({exp_id: expIdNum, operate_trace: operateTrace});
                        }
                        else if (item.path[0] === "define_item") { // 编辑了自定义项， 自定义项是json字符串，要额外再次比对处理
                            var defineDiff = DeepDiff.diff(JSON.parse(item.lhs), JSON.parse(item.rhs));
                            var traceDefineString = "";
                            defineDiff.forEach(function (defineItem) {

                                if(defineItem.kind ==="E"){ // 编辑自定义项内容，包括标题，内容，下拉词库属性
                                    if(defineItem.path[1]==="title"){
                                        traceDefineString += jsLang["exp_define_item"] + ( 1 + defineItem.path[0] ) + jsLang["title_change"] + "(" + defineItem.lhs + " -> " + defineItem.rhs + ");##;";
                                    }else if(defineItem.path[1]==="value"){
                                        traceDefineString += jsLang["exp_define_item"] + ( 1 + defineItem.path[0] ) + jsLang["value_change"] + "(" + defineItem.lhs + " -> " + defineItem.rhs + ");##;";
                                    }else if(defineItem.path[1]==="dict_id"){
                                        traceDefineString += jsLang["exp_define_item"] + ( 1 + defineItem.path[0] ) + jsLang["drop_down_change"] + ";##;" ; // 不用显示具体改成哪个下拉框
                                    }

                                }else if(defineItem.kind === "A"){ // 新增或删除了某项自定义项
                                    if(defineItem.item.kind === "D"){
                                        traceDefineString += jsLang["delete_exp_define_item"] + ( 1 + defineItem.index ) + ";##;";
                                    }else if(defineItem.item.kind === "N"){
                                        traceDefineString += jsLang["add_exp_define_item"] + ( 1 + defineItem.index ) + "(" + jsLang["trace_title"] + ":" + defineItem.item.rhs.title + ";" + jsLang["trace_value"] + ":"  + defineItem.item.rhs.value + ");##;"; // 不用展示下拉属性
                                    }
                                }

                            });
                            baseDataTrace += traceDefineString;
                            const operateTrace = traceDefineString;
                            expTraceRecordList.push({exp_id: expIdNum, operate_trace: operateTrace});
                        }
                        else if (item.path[0] === "weather_json") { // 编辑了天气栏， 也是json字符串，要额外再次比对处理
                            var weatherDiff = DeepDiff.diff(JSON.parse(item.lhs), JSON.parse(item.rhs));
                            var traceWeatherString = "";
                            weatherDiff.forEach(function (weatherItem) {
                                if (weatherItem.path[0] === "create_weather") {
                                    traceWeatherString += jsLang["exp_weather_change"] + "(" + weatherItem.lhs + " -> " + weatherItem.rhs + ");##;";
                                } else if (weatherItem.path[0] === "create_temperature") {
                                    traceWeatherString += jsLang["exp_temperature_change"] + "(" + weatherItem.lhs + " -> " + weatherItem.rhs + ");##;";
                                } else if (weatherItem.path[0] === "create_humidity") {
                                    traceWeatherString += jsLang["exp_humidity_change"] + "(" + weatherItem.lhs + " -> " + weatherItem.rhs + ");##;";
                                }
                            });
                            baseDataTrace += traceWeatherString;
                            const operateTrace = traceWeatherString;
                            expTraceRecordList.push({exp_id: expIdNum, operate_trace: operateTrace});
                        }

                    });
                }

                // 取模块relay_id一一比较对应模块(所有模块都有info属性和data属性 )
                var previousModuleData = previousData.module_data;
                var currentModuleData = currentData.module_data;
                var previous_relay_ids = [];
                var previous_relay_ids_map = {}; //存储索引
                var current_relay_ids = [];
                var current_relay_ids_map = {}; //存储索引
                previousModuleData.forEach(function(item,index){
                    if (item.info.real_id) {
                        previous_relay_ids.push(item.info.real_id);
                        previous_relay_ids_map[item.info.real_id] = index;
                    }
                });
                currentModuleData.forEach(function(item,index){
                    if(item.info.real_id) {
                        current_relay_ids.push(item.info.real_id)
                        current_relay_ids_map[item.info.real_id] = index;
                    }
                });

                /** @type {modSaveExpTypes.ModuleTraceRecord[]} */
                const modulesTraceRecordList = [];

                // 1->比较各个模块变化
                var both_module_ids = previous_relay_ids.filter(function(v){ return current_relay_ids.indexOf(v) > -1 });//
                var moduleDataTrace = "";
                both_module_ids.forEach(function (item, index, array) {

                    var beforeData = previousModuleData[previous_relay_ids_map[item]]; // 包括data和info信息
                    var afterData = currentModuleData[current_relay_ids_map[item]]; // 包括data和info信息

                    /** @type {number} */
                    const expModuleRelayId = beforeData.info.exp_module_relay_id;

                    var moduleDiff = DeepDiff.diff(beforeData, afterData);

                    if(moduleDiff){

                        moduleDiff.forEach(function(item){

                            if (item.path[0] === "info") { // 编辑了模块基础信息
                                if (item.path[1] === "name") { // 模块名称改变
                                    const operateTrace = jsLang["module_name_change"] + "(" + item.lhs + " -> " + item.rhs + ");##;";
                                    moduleDataTrace += operateTrace;
                                    modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});
                                } else if (item.path[1] === "class") { // 模块顺序改变
                                    const operateTrace = jsLang["module_order_change"] + beforeData.info.name + "(" + item.lhs + " -> " + item.rhs + ");##;";
                                    moduleDataTrace += operateTrace;
                                    modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});
                                } else {
                                    // config配置暂时不记录痕迹，包括表格模块的列宽，隐藏项等配置
                                }
                            }
                            else if(item.path[0] === "data"){ // 编辑了模块具体数据 400

                                var moduleName,positionInfo,newRowValue;
                                switch (afterData.info.component_id) {

                                    case 1: // indraw

                                        moduleName = afterData.info.name + jsLang["module"]; // 模块名称

                                        const InMaterialTrace = require('components/save_exp/in_material_trace')
                                        if (InMaterialTrace.needTrace(item)) {
                                            const moduleDataChangeInfo = InMaterialTrace.diffInMaterialData(item);
                                            console.info('物料表新痕迹变化: ', moduleDataChangeInfo);
                                            /// 只记录可能发生变化的字段
                                            if (null != moduleDataChangeInfo) {
                                                const _materialTraceInfo = `${moduleName} ${moduleDataChangeInfo}`;
                                                moduleDataTrace += _materialTraceInfo;
                                                const operateTrace = _materialTraceInfo;
                                                modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});
                                            }
                                        }

                                        var materialMAP = {
                                            "substrates_data": {
                                                name:"Reactant",
                                                nameMap:{
                                                    substrates_name:"name",
                                                    substrates_batch_num:"Batch No",
                                                    substrates_equivalent:"Eq",
                                                    is_base:"is_base",
                                                    substrates_nb:"N",
                                                    substrates_molweight:"MW",
                                                    substrates_mass:"Mass",
                                                    substrates_density:"D",
                                                    substrates_volume:"V",
                                                    substrates_temperature: "C", // 摩尔浓度C
                                                    substrates_pressure:"Purity", // 纯度
                                                    substrates_comment:"Source", // source 来源
                                                    substrates_cas:"CAS",
                                                    define_value:"define item",
                                                }
                                            },
                                            "catalysts_data": {
                                                name:"Reagent",
                                                nameMap:{
                                                    catalysts_name:"name",
                                                    catalysts_batch_num:"Batch No",
                                                    catalysts_equivalent:"Eq",
                                                    is_base:"is_base",
                                                    catalysts_nb:"N",
                                                    catalysts_molweight:"MW",
                                                    catalysts_mass:"Mass",
                                                    catalysts_density:"D",
                                                    catalysts_volume:"V",
                                                    catalysts_temperature: "C", // 摩尔浓度C
                                                    catalysts_pressure:"Purity", // 纯度
                                                    catalysts_comment:"Source", // source 来源
                                                    catalysts_cas:"CAS",
                                                    define_value:"define item",
                                                }
                                            },
                                            "solvent_data": {
                                                name:"Solvent",
                                                nameMap:{
                                                    solvent_name:"name",
                                                    solvent_batch_num: "Batch No",
                                                    solvent_ratio:"Ratio",
                                                    solvent_volume:"v",
                                                    solvent_mass:"Mass",
                                                    solvent_density:"D",
                                                    solvent_bp:"b.p.",
                                                    solvent_comment:"Source",// source 来源
                                                    solvent_cas:"CAS",
                                                    define_value:"define item",
                                                }
                                            },
                                            "condition_data": {
                                                name:"Condition",
                                                nameMap:{
                                                    condition_name:"name",
                                                    condition_temperature:"Temperature",
                                                    condition_rt:"Reaction Time",
                                                    condition_pressure:"Pressure",
                                                    condition_pg:"Protection Gas",
                                                    condition_heating:"Heating",
                                                    condition_comment:"Comment",
                                                    define_value:"define item",
                                                }

                                            },
                                            "product_data": {
                                                name:"Product",
                                                nameMap:{
                                                    product_name:"name",
                                                    product_batch_num:"Batch No",
                                                    product_nb:"N",
                                                    product_salt:"Salt",
                                                    product_salt_eq:"Salt Eq",
                                                    product_molweight:"MW",
                                                    product_mass:"Actual Mass", // 实际质量
                                                    product_theo:"Theo Mass",   // 理论质量
                                                    product_yield:"Yield",
                                                    product_purity:"Purity", //纯度
                                                    product_sample_id:"Sample ID",
                                                    product_barcode:"Barcode",
                                                    define_value:"define item",
                                                }

                                            },
                                            "details_data": {
                                                name:"Details",
                                                nameMap:{
                                                    name:"name",
                                                    cas:"cas",
                                                    de:"de",
                                                    ee:"ee",
                                                    risk_assessment:"Risk Assessment",
                                                    comment:"Comment",
                                                    define_value:"define item",
                                                    detail_upload_files:"Files",
                                                }
                                            },
                                        };
                                        var mapData;
                                        if(item.kind === "E") {  //编辑单元格内容，包含indraw画布，改单位，增加删除自定义列，

                                            if(item.path[1] === "chem_data"){

                                                var NUnit = {
                                                    1: "μmol",
                                                    2: "mmol",
                                                    3: "mol",
                                                    4: "kmol"
                                                };
                                                var MUnit = {
                                                    1: "mg",
                                                    2: "g",
                                                    3: "kg",
                                                    4: "ton"
                                                };
                                                var VUnit = {
                                                    1: "μL",
                                                    2: "mL",
                                                    3: "L",
                                                    4: "kL"
                                                };
                                                var chemDataUnitMap = {
                                                    "mass_unit": {
                                                        info:jsLang["reactant_mass_unit_change"],
                                                        change:MUnit
                                                    },
                                                    "volume_unit": {
                                                        info:jsLang["reactant_volume_unit_change"],
                                                        change:VUnit
                                                    },
                                                    "nb_unit": {
                                                        info:jsLang["reactant_mol_unit_change"],
                                                        change:NUnit
                                                    },
                                                    "solvent_mass_unit": {
                                                        info:jsLang["solvent_mass_unit_change"],
                                                        change:MUnit
                                                    },
                                                    "solvent_volume_unit": {
                                                        info:jsLang["solvent_volume_unit_change"],
                                                        change:VUnit
                                                    },
                                                    "produce_mass_unit": {
                                                        info:jsLang["product_actual_unit_change"],
                                                        change:MUnit
                                                    },
                                                    "produce_theo_unit": {
                                                        info:jsLang["product_theo_unit_change"],
                                                        change:MUnit
                                                    },
                                                    "produce_nb_unit": {
                                                        info:jsLang["product_mol_unit_change"],
                                                        change:NUnit
                                                    },

                                                };

                                                if (item.path[2] === "smiles" /*|| item.path[2] === "indraw_data"|| item.path[2] === "png_data"*/) { // indraw 画布变化，提示即可
                                                    const operateTrace = moduleName + jsLang["indraw_structure_change"] + ";##;";
                                                    moduleDataTrace += operateTrace;
                                                    modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});
                                                } else if (chemDataUnitMap.hasOwnProperty(item.path[2])) { // 单位变化
                                                    const operateTrace = moduleName + chemDataUnitMap[item.path[2]].info + "(" + chemDataUnitMap[item.path[2]]["change"][item.lhs] + " -> " + chemDataUnitMap[item.path[2]]["change"][item.rhs] + ");##;";
                                                    moduleDataTrace += operateTrace;
                                                    modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});
                                                } else if (item.path[2] === "show_details") { // 隐藏显示物料表
                                                    if (item.lhs === 1) {
                                                        const operateTrace = moduleName + jsLang["hide_material_table"] + ";##;";
                                                        moduleDataTrace += operateTrace;
                                                        modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});
                                                    } else {
                                                        const operateTrace = moduleName + jsLang["show_material_table"] + ";##;";
                                                        moduleDataTrace += operateTrace;
                                                        modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});
                                                    }
                                                } else { // 增加自定义列表头，物料表宽度变化,暂时忽略

                                                }
                                            }
                                            else{ // 具体物料表数据编辑变化
                                                mapData = materialMAP[item.path[1]];
                                                if(mapData && mapData.nameMap[item.path[3]]){

                                                    if(item.path[3] === "define_value"){ // 自定义列单独处理

                                                        var defineDiff = DeepDiff.diff(JSON.parse(item.lhs), JSON.parse(item.rhs));
                                                        var traceDefineString = "";

                                                        defineDiff.forEach(function (defineItem) {

                                                            if(defineItem.kind === "E"){ // 编辑自定义列数据

                                                                if(defineItem.path[1] === "name"){   // 改自定义项名称,内容关联的标题改变

                                                                    traceDefineString += jsLang["define_item"] + (defineItem.path[0] -1) + jsLang["item"]  + jsLang["title_change"] +  "("   +  defineItem.lhs + " -> " + defineItem.rhs + ");##;" ; //第几个自定义项的名字更改

                                                                }else if(defineItem.path[1] === "value"){  // 改自定义项值

                                                                    var defineName = JSON.parse(item.rhs)[defineItem.path[0]].name;

                                                                    traceDefineString += defineName +  jsLang["column"] + jsLang["value_change"] + "("   +  defineItem.lhs + " -> " + defineItem.rhs + ");##;" ; // 某某自定义项改变

                                                                }
                                                            }else if(defineItem.kind === "A"){ // 删除或增加自定义列
                                                                if(defineItem.item.kind === "D"){ // 删除
                                                                    traceDefineString += jsLang["delete_define_item"] + defineItem.item.lhs.name + ";##;" ; //删除自定义项
                                                                }
                                                                else if(defineItem.item.kind === "N"){ // 新增
                                                                    traceDefineString += jsLang["add_define_item"] + "(" + jsLang["trace_title"] + ":" + defineItem.item.rhs.name + "," + jsLang["trace_value"] + ":"+ defineItem.item.rhs.value + ");##;" ; //删除自定义项
                                                                }
                                                            }
                                                        });
                                                        positionInfo =  " " + mapData.name  + ": " + " " + (item.path[2] + 1) + " "  + jsLang["row"] + ";##;"; // 单元格位置信息
                                                        const operateTrace = moduleName + positionInfo + traceDefineString;
                                                        moduleDataTrace += operateTrace;
                                                        modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});
                                                    }
                                                    else{
                                                        positionInfo =  " " + mapData.name  + ": "+ " " + (item.path[2] + 1) + " " + jsLang["row"]  + " " + mapData.nameMap[item.path[3]] + " "  +  jsLang["column"] ; // 单元格位置信息
                                                        if(item.path[3] === "detail_upload_files"){ // 单独处理详情里的文件上传
                                                            const operateTrace = moduleName + positionInfo + "("   +   getNameFromFilesJson(item.lhs) + " -> " +  getNameFromFilesJson(item.rhs) + ");##;"; // ;##; 为末尾分隔符，用于换行分割
                                                            moduleDataTrace += operateTrace;
                                                            modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});
                                                        }else{
                                                            const operateTrace = moduleName + positionInfo + "("   +  item.lhs + " -> " + item.rhs + ");##;";
                                                            moduleDataTrace += operateTrace;
                                                            modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                        else if(item.kind === "A"){ // 新增或删除某行内容

                                            mapData = materialMAP[item.path[1]];
                                            if(mapData){

                                                if(item.item.kind === "D"){ // 删除
                                                    const operateTrace = moduleName + mapData.name + jsLang["delete_row"] +  " "  + item.index + ";##;"; //indraw模块reactant删除行 4；
                                                    moduleDataTrace += operateTrace;
                                                    modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});
                                                }
                                                else if(item.item.kind === "N"){ // 新增

                                                    var nameMap = mapData.nameMap;
                                                    newRowValue = "";
                                                    Object.keys(item.item.rhs).forEach(function (key) {
                                                        if(nameMap.hasOwnProperty(key) && item.item.rhs[key]  && key !== "define_value"  && key !== "detail_upload_files"){ // 文件和自定义列暂不处理，新增行同时传文件不大可能
                                                            newRowValue +=  nameMap[key] + ":"  + item.item.rhs[key] + "," ;
                                                        }

                                                    });
                                                    const operateTrace = moduleName + mapData.name  + jsLang["add_row"] +  " "  + item.index  + "("   + newRowValue +");##;" ; // indraw模块reactant添加行
                                                    moduleDataTrace += operateTrace;
                                                    modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});
                                                }
                                            }

                                        }
                                        break;
                                    case 9: // 参考文献
                                    case 10: //材料与仪器

                                        var columnNameMap;
                                        if(afterData.info.component_id === 9){
                                            columnNameMap = {
                                                "name": jsLang["ref_name"], //参考文献期刊名称
                                                "year": jsLang["ref_year"], // 年份
                                                "volume": jsLang["ref_volume"], // 卷
                                                "chapter": jsLang["ref_chapter"], //期
                                                "page": jsLang["ref_page"], //起止页码
                                                "supply": jsLang["ref_link"], //链接网址
                                                "upload_file": jsLang["ref_upload_file"], //上传文献
                                            };
                                        }
                                        else if(afterData.info.component_id === 10){
                                            columnNameMap = {
                                                "name": jsLang["name"],   //材料仪器名称
                                                "batch_num": jsLang["batch_num"],
                                                "product_model": jsLang["specification"],
                                                "number": jsLang["model"],
                                                "remark": jsLang["trace_remark"],
                                                "supply": jsLang["supply"],
                                                "upload_file": jsLang["upload_file"],
                                            };
                                        }

                                        if(item.kind === "E"){  //编辑单元格内容  上传文件会新增 3个kind N变化 忽略掉

                                            moduleName = afterData.info.name + jsLang["module_data_change"];
                                            positionInfo = " " + columnNameMap[item.path[3]] + " "  +  jsLang["column"] + " " + (item.path[2] + 1) + " "  + jsLang["row"] ; // 单元格位置信息

                                            if(item.path[3] === "upload_file"){ // 上传文件列要显示文件名
                                                const operateTrace = moduleName + positionInfo +"("   +   getNameFromFilesJson(item.lhs) + " -> " + getNameFromFilesJson(item.rhs) + ");##;";
                                                moduleDataTrace += operateTrace;
                                                modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});
                                            }else if(columnNameMap.hasOwnProperty(item.path[3])){ // 其他列改变
                                                const operateTrace = moduleName + positionInfo + "("   +  item.lhs + " -> " + item.rhs + ");##;";
                                                moduleDataTrace += operateTrace;
                                                modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});
                                            }else{

                                            }

                                        }
                                        else if(item.kind === "A"){ // 新增或删除行 ， kind=N 是有些上传文件导致生成新属性，忽略掉

                                            if(item.item.kind === "D"){ // 删除行
                                                const operateTrace = afterData.info.name + jsLang["module"] + jsLang["delete_row"] + " " + ( item.index + 1)  + ";##;";
                                                moduleDataTrace += operateTrace;
                                                modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});
                                            }
                                            else if(item.item.kind === "N"){ // 新增行

                                                // 若新增行同时填了值，显示新增的值
                                                newRowValue = "";
                                                Object.keys(columnNameMap).forEach(function (nameItem) {
                                                    if(nameItem === "upload_file" && item.item.rhs.upload_file !=="[]"){ // 文件单独处理
                                                        newRowValue +=  columnNameMap["upload_file"] + ":"  + item.item.rhs["upload_file"] + "," ;
                                                    }else if(nameItem !== "upload_file" && item.item.rhs[nameItem]){
                                                        newRowValue +=  columnNameMap[nameItem] + ":"  + item.item.rhs[nameItem] + "," ;
                                                    }
                                                });
                                                const operateTrace = afterData.info.name + jsLang["module"] + jsLang["add_row"] + " " + (item.index + 1) + "(" + newRowValue + ");##;";
                                                moduleDataTrace += operateTrace;
                                                modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});
                                            }
                                        }
                                        break;
                                    case 13: //自定义表格

                                        moduleName = afterData.info.name + jsLang["module"];

                                        if(item.kind === "E"){  //编辑单元格内容 改变文件内容列是A类

                                            if (item.path[1] === "field_key") { // 自定义标题改变
                                                positionInfo = jsLang["title_row_change"] + item.path[2].replace(/[^\d]/g, '') + " " + jsLang["column"]; // 单元格位置信息
                                                const operateTrace = moduleName + positionInfo + "(" + item.lhs + " -> " + item.rhs + ");##;";
                                                moduleDataTrace += operateTrace;
                                                modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});
                                            } else if (item.path[1] === "field_value" && item.path[3] !== 'instrument_id' && item.path[3] !== 'instrument_running_id') { // 自定义内容改变  2021/6/8 去掉插入仪器的隐藏id痕迹
                                                if (item.path[3] !== "wms_batch_id" && item.path[3] !== "wms_product_id" && item.path[3] !== "wms_sync_status" && item.path[3] !== "wms_inventory_id") {
                                                    positionInfo = jsLang["data_row_change"] + " " + item.path[3].replace(/[^\d]/g, '') + " " + jsLang["column"] + " " + (item.path[2] + 1) + " " + jsLang["row"]; // 单元格位置信息
                                                    const operateTrace = moduleName + positionInfo + "(" + item.lhs + " -> " + item.rhs + ");##;";
                                                    moduleDataTrace += operateTrace;
                                                    modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});
                                                }
                                            }

                                        }
                                        else if(item.kind === "A"){ // 新增或删除行 文件框新增或删除文件


                                            if(!item.path[2]){ // path不存在第三个参数说明是新增删除行

                                                if(item.item.kind === "D"){ // 删除行
                                                    const operateTrace = moduleName + jsLang["delete_row"] + " " + ( item.index + 1)  + ";##;";
                                                    moduleDataTrace += operateTrace;
                                                    modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});
                                                }
                                                else if(item.item.kind === "N"){ // 新增行
                                                    // 若新增行同时填了值，显示新增的非空值
                                                    newRowValue = "";
                                                    Object.keys(item.item.rhs).forEach(function (key) { // key
                                                        if (item.item.rhs[key] && key !== "upload_file_data" && key !== "wms_batch_id" && key !== "wms_product_id" && key !== "instrument_id" && key !== "instrument_running_id" && key !== "wms_sync_status" && key !== 'instrument_data_info' && key !== 'wms_inventory_id') { // 文件暂不处理，新增行同时传文件不大可能
                                                            newRowValue += key.replace('data', jsLang['field']) + ":" + item.item.rhs[key] + ";";
                                                        }
                                                    });
                                                    const operateTrace = moduleName + jsLang["add_row"] + " " + (item.index + 1) + "(" + newRowValue + ");##;";
                                                    moduleDataTrace += operateTrace;
                                                    modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});
                                                }
                                            }else if(item.path[3] && item.path[3] ==="upload_file_data"){ // 新增或删除文件框里文件

                                                if(item.item.kind === "D"){ // 删除文件
                                                    positionInfo = jsLang["file_row_change"] + " " + (item.path[2] + 1) + " "  + jsLang["row"] ; // 单元格位置信息
                                                    var deleteFileInfo = jsLang["delete_file"] + "" + (item.index + 1) + "" + "("  + item.item.lhs.real_name + ");##;"; // 删除了文件5(name)
                                                    const operateTrace = moduleName +  positionInfo  +   deleteFileInfo;
                                                    moduleDataTrace += operateTrace;
                                                    modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});

                                                }else if(item.item.kind === "N"){ //新增文件

                                                    positionInfo = jsLang["file_row_change"] + " " + (item.path[2] + 1) + " "  + jsLang["row"] ; // 单元格位置信息
                                                    var addFileInfo = jsLang["add_new_file"] + "" + (item.index + 1) + "" + "("  + item.item.rhs.real_name + ");##;";// 新增了文件5(name)
                                                    const operateTrace = moduleName +  positionInfo  +  addFileInfo;
                                                    moduleDataTrace += operateTrace;
                                                    modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});
                                                }

                                            }

                                        }
                                        else if(item.kind === "N"){ // 新增列 每次加一个单元格

                                            if(item.path[1] ==="field_key"){ // 添加列标题
                                                const operateTrace = moduleName +  jsLang["add_define_column"] + "(" + item.rhs + ");##;";// 添加了自定义列（name）
                                                moduleDataTrace += operateTrace;
                                                modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});
                                            }else if(item.path[1] ==="field_value"){ // 添加列内容
                                                positionInfo = " " + item.path[3].replace(/[^\d]/g, '') + " "  + jsLang["column"] + " "+ (item.path[2] + 1) + " " + jsLang["row"] ; // 单元格位置信息
                                                const operateTrace = moduleName + jsLang["add_define_column"] + positionInfo +  "(" + item.rhs + ");##;" ;
                                                moduleDataTrace += operateTrace;
                                                modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});
                                            }

                                        }
                                        else if(item.kind === "D"){ // 删除列 每次删一个单元格

                                            if(item.path[1] ==="field_key"){ // 删除列标题
                                                const operateTrace = moduleName +  jsLang["delete_define_column"] + "(" + item.lhs + ");##;";// 添加了自定义列（name）
                                                moduleDataTrace += operateTrace;
                                                modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});
                                            }else if(item.path[1] ==="field_value"){ // 删除列内容
                                                positionInfo = " " + item.path[3].replace(/[^\d]/g, '') + " "  + jsLang["column"] + " "+ (item.path[2] + 1) + " " + jsLang["row"]; // 单元格位置信息
                                                const operateTrace = moduleName + jsLang["delete_define_column"] + positionInfo +  "(" + item.lhs + ");##;";
                                                moduleDataTrace += operateTrace;
                                                modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});
                                            }
                                        }
                                        break;
                                    case 16: // TLC
                                        moduleName = afterData.info.name + jsLang["module"];

                                        var tlcNameMap = {
                                            "no": jsLang["trace_no"],   //编号
                                            "eluent": jsLang["trace_eluent"], // 展开剂
                                            "ratio": jsLang["ratio"],
                                            "cdr": jsLang["cdr"], //显色剂
                                            "remark": jsLang["trace_remark"],
                                            "rf1": "Rf1",
                                            "rf2": "Rf2",
                                            "rf3": "Rf3",
                                            "rf4": "Rf4",
                                            "rf5": "Rf5",
                                        };
                                        if(item.kind === "E"){  //编辑单元格内容  上传文件会新增 3个kind N变化 忽略掉

                                            if (item.path[2] === "png_data") {
                                                const operateTrace = moduleName + jsLang["tlc_canvas_change"] + ";##;"; //画布变化
                                                moduleDataTrace += operateTrace;
                                                modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});
                                            } else if (item.path[2] === "table_data") {
                                                positionInfo = " " + tlcNameMap[item.path[4]] + " " + jsLang["column"] + " " + (item.path[3] + 1) + " " + jsLang["row"]; // 单元格位置信息
                                                const operateTrace = moduleName + positionInfo + "(" + item.lhs + " -> " + item.rhs + ");##;";
                                                moduleDataTrace += operateTrace;
                                                modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});
                                            }

                                        }
                                        else if(item.kind === "A"){ // 新增或删除行 ， kind=N 是有些上传文件导致生成新属性，忽略掉

                                            if(item.item.kind === "D"){ // 删除行
                                                const operateTrace = moduleName + jsLang["delete_row"] + " " + ( item.index + 1)  + ";##;";
                                                moduleDataTrace += operateTrace;
                                                modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});
                                            }
                                            else if(item.item.kind === "N"){ // 新增行

                                                // 若新增行同时填了值，显示新增的值
                                                newRowValue = "";
                                                Object.keys(tlcNameMap).forEach(function (nameItem) {

                                                    if(item.item.rhs[nameItem]){
                                                        newRowValue +=  tlcNameMap[nameItem] + ":"  + item.item.rhs[nameItem] + "," ;
                                                    }
                                                });
                                                const operateTrace = moduleName + jsLang["add_row"] + " " + (item.index + 1) + "(" + newRowValue + ");##;";
                                                moduleDataTrace += operateTrace;
                                                modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});
                                            }
                                        }




                                        break;
                                    case 7: // 上传文件
                                    case 8: // 上传图片
                                        moduleName = afterData.info.name + jsLang["module"];
                                        if(item.kind === "A"){  //只显示增加或删除文件
                                            if(item.item.kind === "D"){
                                                const operateTrace = moduleName + jsLang["delete_file"] + " " + ( item.index + 1)  +"(" + item.item.lhs.file_name + ");##;" ; // 文件模块删除文件2 (文件名)
                                                moduleDataTrace += operateTrace;
                                                modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});
                                            }else if(item.item.kind === "N"){
                                                const operateTrace = moduleName + jsLang["add_new_file"] + " " + ( item.index + 1)  +"(" + item.item.rhs.file_name + ");##;" ; // 文件模块删除文件2 (文件名)
                                                moduleDataTrace += operateTrace;
                                                modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});
                                            }
                                        }

                                        break;
                                    case 2: // 实验操作
                                    case 3: // 结果与讨论
                                    case 11: // 文本编辑
                                    case 12: // 摘要
                                        moduleName = afterData.info.name + jsLang["module"]; // 模块名称
                                        var beforeText = $('<div>' + item.lhs + '</div>')[0].innerText;
                                        var afterText = $('<div>' + item.rhs + '</div>')[0].innerText;
                                        var textTrace = '';
                                        //检查保存前有几张图片
                                        var bStr = item.lhs;
                                        var bIndex = bStr.indexOf('<img'); // 字符出现的位置
                                        var bNum = 0; // 这个字符出现的次数
                                        while(bIndex !== -1) {
                                            bNum++; // 每出现一次 次数加一
                                            bIndex = bStr.indexOf('<img',bIndex + 1); // 从字符串出现的位置的下一位置开始继续查找
                                        }
                                        console.log('原有图'+bNum);
                                        //检查保存后有几张图片
                                        var aStr = item.rhs;
                                        var aIndex = aStr.indexOf('<img'); // 字符出现的位置
                                        var aNum = 0; // 这个字符出现的次数
                                        while(aIndex !== -1) {
                                            aNum++; // 每出现一次 次数加一
                                            aIndex = aStr.indexOf('<img',aIndex + 1); // 从字符串出现的位置的下一位置开始继续查找
                                        }
                                        console.log('现有图'+aNum);
                                        if (item.kind === "E") { // 编辑器内容变化,只比较字符串变化
                                            /*var textDiff = require('bin/diff.min').diffChars(beforeText, afterText);
                                            textDiff.forEach(function (part) {
                                                textTrace += part.added ? (jsLang["trace_add"] + '"' + part.value + '";') : (part.removed ? (jsLang["trace_delete"] + '"' + part.value + '";') : '');
                                            });*/

                                            /**
                                             * 上面的diffChars有性能问题，比如当文本编辑器中含有大量内容，然后选中其中一半的内容进行删除操作
                                             * 采用Google的diff_match_patch(https://github.com/google/diff-match-patch)进行替换
                                             */
                                            require('bin/diff_match_patch');
                                            var dmp = new diff_match_patch();
                                            var diff = dmp.diff_main(beforeText, afterText);
                                            dmp.diff_cleanupSemantic(diff);
                                            console.log('diiff'+diff);
                                            if(diff.length>0){
                                                diff.forEach(function (item) {
                                                    //如果是图片变化 则记录新增或删除图片 否则按原来逻辑
                                                    if(aNum<bNum){
                                                        textTrace += jsLang["trace_delete"] +  jsLang["img"]+';'
                                                    }
                                                    if(aNum>bNum){
                                                        textTrace += jsLang["trace_add"] + jsLang["img"]+';'
                                                    }
                                                    if(aNum==bNum){
                                                        if (item[0] === -1) {
                                                            textTrace += jsLang["trace_delete"] + '"' + item[1] + '";'
                                                        } else if (item[0] === 1) {
                                                            textTrace += jsLang["trace_add"] + '"' + item[1] + '";'

                                                        }
                                                    }


                                                });
                                            }
                                            else{
                                                if(aNum<bNum){
                                                    textTrace += jsLang["trace_delete"] +  jsLang["img"]+';'
                                                }
                                                if(aNum>bNum){
                                                    textTrace += jsLang["trace_add"] + jsLang["img"]+';'
                                                }
                                            }

                                            const operateTrace = moduleName + jsLang["value_change"] + textTrace + ";##;";
                                            moduleDataTrace += operateTrace;
                                            modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});
                                        }
                                        break;
                                    case 18: //多功能表格
                                        moduleName = afterData.info.name + jsLang["module"]; // 模块名称

                                        if(item.path[1] ==="data"){ //  settings 忽略
                                            if(item.kind === "E"){ // 编辑器内容变化,只比较字符串变化

                                                var cellDiff = DeepDiff.diff(JSON.parse(item.lhs), JSON.parse(item.rhs));
                                                cellDiff.forEach(function (cellItem) {
                                                    positionInfo = " " + (cellItem.path[0] + 1) + " "  +  jsLang["row"] + " " + ( cellItem.path[1] + 1 ) + " "  + jsLang["column"] ; // 单元格位置信息
                                                    var beforeText = cellItem.lhs ;
                                                    var afterText = cellItem.rhs ;

                                                    if($(beforeText).length > 0){
                                                        beforeText = $(beforeText)[0].innerText;
                                                    }
                                                    if($(afterText).length > 0){
                                                        afterText = $(afterText)[0].innerText;
                                                    }
                                                    const operateTrace = moduleName + positionInfo + "("   +  beforeText + " -> " + afterText + ");##;";
                                                    moduleDataTrace += operateTrace;
                                                    modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});
                                                })
                                            }
                                        }

                                        break;
                                    case 19: // xsheet
                                        // deleted by xyx 2024.4.9 老的intbale的痕迹不用了
                                        // moduleName = afterData.info.name + jsLang["module"]; // 模块名称
                                        // if (item.path[1] === "data") {
                                        //     if (item.rhs === item.lhs) { // add by hkk 2021/7/1
                                        //         break;
                                        //     }
                                        //     var AfterItem = JSON.parse(item.rhs);
                                        //     var BeforeItem = JSON.parse(item.lhs);
                                        //
                                        //     if (BeforeItem.data.length > AfterItem.data.length) { // changed by xyx 2024.4.9 删除sheet单独处理
                                        //
                                        //         try {
                                        //             var afterSheetNames = [];
                                        //             var deleteSheetNames = '';
                                        //             AfterItem.data.forEach(function (item1) {
                                        //                 afterSheetNames.push(item1.name)
                                        //             });
                                        //             BeforeItem.data.forEach(function (item2) {
                                        //                 if (!afterSheetNames.includes(item2.name)) {
                                        //                     deleteSheetNames += (item2.name + ',')
                                        //                 }
                                        //             });
                                        //             deleteSheetNames = deleteSheetNames.slice(0, deleteSheetNames.length - 1);
                                        //             moduleDataTrace += moduleName + jsLang["delete_sheet"] + "(" + deleteSheetNames + ");##;";
                                        //         } catch (e) {
                                        //             uploadExceptionLog(e);
                                        //         }
                                        //
                                        //
                                        //     }
                                        //     else {
                                        //         //added by xieyuxiang 2023.1.4 剔除空内容的单元格 bug28822
                                        //         // deleted by xyx 2024.3.27 新intable上线，老逻辑暂时注释
                                        //         // for (var beforeItemKey in BeforeItem) {
                                        //         //     var rowsData = BeforeItem[beforeItemKey].data.rows;//以行为基准
                                        //         //     for (var rowKey in rowsData) {
                                        //         //         for (var colKey in rowsData[rowKey].cells) {
                                        //         //             if(rowsData[rowKey].cells[colKey].text===''){
                                        //         //                 delete BeforeItem[beforeItemKey].data.rows[rowKey].cells[colKey];
                                        //         //             }
                                        //         //             else {
                                        //         //                 //剔除style
                                        //         //                 if(rowsData[rowKey].cells[colKey].style!==undefined)
                                        //         //                     delete BeforeItem[beforeItemKey].data.rows[rowKey].cells[colKey].style;
                                        //         //             }
                                        //         //         }
                                        //         //     }
                                        //         // }
                                        //         // for (var afterItemKey in AfterItem) {
                                        //         //     var rowsData = AfterItem[afterItemKey].data.rows;//以行为基准
                                        //         //     for (var rowKey in rowsData) {
                                        //         //         for (var colKey in rowsData[rowKey].cells) {
                                        //         //             if(rowsData[rowKey].cells[colKey].text===''){
                                        //         //                 delete AfterItem[afterItemKey].data.rows[rowKey].cells[colKey];
                                        //         //             }
                                        //         //             else {
                                        //         //                 //剔除style
                                        //         //                 if(rowsData[rowKey].cells[colKey].style!==undefined)
                                        //         //                     delete AfterItem[afterItemKey].data.rows[rowKey].cells[colKey].style;
                                        //         //             }
                                        //         //         }
                                        //         //     }
                                        //         // }
                                        //         var sheetDiff = DeepDiff.diff(BeforeItem, AfterItem);
                                        //         for (var i = 0; i < sheetDiff.length; i++) {
                                        //
                                        //             try {
                                        //                 if (i > 5200) { // 只记录5200 条 超过数据库记录太大不好展示
                                        //                     break
                                        //                 }
                                        //
                                        //                 var cellItem = sheetDiff[i];
                                        //                 // 样式等改变不记录痕迹 跳过当前循环  // add by hkk 2020/7/14
                                        //                 if ((cellItem.kind === 'E' && cellItem.path[6] === 'style') || // 样式改变
                                        //                     (cellItem.kind === 'N' && cellItem.path[4] === 'height') || // 行高改变
                                        //                     (cellItem.kind === 'E' && cellItem.path[4] === 'scroll') ||
                                        //                     (cellItem.kind === 'E' && cellItem.path[4] === 'scrollPos') ||
                                        //                     (cellItem.kind === 'N' && cellItem.path[6] === 'style') ||  // 样式改变
                                        //                     (cellItem.kind === 'E' && cellItem.path[2] === 'selectorRange') // 选取改变
                                        //                 ) {
                                        //                     continue
                                        //                 }
                                        //
                                        //
                                        //                 if (cellItem.kind === 'A') { // add by hkk 2021/7/6 新增和删除sheet的痕迹
                                        //                     if (cellItem.path && cellItem.path[2] && cellItem.path[2] === 'collaborations') { // add by hkk 2022/3/23
                                        //                         // 暂不记录痕迹，没有获取合著用户名称和对应区域
                                        //                     } else {
                                        //                         if (cellItem.item.kind === "N") {
                                        //                             moduleDataTrace += moduleName + jsLang["add_new_sheet"] + "(" + cellItem.item.rhs.name + ");##;"; //
                                        //                         } else if (cellItem.item.kind === "D") {
                                        //                             moduleDataTrace += moduleName + jsLang["delete_sheet"] + "(" + cellItem.item.lhs.name + ");##;"; //
                                        //                         }
                                        //                     }
                                        //                 } else {
                                        //                     var sheetName = AfterItem[cellItem.path[0]].name;
                                        //                     var rowNumber, colNumber, beforeText, afterText, coords;
                                        //                     if (cellItem.kind === 'N' && cellItem.path[1] === 'data' && cellItem.path[2] === 'rows') { // 单元格新增数据
                                        //                         rowNumber = cellItem.path[3];
                                        //                         beforeText = '';
                                        //                         if (cellItem.path[4] && cellItem.path[4] === 'cells' && cellItem.path[5]) { // 新增在已有数据的行上
                                        //                             colNumber = cellItem.path[5];
                                        //                             afterText = cellItem.rhs.text || cellItem.rhs;
                                        //                             if (typeof afterText === 'object') { // add by hkk 2020/7/14
                                        //                                 continue
                                        //                             }
                                        //                         } else {
                                        //                             try {
                                        //                                 colNumber = getFirstKey(cellItem.rhs.cells);
                                        //                                 if (colNumber === undefined) { // add by hkk 2021/7/1
                                        //                                     continue
                                        //                                 }
                                        //                                 afterText = cellItem.rhs.cells[colNumber].text;
                                        //                                 if (afterText === undefined) { // add by hkk 2020/7/14
                                        //                                     continue
                                        //                                 }
                                        //                             } catch (e) {
                                        //                                 uploadExceptionLog(e);
                                        //                             }
                                        //                         }
                                        //                         coords = getCoordsFromXY(parseInt(colNumber), parseInt(rowNumber));
                                        //                         positionInfo = " " + sheetName + " " + coords; //位置信息
                                        //                         moduleDataTrace += moduleName + positionInfo + "(" + beforeText + " -> " + afterText + ");##;";
                                        //                     } else if (cellItem.kind === 'E' && cellItem.path[6] === 'text') {// 单元格改变数据
                                        //                         rowNumber = cellItem.path[3];
                                        //                         colNumber = cellItem.path[5];
                                        //                         beforeText = cellItem.lhs;
                                        //                         afterText = cellItem.rhs;
                                        //                         coords = getCoordsFromXY(parseInt(colNumber), parseInt(rowNumber));
                                        //                         positionInfo = " " + sheetName + " " + coords; //位置信息
                                        //                         moduleDataTrace += moduleName + positionInfo + "(" + beforeText + " -> " + afterText + ");##;";
                                        //                     } else if (cellItem.kind === 'D' && cellItem.path[6] === 'text') {// 单元格删除数据
                                        //
                                        //                         rowNumber = cellItem.path[3];
                                        //                         colNumber = cellItem.path[5];
                                        //                         beforeText = cellItem.lhs;
                                        //                         afterText = '';
                                        //
                                        //                         coords = getCoordsFromXY(parseInt(colNumber), parseInt(rowNumber));
                                        //
                                        //                         positionInfo = " " + sheetName + " " + coords; //位置信息
                                        //                         moduleDataTrace += moduleName + positionInfo + "(" + beforeText + " -> " + afterText + ");##;";
                                        //                     } else if (cellItem.kind === 'E' && cellItem.path[1] === 'name') { // // add by hkk 2021/7/6 sheet名称修改
                                        //
                                        //                         moduleDataTrace += moduleName + jsLang["modify_sheet"] + "(" + cellItem.lhs + " -> " + cellItem.rhs + ");##;"; //
                                        //
                                        //                     }
                                        //                 }
                                        //
                                        //
                                        //             } catch (e) {
                                        //                 uploadExceptionLog(e);
                                        //             }
                                        //         }
                                        //     }
                                        //
                                        //
                                        //
                                        //
                                        // }

                                       break;
                                    default:
                                        break;

                                }
                            }

                        })
                    }
                });

                // 2->删除的的模块痕迹
                var delete_module_ids =  previous_relay_ids.filter(function(v){ return current_relay_ids.indexOf(v) === -1 });
                var deleteModuleTrace = "";
                delete_module_ids.forEach(function (item) {
                    const beforeData = previousModuleData[previous_relay_ids_map[item]];
                    const expModuleRelayId = beforeData.info.exp_module_relay_id;

                    var delete_module_name = previousModuleData[previous_relay_ids_map[item]].info.name + jsLang["module"]; // 模块名称
                    const operateTrace = jsLang["delete_module"]  + "("   +  delete_module_name + ");##;";  //IDNRAW模块reactant删除行4；
                    deleteModuleTrace += operateTrace;
                    modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});
                });

                // 3->新增的模块痕迹
                var addModuleTrace = "";

                // 4->新intable的变化
                if (currentData.module_data) {
                    currentData.module_data.forEach(function (item) {
                        const expModuleRelayId = item?.info?.exp_module_relay_id;


                        const moduleName = item.info.name + jsLang["module"] + ': '; // 模块名称
                        if (item.info['component_id'] == 19) {
                            if (Array.isArray(item.data.trace)) {
                                item.data.trace.forEach(function (trace) {
                                    const operateTrace = (moduleName + trace + ';##;');
                                    moduleDataTrace += operateTrace;
                                    modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});
                                })
                            }
                        }
                    })
                }

                // 6秒内总痕迹 基础信息痕迹，模块变化痕迹，模块新增痕迹，模块删除痕迹,每条痕迹可用";##;"分割，非空保存到数据库
                var sumTrace = baseDataTrace + moduleDataTrace + deleteModuleTrace + addModuleTrace;
                if (sumTrace && sumTrace !== '') {
                    $.ajaxFn({
                        url: ELN_URL + '?r=history/save-experiment-trace-detail',
                        data: {
                            exp_id: exp_id,
                            traceDetail: sumTrace,
                            exps_trace_record_list: expTraceRecordList,
                            modules_trace_record_list: modulesTraceRecordList,
                        },
                        noLoad: true,
                        success: function (res) {
                            if (res.status == 1) {
                            }
                        }
                    }, null, true, true);
                }
            });
        },

        // 合著模块，记录痕迹详情 // add by hkk 2021/3/1
        saveCoAuthorTraceDetail: function (previousData,currentData,exp_id,create_time) {

            require(['bin/deep-diff.min'], function () {

                function getNameFromFilesJson(fileString) {
                    var name = "";
                    JSON.parse(fileString).forEach(function (item,index,array) {
                        name  +=  item.real_name + ","
                    });
                    return name
                }

                function getFirstKey(obj) {		//obj为我们的对象
                    for (var key in obj) {
                        return key;
                    }
                }

                function getCoordsFromXY(x, y) {
                    const alphabets = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'];

                    function stringAt(index) {
                        let str = '';
                        let cindex = index;
                        while (cindex >= alphabets.length) {
                            cindex /= alphabets.length;
                            cindex -= 1;
                            str += alphabets[parseInt(cindex, 10) % alphabets.length];
                        }
                        const last = index % alphabets.length;
                        str += alphabets[last];
                        return str;
                    }

                    return `${stringAt(x)}${y + 1}`;
                }

                // 取模块relay_id一一比较对应模块(所有模块都有info属性和data属性 )
                var previousModuleData = [previousData];
                var currentModuleData = [currentData];
                var previous_relay_ids = [];
                var previous_relay_ids_map = {}; //存储索引
                var current_relay_ids = [];
                var current_relay_ids_map = {}; //存储索引
                previousModuleData.forEach(function(item,index){
                    if (item && item.info && item.info.real_id) {
                        previous_relay_ids.push(item.info.real_id);
                        previous_relay_ids_map[item.info.real_id] = index;
                    }
                });
                currentModuleData.forEach(function(item,index){
                    if(item.info.real_id) {
                        current_relay_ids.push(item.info.real_id);
                        current_relay_ids_map[item.info.real_id] = index;
                    }
                });

                /** @type {modSaveExpTypes.ModuleTraceRecord[]} */
                const modulesTraceRecordList = [];

                // 1->比较各个模块变化
                var both_module_ids = previous_relay_ids.filter(function(v){ return current_relay_ids.indexOf(v) > -1 });//
                var moduleDataTrace = "";
                both_module_ids.forEach(function (item, index, array) {

                    var beforeData = previousModuleData[previous_relay_ids_map[item]]; // 包括data和info信息
                    var afterData = currentModuleData[current_relay_ids_map[item]]; // 包括data和info信息

                    /** @type {number} */
                    const expModuleRelayId = beforeData.info.exp_module_relay_id;

                    var moduleDiff = DeepDiff.diff(beforeData, afterData);

                    if(moduleDiff){
                        moduleDiff.forEach(function(item){

                            if (item.path[0] === "info") { // 编辑了模块基础信息

                                if(item.path[1] === "name"){ // 模块名称改变
                                    const operateTrace = jsLang["module_name_change"] + "(" + item.lhs + " -> " + item.rhs + ");##;";
                                    moduleDataTrace += operateTrace;
                                    modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});
                                }else if(item.path[1] === "class") { // 模块顺序改变
                                    const operateTrace = jsLang["module_order_change"] + beforeData.info.name +  "(" + item.lhs + " -> " + item.rhs + ");##;";
                                    moduleDataTrace += operateTrace;
                                    modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});
                                }else{
                                    // config配置暂时不记录痕迹，包括表格模块的列宽，隐藏项等配置
                                }

                            }
                            else if(item.path[0] === "data"){ // 编辑了模块具体数据 400

                                var moduleName,positionInfo,newRowValue;
                                switch (afterData.info.component_id) {

                                    case 1: // indraw

                                        moduleName = afterData.info.name + jsLang["module"]; // 模块名称

                                        const InMaterialTrace = require('components/save_exp/in_material_trace');
                                        if (InMaterialTrace.needTrace(item)) {
                                            const moduleDataChangeInfo = InMaterialTrace.diffInMaterialData(item);
                                            console.info('物料表新痕迹变化: ', moduleDataChangeInfo);
                                            /// 只记录可能发生变化的字段
                                            if (null != moduleDataChangeInfo) {
                                                const _materialTraceInfo = `${moduleName} ${moduleDataChangeInfo}`;
                                                moduleDataTrace += _materialTraceInfo;
                                                const operateTrace = _materialTraceInfo;
                                                modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});
                                            }
                                        }

                                        var materialMAP = {
                                            "substrates_data": {
                                                name:"Reactant",
                                                nameMap:{
                                                    substrates_name:"name",
                                                    substrates_batch_num:"Batch No",
                                                    substrates_equivalent:"Eq",
                                                    is_base:"is_base",
                                                    substrates_nb:"N",
                                                    substrates_molweight:"MW",
                                                    substrates_mass:"Mass",
                                                    substrates_density:"D",
                                                    substrates_volume:"V",
                                                    substrates_temperature: "C", // 摩尔浓度C
                                                    substrates_pressure:"Purity", // 纯度
                                                    substrates_comment:"Source", // source 来源
                                                    substrates_cas:"CAS",
                                                    define_value:"define item",
                                                }
                                            },
                                            "catalysts_data": {
                                                name:"Reagent",
                                                nameMap:{
                                                    catalysts_name:"name",
                                                    catalysts_batch_num:"Batch No",
                                                    catalysts_equivalent:"Eq",
                                                    is_base:"is_base",
                                                    catalysts_nb:"N",
                                                    catalysts_molweight:"MW",
                                                    catalysts_mass:"Mass",
                                                    catalysts_density:"D",
                                                    catalysts_volume:"V",
                                                    catalysts_temperature: "C", // 摩尔浓度C
                                                    catalysts_pressure:"Purity", // 纯度
                                                    catalysts_comment:"Source", // source 来源
                                                    catalysts_cas:"CAS",
                                                    define_value:"define item",
                                                }
                                            },
                                            "solvent_data": {
                                                name:"Solvent",
                                                nameMap:{
                                                    solvent_name:"name",
                                                    solvent_batch_num: "Batch No",
                                                    solvent_ratio:"Ratio",
                                                    solvent_volume:"v",
                                                    solvent_mass:"Mass",
                                                    solvent_density:"D",
                                                    solvent_bp:"b.p.",
                                                    solvent_comment:"Source",// source 来源
                                                    solvent_cas:"CAS",
                                                    define_value:"define item",
                                                }
                                            },
                                            "condition_data": {
                                                name:"Condition",
                                                nameMap:{
                                                    condition_name:"name",
                                                    condition_temperature:"Temperature",
                                                    condition_rt:"Reaction Time",
                                                    condition_pressure:"Pressure",
                                                    condition_pg:"Protection Gas",
                                                    condition_heating:"Heating",
                                                    condition_comment:"Comment",
                                                    define_value:"define item",
                                                }

                                            },
                                            "product_data": {
                                                name:"Product",
                                                nameMap:{
                                                    product_name:"name",
                                                    product_batch_num:"Batch No",
                                                    product_nb:"N",
                                                    product_salt:"Salt",
                                                    product_salt_eq:"Salt Eq",
                                                    product_molweight:"MW",
                                                    product_mass:"Actual Mass", // 实际质量
                                                    product_theo:"Theo Mass",   // 理论质量
                                                    product_yield:"Yield",
                                                    product_purity:"Purity", //纯度
                                                    product_sample_id:"Sample ID",
                                                    product_barcode:"Barcode",
                                                    define_value:"define item",
                                                }

                                            },
                                            "details_data": {
                                                name:"Details",
                                                nameMap:{
                                                    name:"name",
                                                    cas:"cas",
                                                    de:"de",
                                                    ee:"ee",
                                                    risk_assessment:"Risk Assessment",
                                                    comment:"Comment",
                                                    define_value:"define item",
                                                    detail_upload_files:"Files",
                                                }
                                            },
                                        };
                                        var mapData;
                                        if(item.kind === "E") {  //编辑单元格内容，包含indraw画布，改单位，增加删除自定义列，

                                            if(item.path[1] === "chem_data"){

                                                var NUnit = {
                                                    1: "μmol",
                                                    2: "mmol",
                                                    3: "mol",
                                                    4: "kmol"
                                                };
                                                var MUnit = {
                                                    1: "mg",
                                                    2: "g",
                                                    3: "kg",
                                                    4: "ton"
                                                };
                                                var VUnit = {
                                                    1: "μL",
                                                    2: "mL",
                                                    3: "L",
                                                    4: "kL"
                                                };
                                                var chemDataUnitMap = {
                                                    "mass_unit": {
                                                        info:jsLang["reactant_mass_unit_change"],
                                                        change:MUnit
                                                    },
                                                    "volume_unit": {
                                                        info:jsLang["reactant_volume_unit_change"],
                                                        change:VUnit
                                                    },
                                                    "nb_unit": {
                                                        info:jsLang["reactant_mol_unit_change"],
                                                        change:NUnit
                                                    },
                                                    "solvent_mass_unit": {
                                                        info:jsLang["solvent_mass_unit_change"],
                                                        change:MUnit
                                                    },
                                                    "solvent_volume_unit": {
                                                        info:jsLang["solvent_volume_unit_change"],
                                                        change:VUnit
                                                    },
                                                    "produce_mass_unit": {
                                                        info:jsLang["product_actual_unit_change"],
                                                        change:MUnit
                                                    },
                                                    "produce_theo_unit": {
                                                        info:jsLang["product_theo_unit_change"],
                                                        change:MUnit
                                                    },
                                                    "produce_nb_unit": {
                                                        info:jsLang["product_mol_unit_change"],
                                                        change:NUnit
                                                    },

                                                };

                                                if(item.path[2] === "smiles" /*|| item.path[2] === "indraw_data"|| item.path[2] === "png_data"*/ ){ // indraw 画布变化，提示即可
                                                    const operateTrace = moduleName + jsLang["indraw_structure_change"] + ";##;";
                                                    moduleDataTrace += operateTrace;
                                                    modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});
                                                }else if(chemDataUnitMap.hasOwnProperty(item.path[2])){ // 单位变化
                                                    const operateTrace = moduleName +  chemDataUnitMap[item.path[2]].info + "("   +  chemDataUnitMap[item.path[2]]["change"][item.lhs] + " -> " + chemDataUnitMap[item.path[2]]["change"][item.rhs] + ");##;";
                                                    moduleDataTrace += operateTrace;
                                                    modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});
                                                }else if(item.path[2] === "show_details" ){ // 隐藏显示物料表

                                                    if(item.lhs===1){
                                                        const operateTrace = moduleName +  jsLang["hide_material_table"] + ";##;";
                                                        moduleDataTrace += operateTrace;
                                                        modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});
                                                    }else{
                                                        const operateTrace = moduleName +  jsLang["show_material_table"] + ";##;";
                                                        moduleDataTrace += operateTrace;
                                                        modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});
                                                    }
                                                }else{ // 增加自定义列表头，物料表宽度变化,暂时忽略

                                                }
                                            }
                                            else{ // 具体物料表数据编辑变化
                                                mapData = materialMAP[item.path[1]];
                                                if(mapData && mapData.nameMap[item.path[3]]){

                                                    if(item.path[3] === "define_value"){ // 自定义列单独处理

                                                        var defineDiff = DeepDiff.diff(JSON.parse(item.lhs), JSON.parse(item.rhs));
                                                        var traceDefineString = "";

                                                        defineDiff.forEach(function (defineItem) {

                                                            if(defineItem.kind === "E"){ // 编辑自定义列数据

                                                                if(defineItem.path[1] === "name"){   // 改自定义项名称,内容关联的标题改变

                                                                    traceDefineString += jsLang["define_item"] + (defineItem.path[0] -1) + jsLang["item"]  + jsLang["title_change"] +  "("   +  defineItem.lhs + " -> " + defineItem.rhs + ");##;" ; //第几个自定义项的名字更改

                                                                }else if(defineItem.path[1] === "value"){  // 改自定义项值

                                                                    var defineName = JSON.parse(item.rhs)[defineItem.path[0]].name;

                                                                    traceDefineString += defineName +  jsLang["column"] + jsLang["value_change"] + "("   +  defineItem.lhs + " -> " + defineItem.rhs + ");##;" ; // 某某自定义项改变

                                                                }
                                                            }else if(defineItem.kind === "A"){ // 删除或增加自定义列
                                                                if(defineItem.item.kind === "D"){ // 删除
                                                                    traceDefineString += jsLang["delete_define_item"] + defineItem.item.lhs.name + ";##;" ; //删除自定义项
                                                                }
                                                                else if(defineItem.item.kind === "N"){ // 新增
                                                                    traceDefineString += jsLang["add_define_item"] + "(" + jsLang["trace_title"] + ":" + defineItem.item.rhs.name + "," + jsLang["trace_value"] + ":"+ defineItem.item.rhs.value + ");##;" ; //删除自定义项
                                                                }
                                                            }
                                                        });
                                                        positionInfo =  " " + mapData.name  + ": " + " " + (item.path[2] + 1) + " "  + jsLang["row"] + ";##;"; // 单元格位置信息
                                                        const operateTrace = moduleName + positionInfo + traceDefineString ;
                                                        moduleDataTrace += operateTrace;
                                                        modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});
                                                    }
                                                    else{
                                                        positionInfo =  " " + mapData.name  + ": "+ " " + (item.path[2] + 1) + " " + jsLang["row"]  + " " + mapData.nameMap[item.path[3]] + " "  +  jsLang["column"] ; // 单元格位置信息
                                                        if(item.path[3] === "detail_upload_files"){ // 单独处理详情里的文件上传
                                                            const operateTrace = moduleName + positionInfo + "("   +   getNameFromFilesJson(item.lhs) + " -> " +  getNameFromFilesJson(item.rhs) + ");##;"; // ;##; 为末尾分隔符，用于换行分割
                                                            moduleDataTrace += operateTrace;
                                                            modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});
                                                        }else{
                                                            const operateTrace = moduleName + positionInfo + "("   +  item.lhs + " -> " + item.rhs + ");##;";
                                                            moduleDataTrace += operateTrace;
                                                            modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                        else if(item.kind === "A"){ // 新增或删除某行内容

                                            mapData = materialMAP[item.path[1]];
                                            if(mapData){

                                                if(item.item.kind === "D"){ // 删除
                                                    const operateTrace = moduleName + mapData.name + jsLang["delete_row"] +  " "  + item.index + ";##;"; //indraw模块reactant删除行 4；
                                                    moduleDataTrace += operateTrace;
                                                    modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});
                                                }
                                                else if(item.item.kind === "N"){ // 新增

                                                    var nameMap = mapData.nameMap;
                                                    newRowValue = "";
                                                    Object.keys(item.item.rhs).forEach(function (key) {
                                                        if(nameMap.hasOwnProperty(key) && item.item.rhs[key]  && key !== "define_value"  && key !== "detail_upload_files"){ // 文件和自定义列暂不处理，新增行同时传文件不大可能
                                                            newRowValue +=  nameMap[key] + ":"  + item.item.rhs[key] + "," ;
                                                        }

                                                    });
                                                    const operateTrace = moduleName + mapData.name  + jsLang["add_row"] +  " "  + item.index  + "("   + newRowValue +");##;" ; // indraw模块reactant添加行
                                                    moduleDataTrace += operateTrace;
                                                    modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});

                                                }
                                            }

                                        }
                                        break;
                                    case 9: // 参考文献
                                    case 10: //材料与仪器

                                        var columnNameMap;
                                        if(afterData.info.component_id === 9){
                                            columnNameMap = {
                                                "name": jsLang["ref_name"], //参考文献期刊名称
                                                "year": jsLang["ref_year"], // 年份
                                                "volume": jsLang["ref_volume"], // 卷
                                                "chapter": jsLang["ref_chapter"], //期
                                                "page": jsLang["ref_page"], //起止页码
                                                "supply": jsLang["ref_link"], //链接网址
                                                "upload_file": jsLang["ref_upload_file"], //上传文献
                                            };
                                        }
                                        else if(afterData.info.component_id === 10){
                                            columnNameMap = {
                                                "name": jsLang["name"],   //材料仪器名称
                                                "batch_num": jsLang["batch_num"],
                                                "product_model": jsLang["specification"],
                                                "number": jsLang["model"],
                                                "remark": jsLang["trace_remark"],
                                                "supply": jsLang["supply"],
                                                "upload_file": jsLang["upload_file"],
                                            };
                                        }

                                        if(item.kind === "E"){  //编辑单元格内容  上传文件会新增 3个kind N变化 忽略掉

                                            moduleName = afterData.info.name + jsLang["module_data_change"];
                                            positionInfo = " " + columnNameMap[item.path[3]] + " "  +  jsLang["column"] + " " + (item.path[2] + 1) + " "  + jsLang["row"] ; // 单元格位置信息

                                            if(item.path[3] === "upload_file"){ // 上传文件列要显示文件名
                                                const operateTrace = moduleName + positionInfo +"("   +   getNameFromFilesJson(item.lhs) + " -> " + getNameFromFilesJson(item.rhs) + ");##;";
                                                moduleDataTrace += operateTrace;
                                                modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});
                                            }else if(columnNameMap.hasOwnProperty(item.path[3])){ // 其他列改变
                                                const operateTrace = moduleName + positionInfo + "("   +  item.lhs + " -> " + item.rhs + ");##;";
                                                moduleDataTrace += operateTrace;
                                                modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});
                                            }else{

                                            }

                                        }
                                        else if(item.kind === "A"){ // 新增或删除行 ， kind=N 是有些上传文件导致生成新属性，忽略掉

                                            if(item.item.kind === "D"){ // 删除行
                                                const operateTrace = afterData.info.name + jsLang["module"] + jsLang["delete_row"] + " " + ( item.index + 1)  + ";##;";
                                                moduleDataTrace += operateTrace;
                                                modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});
                                            }
                                            else if(item.item.kind === "N"){ // 新增行

                                                // 若新增行同时填了值，显示新增的值
                                                newRowValue = "";
                                                Object.keys(columnNameMap).forEach(function (nameItem) {
                                                    if(nameItem === "upload_file" && item.item.rhs.upload_file !=="[]"){ // 文件单独处理
                                                        newRowValue +=  columnNameMap["upload_file"] + ":"  + item.item.rhs["upload_file"] + "," ;
                                                    }else if(nameItem !== "upload_file" && item.item.rhs[nameItem]){
                                                        newRowValue +=  columnNameMap[nameItem] + ":"  + item.item.rhs[nameItem] + "," ;
                                                    }
                                                });
                                                const operateTrace = afterData.info.name + jsLang["module"] + jsLang["add_row"] + " " + (item.index + 1) + "(" + newRowValue + ");##;";
                                                moduleDataTrace += operateTrace;
                                                modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});
                                            }
                                        }
                                        break;
                                    case 13: //自定义表格

                                        moduleName = afterData.info.name + jsLang["module"];
                                        if(item.kind === "E"){  //编辑单元格内容 改变文件内容列是A类

                                            if(item.path[1] === "field_key"){ // 自定义标题改变
                                                positionInfo = jsLang["title_row_change"] + item.path[2].replace(/[^\d]/g, '') + " "  +  jsLang["column"]; // 单元格位置信息
                                                const operateTrace = moduleName + positionInfo +"("   +   item.lhs + " -> " + item.rhs + ");##;";
                                                moduleDataTrace += operateTrace;
                                                modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});
                                            }else if(item.path[1] === "field_value"){ // 自定义内容改变
                                                positionInfo = jsLang["data_row_change"] + " " + item.path[3].replace(/[^\d]/g, '') + " "  +  jsLang["column"] + " " + (item.path[2] + 1) + " "  + jsLang["row"] ; // 单元格位置信息
                                                const operateTrace = moduleName + positionInfo + "("   +  item.lhs + " -> " + item.rhs + ");##;";
                                                moduleDataTrace += operateTrace;
                                                modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});
                                            }

                                        }
                                        else if(item.kind === "A"){ // 新增或删除行 文件框新增或删除文件


                                            if(!item.path[2]){ // path不存在第三个参数说明是新增删除行

                                                if(item.item.kind === "D"){ // 删除行
                                                    const operateTrace = moduleName + jsLang["delete_row"] + " " + ( item.index + 1)  + ";##;";
                                                    moduleDataTrace += operateTrace;
                                                    modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});
                                                }
                                                else if(item.item.kind === "N"){ // 新增行
                                                    // 若新增行同时填了值，显示新增的非空值
                                                    newRowValue = "";
                                                    Object.keys(item.item.rhs).forEach(function (key) { // key
                                                        if(item.item.rhs[key] && key !== "upload_file_data" && key.search(/data\d/) !== -1){ // 文件暂不处理，新增行同时传文件不大可能
                                                            newRowValue +=  key.replace('data', jsLang['field']) + ":"  + item.item.rhs[key] + ";" ;
                                                        }
                                                    });
                                                    const operateTrace = moduleName + jsLang["add_row"] + " " + (item.index + 1) + "(" + newRowValue + ");##;";
                                                    moduleDataTrace += operateTrace;
                                                    modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});
                                                }
                                            }else if(item.path[3] && item.path[3] ==="upload_file_data"){ // 新增或删除文件框里文件

                                                if(item.item.kind === "D"){ // 删除文件
                                                    positionInfo = jsLang["file_row_change"] + " " + (item.path[2] + 1) + " "  + jsLang["row"] ; // 单元格位置信息
                                                    var deleteFileInfo = jsLang["delete_file"] + "" + (item.index + 1) + "" + "("  + item.item.lhs.real_name + ");##;" // 删除了文件5(name)
                                                    const operateTrace = moduleName +  positionInfo  +   deleteFileInfo;
                                                    moduleDataTrace += operateTrace;
                                                    modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});

                                                }else if(item.item.kind === "N"){ //新增文件

                                                    positionInfo = jsLang["file_row_change"] + " " + (item.path[2] + 1) + " "  + jsLang["row"] ; // 单元格位置信息
                                                    var addFileInfo = jsLang["add_new_file"] + "" + (item.index + 1) + "" + "("  + item.item.rhs.real_name + ");##;" // 新增了文件5(name)
                                                    const operateTrace = moduleName +  positionInfo  +  addFileInfo;
                                                    moduleDataTrace += operateTrace;
                                                    modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});

                                                }

                                            }

                                        }
                                        else if(item.kind === "N"){ // 新增列 每次加一个单元格

                                            if(item.path[1] ==="field_key"){ // 添加列标题

                                                const operateTrace = moduleName +  jsLang["add_define_column"] + "(" + item.rhs + ");##;";// 添加了自定义列（name）
                                                moduleDataTrace += operateTrace;
                                                modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});

                                            }else if(item.path[1] ==="field_value"){ // 添加列内容

                                                positionInfo = " " + item.path[3].replace(/[^\d]/g, '') + " "  + jsLang["column"] + " "+ (item.path[2] + 1) + " " + jsLang["row"] ; // 单元格位置信息
                                                const operateTrace = moduleName + jsLang["add_define_column"] + positionInfo +  "(" + item.rhs + ");##;" ;
                                                moduleDataTrace += operateTrace;
                                                modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});

                                            }

                                        }
                                        else if(item.kind === "D"){ // 删除列 每次删一个单元格

                                            if(item.path[1] ==="field_key"){ // 删除列标题

                                                const operateTrace = moduleName +  jsLang["delete_define_column"] + "(" + item.lhs + ");##;";// 添加了自定义列（name）
                                                moduleDataTrace += operateTrace;
                                                modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});

                                            }else if(item.path[1] ==="field_value"){ // 删除列内容

                                                positionInfo = " " + item.path[3].replace(/[^\d]/g, '') + " "  + jsLang["column"] + " "+ (item.path[2] + 1) + " " + jsLang["row"]; // 单元格位置信息

                                                const operateTrace = moduleName + jsLang["delete_define_column"] + positionInfo +  "(" + item.lhs + ");##;";
                                                moduleDataTrace += operateTrace;
                                                modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});

                                            }
                                        }

                                        break;
                                    case 16: // TLC
                                        moduleName = afterData.info.name + jsLang["module"];

                                        var tlcNameMap = {
                                            "no": jsLang["trace_no"],   //编号
                                            "eluent": jsLang["trace_eluent"], // 展开剂
                                            "ratio": jsLang["ratio"],
                                            "cdr": jsLang["cdr"], //显色剂
                                            "remark": jsLang["trace_remark"],
                                            "rf1": "Rf1",
                                            "rf2": "Rf2",
                                            "rf3": "Rf3",
                                            "rf4": "Rf4",
                                            "rf5": "Rf5",
                                        };
                                        if(item.kind === "E"){  //编辑单元格内容  上传文件会新增 3个kind N变化 忽略掉

                                            if(item.path[2] === "png_data" ){
                                                const operateTrace = moduleName + jsLang["tlc_canvas_change"] + ";##;"; //画布变化
                                                moduleDataTrace += operateTrace;
                                                modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});
                                            }else if(item.path[2] === "table_data"){

                                                positionInfo = " " + tlcNameMap[item.path[4]] + " "  +  jsLang["column"] + " " + (item.path[3] + 1) + " "  + jsLang["row"] ; // 单元格位置信息
                                                const operateTrace = moduleName + positionInfo + "("   +  item.lhs + " -> " + item.rhs + ");##;";
                                                moduleDataTrace += operateTrace;
                                                modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});
                                            }

                                        }
                                        else if(item.kind === "A"){ // 新增或删除行 ， kind=N 是有些上传文件导致生成新属性，忽略掉

                                            if(item.item.kind === "D"){ // 删除行
                                                const operateTrace = moduleName + jsLang["delete_row"] + " " + ( item.index + 1)  + ";##;";
                                                moduleDataTrace += operateTrace
                                                modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});
                                            }
                                            else if(item.item.kind === "N"){ // 新增行

                                                // 若新增行同时填了值，显示新增的值
                                                newRowValue = "";
                                                Object.keys(tlcNameMap).forEach(function (nameItem) {

                                                    if(item.item.rhs[nameItem]){
                                                        newRowValue +=  tlcNameMap[nameItem] + ":"  + item.item.rhs[nameItem] + "," ;
                                                    }
                                                });
                                                const operateTrace = moduleName + jsLang["add_row"] + " " + (item.index + 1) + "(" + newRowValue + ");##;";
                                                moduleDataTrace += operateTrace;
                                                modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});
                                            }
                                        }




                                        break;
                                    case 7: // 上传文件
                                    case 8: // 上传图片

                                        moduleName = afterData.info.name + jsLang["module"];
                                        if(item.kind === "A"){  //只显示增加或删除文件

                                            if(item.item.kind === "D"){
                                                const operateTrace = moduleName + jsLang["delete_file"] + " " + ( item.index + 1)  +"(" + item.item.lhs.file_name + ");##;" ; // 文件模块删除文件2 (文件名)
                                                moduleDataTrace += operateTrace;
                                                modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});
                                            }else if(item.item.kind === "N"){
                                                const operateTrace = moduleName + jsLang["add_new_file"] + " " + ( item.index + 1)  +"(" + item.item.rhs.file_name + ");##;" ; // 文件模块删除文件2 (文件名)
                                                moduleDataTrace += operateTrace;
                                                modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});
                                            }
                                        }

                                        break;
                                    case 2: // 实验操作
                                    case 3: // 结果与讨论
                                    case 11: // 文本编辑
                                    case 12: // 摘要
                                        moduleName = afterData.info.name + jsLang["module"]; // 模块名称
                                        if(item.kind === "E"){ // 编辑器内容变化,只比较字符串变化
                                            var beforeText = $('<div>' + item.lhs + '</div>')[0].innerText;
                                            var afterText = $('<div>' + item.rhs + '</div>')[0].innerText;
                                            var textTrace = '';

                                            /*var textDiff = require('bin/diff.min').diffChars(beforeText, afterText);
                                            textDiff.forEach(function (part) {
                                                textTrace += part.added ? (jsLang["trace_add"] + '"' + part.value + '";') : (part.removed ? (jsLang["trace_delete"] + '"' + part.value + '";') : '');
                                            });*/

                                            /**
                                             * 上面的diffChars有性能问题，比如当文本编辑器中含有大量内容，然后选中其中一半的内容进行删除操作
                                             * 采用Google的diff_match_patch(https://github.com/google/diff-match-patch)进行替换
                                             */
                                            require('bin/diff_match_patch');
                                            var dmp = new diff_match_patch();
                                            var diff = dmp.diff_main(beforeText, afterText);
                                            dmp.diff_cleanupSemantic(diff);
                                            diff.forEach(function (item) {
                                                if (item[0] === -1) {
                                                    textTrace += jsLang["trace_delete"] + '"' + item[1] + '";'
                                                } else if (item[0] === 1) {
                                                    textTrace += jsLang["trace_add"] + '"' + item[1] + '";'
                                                }
                                            });

                                            const operateTrace = moduleName + jsLang["value_change"] + textTrace + ";##;";
                                            moduleDataTrace += operateTrace;
                                            modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});
                                        }
                                        break;
                                    case 18: //多功能表格
                                        moduleName = afterData.info.name + jsLang["module"]; // 模块名称

                                        if(item.path[1] ==="data"){ //  settings 忽略
                                            if(item.kind === "E"){ // 编辑器内容变化,只比较字符串变化

                                                var cellDiff = DeepDiff.diff(JSON.parse(item.lhs), JSON.parse(item.rhs));
                                                cellDiff.forEach(function (cellItem) {
                                                    positionInfo = " " + (cellItem.path[0] + 1) + " "  +  jsLang["row"] + " " + ( cellItem.path[1] + 1 ) + " "  + jsLang["column"] ; // 单元格位置信息
                                                    var beforeText = cellItem.lhs ;
                                                    var afterText = cellItem.rhs ;

                                                    if($(beforeText).length > 0){
                                                        beforeText = $(beforeText)[0].innerText;
                                                    }
                                                    if($(afterText).length > 0){
                                                        afterText = $(afterText)[0].innerText;
                                                    }
                                                    const operateTrace = moduleName + positionInfo + "("   +  beforeText + " -> " + afterText + ");##;";
                                                    moduleDataTrace += operateTrace;
                                                    modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});
                                                })


                                            }
                                        }

                                        break;
                                    case 19: // xsheet
                                        moduleName = afterData.info.name + jsLang["module"]; // 模块名称
                                        if (item.path[1] === "data") {

                                            var AfterItem = JSON.parse(item.rhs);
                                            var sheetDiff = DeepDiff.diff(JSON.parse(item.lhs), AfterItem);

                                            for (var i = 0; i < sheetDiff.length; i++) {

                                                try {
                                                    if (i > 5200) { // 只记录5200 条 超过数据库记录太大不好展示
                                                        break
                                                    }

                                                    var cellItem = sheetDiff[i];
                                                    // 样式等改变不记录痕迹 跳过当前循环  // add by hkk 2020/7/14
                                                    if ((cellItem.kind === 'E' && cellItem.path[6] === 'style') || // 样式改变
                                                        (cellItem.kind === 'N' && cellItem.path[4] === 'height') || // 行高改变
                                                        (cellItem.kind === 'E' && cellItem.path[4] === 'scroll') ||
                                                        (cellItem.kind === 'E' && cellItem.path[4] === 'scrollPos') ||
                                                        (cellItem.kind === 'N' && cellItem.path[6] === 'style') ||  // 样式改变
                                                        (cellItem.kind === 'E' && cellItem.path[2] === 'selectorRange') // 选取改变
                                                    ) {
                                                        continue
                                                    }

                                                    var sheetName = AfterItem[cellItem.path[0]].name;
                                                    var rowNumber, colNumber, beforeText, afterText, coords;
                                                    if (cellItem.kind === 'N' && cellItem.path[1] === 'data' && cellItem.path[2] === 'rows') { // 单元格新增数据
                                                        rowNumber = cellItem.path[3];
                                                        beforeText = '';
                                                        if (cellItem.path[4] && cellItem.path[4] === 'cells' && cellItem.path[5]) { // 新增在已有数据的行上
                                                            colNumber = cellItem.path[5];
                                                            afterText = cellItem.rhs.text || cellItem.rhs;
                                                            if(typeof afterText === 'object'){ // add by hkk 2020/7/14
                                                                continue
                                                            }
                                                        } else {
                                                            try {
                                                                colNumber = getFirstKey(cellItem.rhs.cells);
                                                                afterText = cellItem.rhs.cells[colNumber].text;
                                                                if(afterText=== undefined){ // add by hkk 2020/7/14
                                                                    continue
                                                                }
                                                            } catch (e) {
                                                                uploadExceptionLog(e);
                                                            }
                                                        }
                                                        coords = getCoordsFromXY(parseInt(colNumber), parseInt(rowNumber));
                                                        positionInfo = " " + sheetName + " " + coords; //位置信息
                                                        const operateTrace = moduleName + positionInfo + "(" + beforeText + " -> " + afterText + ");##;";
                                                        moduleDataTrace += operateTrace;
                                                        modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});
                                                    }
                                                    else if (cellItem.kind === 'E' && cellItem.path[6] === 'text') {// 单元格改变数据
                                                        rowNumber =cellItem.path[3];
                                                        colNumber = cellItem.path[5];
                                                        beforeText = cellItem.lhs;
                                                        afterText = cellItem.rhs;
                                                        coords = getCoordsFromXY(parseInt(colNumber), parseInt(rowNumber));
                                                        positionInfo = " " + sheetName + " " + coords; //位置信息
                                                        const operateTrace = moduleName + positionInfo + "(" + beforeText + " -> " + afterText + ");##;";
                                                        moduleDataTrace += operateTrace;
                                                        modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});
                                                    }
                                                    else if (cellItem.kind === 'D' && cellItem.path[6] === 'text') {// 单元格删除数据

                                                        rowNumber = cellItem.path[3];
                                                        colNumber = cellItem.path[5];
                                                        beforeText = cellItem.lhs;
                                                        afterText = '';

                                                        coords = getCoordsFromXY(parseInt(colNumber), parseInt(rowNumber));

                                                        positionInfo = " " + sheetName + " " + coords; //位置信息
                                                        const operateTrace = moduleName + positionInfo + "(" + beforeText + " -> " + afterText + ");##;";
                                                        moduleDataTrace += operateTrace;
                                                        modulesTraceRecordList.push({exp_module_relay_id: expModuleRelayId, operate_trace: operateTrace});
                                                    }
                                                } catch (e) {
                                                    uploadExceptionLog(e);
                                                }
                                            }
                                        }

                                        break;
                                    default:
                                        break;

                                }
                            }

                        })
                    }
                });

                // 模块修改痕迹 每条痕迹可用";##;"分割，非空保存到数据库
                if(moduleDataTrace && moduleDataTrace !== ''){
                    $.ajaxFn({
                        url: ELN_URL + '?r=history/save-experiment-trace-detail',
                        data: {
                            exp_id:exp_id,
                            create_time,
                            traceDetail:moduleDataTrace,
                            modules_trace_record_list: modulesTraceRecordList,
                        },
                        noLoad: true,
                        success: function (res) {
                            if (res.status == 1) {
                            }
                        }
                    }, null, true, true);
                }


            });
        },

        // 深拷贝对象 add by hkk 2019/11/27
        deepClone:function (initalObj){
            try {
                if(initalObj === null) return null
                if(typeof initalObj !== 'object') return obj;
                if(initalObj.constructor===Date) return new Date(initalObj);
                var newObj = new initalObj.constructor ();  //保持继承链
                for (var key in initalObj) {
                    if (initalObj.hasOwnProperty(key)) {   //不遍历其原型链上的属性
                        var val = initalObj[key];
                        newObj[key] = typeof val === 'object' ? arguments.callee(val) : val; // 使用arguments.callee解除与函数名的耦合
                    }
                }
                return newObj;
            } catch (e) {
                uploadExceptionLog(e);
                return JSON.parse(JSON.stringify(initalObj))
            }
        },

        /**
         * 保存单个模块
         * @param relayId 模块ID
         * @param relayPos 模块在实验中的顺序
         */
        saveSingleModule: function (relayId, relayPos) {
            var $module = $('#modul_line_' + relayId).find('[modul_part]');
            var moduleData = save_exp.getModuleData($module, relayPos - 1);
            var reqData = {
                exp_id: $('.exp_conetnt.active #exp_id').val(),
                module_data: moduleData
            };
            save_exp.saveSingleModuleAjax(relayId, reqData);
        },

        /**
         * 保存单个模块Ajax请求
         * @param relayId 模块ID
         * @param ajaxData
         */
        saveSingleModuleAjax: function (relayId, ajaxData) {
            const pako = require('pako1/pako'); // 引入加密的库

            // changed by xyx 2024.1.24 intable数据应该加密module_data.data.data，其他模块应该加密module_data.data
            // let tempAjaxData = ajaxData; //保留一份未加密的数据用于痕迹对比
            const tmpAjaxData = $.extend(true, {}, ajaxData);

            /* 对intable模块数据进行压缩加密 */
            if (tmpAjaxData.module_data.info.component_id == 19) {
                // intable 数据和其他模块不一样
                const zipStr = pako.gzip(tmpAjaxData.module_data.data.data, { to: 'string', level: 3 });
                const binStr = btoa(zipStr);
                tmpAjaxData.module_data.info.encode_type = 'gzip';
                tmpAjaxData.module_data.data.data = binStr;
            }

            // bug#777,对模块数据进行压缩编码,统一在后端解码,以防止模块数据被xss过滤系统破坏
            const moduleDataJSON = JSON.stringify(tmpAjaxData.module_data.data);
            const zipStr = pako.gzip(moduleDataJSON, { to: 'string', level: 3 });
            const binStr = btoa(zipStr);
            tmpAjaxData.module_data.info.encode_type = 'gzip';
            tmpAjaxData.module_data.data = binStr;

            $.ajaxFn({
                url: '/?r=experiment/save-module',
                data: tmpAjaxData,
                success: function (res) {
                    if (res.status == 1) {
                        $.closeModal();
                        $.showAlert(mainLang('save_success'));

                        $.ajaxFn({
                            url: '/?r=experiment/reload-module',
                            type: 'GET',
                            data: {
                                relay_id: relayId,
                                for_edit: 0
                            },
                            success: function (res) {
                                if (res.status == 1) {
                                    $('#modul_line_' + relayId).replaceWith(res.data);
                                } else {
                                    $.showAlert(res.info);
                                }
                            }
                        });
                        // 加载完后记录当前模块数据用于痕迹比对 // add by hkk 2021/3/1
                        if(ajaxData.module_data?.data?.trace != null && Array.isArray(ajaxData.module_data?.data?.trace)){
                            let moduleDataTrace = '';
                            const moduleName = ajaxData.module_data.info?.name + jsLang["module"] + ': '; // 模块名称
                            ajaxData.module_data.data.trace.forEach(function (trace) {
                                moduleDataTrace += (moduleName + trace + ';##;');
                            })
                            if(moduleDataTrace !== ''){
                                $.ajaxFn({
                                    url: ELN_URL + '?r=history/save-experiment-trace-detail',
                                    data: {
                                        exp_id: ajaxData.exp_id,
                                        traceDetail: moduleDataTrace
                                    },
                                    noLoad: true,
                                    success: function (res) {
                                        if (res.status == 1) {
                                        }
                                    }
                                }, null, true, true);
                            }
                        }else{
                            save_exp.saveCoAuthorTraceDetail(window.previousModulData, ajaxData.module_data, $('.exp_conetnt.active #exp_id').val(), res.data.create_time);
                        }
                    } else {// 弹窗，用于输入密码和原因
                        var resData = res.data;
                        if (resData && resData.pop_dialog && !ajaxData.from_dialog) {
                            $.showContent('warning', mainLang('save'), resData.dialog_html, function () {
                                ajaxData.password = $('.save-dialog .password_input').val().trim();
                                if (ajaxData.password === '') {
                                    $.showAlert(mainLang('pls_input_password'));
                                    return false;
                                }
                                ajaxData.password = $.passwordEncipher(ajaxData.password);

                                var $reasonDom = $('.save-dialog .reason-textarea');
                                if (!$reasonDom.is(':visible')) {
                                    $reasonDom = $('.save-dialog [name=submit_reason]');
                                }
                                ajaxData.reason = $reasonDom.val().trim();
                                if (ajaxData.reason === '') {
                                    $.showAlert(mainLang('pls_input_reason'));
                                    return false;
                                }

                                ajaxData.from_dialog = true;

                                save_exp.saveSingleModuleAjax(relayId, ajaxData);
                            });
                            return;
                        }

                        $.showAlert(res.info);
                        $.closeModal();
                    }
                }
            });
        },

    };
    return save_exp;
});
