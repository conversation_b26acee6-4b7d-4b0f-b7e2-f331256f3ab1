define(function (require) {

    const {
        RxnFingerprintStore,
        updateChemServiceData,
        isParsedRxnEmpty,
    } = require('components/in_draw/in_draw_utils');
    // in_material_utils设置了导入别名, 使用别名导入
    const InMaterialUtils = require('in_material_js/in_material_utils');

    $.addDataToProduce = function () {
        //var thisId = $('.exp_conetnt.active #exp_id').val();
        var type = $('.exp_conetnt.active #type').val();
        if(type == 2){
            var thisId = $('.exp_conetnt.active #temp_id').val();
        }
        else {
            var thisId = $('.exp_conetnt.active #exp_id').val();
        }
        if ($('.exp_conetnt.active .nb_unit_' + thisId).val() == 1) {
            var nb_unit = ' μmol';
        } else if ($('.exp_conetnt.active .nb_unit_' + thisId).val() == 2) {
            var nb_unit = ' mmol';
        } else if ($('.exp_conetnt.active .nb_unit_' + thisId).val() == 3) {
            var nb_unit = ' mol';
        }else if ($('.exp_conetnt.active .nb_unit_' + thisId).val() == 4) {
            var nb_unit = ' kmol';
        }
        if ($('.exp_conetnt.active .mass_unit_' + thisId).val() == 1) {
            var mass_unit = ' mg';
        } else if ($('.exp_conetnt.active .mass_unit_' + thisId).val() == 2) {
            var mass_unit = ' g';
        } else if ($('.exp_conetnt.active .mass_unit_' + thisId).val() == 3) {
            var mass_unit = ' kg';
        } else if ($('.exp_conetnt.active .mass_unit_' + thisId).val() == 4) {
            var mass_unit = ' ton';
        }

        if ($('.exp_conetnt.active .product_nb_unit_' + thisId).val() == 1) {
            var product_nb_unit = ' μmol';
        } else if ($('.exp_conetnt.active .product_nb_unit_' + thisId).val() == 2) {
            var product_nb_unit = ' mmol';
        } else if ($('.exp_conetnt.active .product_nb_unit_' + thisId).val() == 3) {
            var product_nb_unit = ' mol';
        }else if ($('.exp_conetnt.active .product_nb_unit_' + thisId).val() == 4) {
            var product_nb_unit = ' kmol';
        }

        if ($('.exp_conetnt.active .product_theo_unit_' + thisId).val() == 3) {
            var produce_mass_unit = ' kg';
        } else if ($('.exp_conetnt.active .product_theo_unit_' + thisId).val() == 2) {
            var produce_theo_unit = ' g';
        } else if ($('.exp_conetnt.active .product_theo_unit_' + thisId).val() == 1){
            var produce_theo_unit = ' mg';
        }else if ($('.exp_conetnt.active .product_theo_unit_' + thisId).val() == 4){
            var produce_theo_unit = ' ton';
        }

        if ($('.exp_conetnt.active .product_mass_unit_' + thisId).val() == 3) {
            var produce_mass_unit = ' kg';
        } else if ($('.exp_conetnt.active .product_mass_unit_' + thisId).val() == 2) {
            var produce_mass_unit = ' g';
        } else if ($('.exp_conetnt.active .product_mass_unit_' + thisId).val() == 1) {
            var produce_mass_unit = ' mg';
        }else if ($('.exp_conetnt.active .product_mass_unit_' + thisId).val() == 4) {
            var produce_mass_unit = ' ton';
        }

        if ($('.exp_conetnt.active .volume_unit_' + thisId).val() == 1) {
            var volume_unit = ' μL';
        } else if ($('.exp_conetnt.active .volume_unit_' + thisId).val() == 2) {
            var volume_unit = ' mL';
        } else if ($('.exp_conetnt.active .volume_unit_' + thisId).val() == 3) {
            var volume_unit = ' L';
        }else if ($('.exp_conetnt.active .volume_unit_' + thisId).val() == 4) {
            var volume_unit = ' kL';
        }

        if ($('.exp_conetnt.active .solvent_volume_unit_' + thisId).val() == 1) {
            var solvent_volume_unit = ' μL';
        } else if ($('.exp_conetnt.active .solvent_volume_unit_' + thisId).val() == 2) {
            var solvent_volume_unit = ' mL';
        } else if ($('.exp_conetnt.active .solvent_volume_unit_' + thisId).val() == 3) {
            var solvent_volume_unit = ' L';
        }else if ($('.exp_conetnt.active .solvent_volume_unit_' + thisId).val() == 4) {
            var solvent_volume_unit = ' kL';
        }

        if ($('.exp_conetnt.active .solvent_mass_unit_' + thisId).val() == 1) {
            var solvent_mass_unit = ' mg';
        } else if ($('.exp_conetnt.active .solvent_mass_unit_' + thisId).val() == 2) {
            var solvent_mass_unit = ' g';
        } else if ($('.exp_conetnt.active .solvent_mass_unit_' + thisId).val() == 3) {
            var solvent_mass_unit = ' kg';
        } else if ($('.exp_conetnt.active .solvent_mass_unit_' + thisId).val() == 4) {
            var solvent_mass_unit = ' ton';
        }

        var getAddData = function (start, postData, isGetV) {
            var addThisData = {};

            for (var i = 0; i < postData[start + '_name'].length; i++) {
                var dataKey = '';
                if (typeof postData[start + '_name'] != 'undefined') {
                    if (postData[start + '_name'][i] != '') {
                        dataKey = postData[start + '_name'][i] + '_*_' + postData[start + '_inchi'][i] + '_*_' + postData[start + '_smiles'][i] + '_*_' + [i];
                        addThisData[dataKey] = postData[start + '_name'][i];
                        addThisData[dataKey] += ' (';

                        var mass = postData[start + '_mass'][i];
                        var v = postData[start + '_volume'][i];

                        var trType = start; //$('.tr_:eq('+i+')').attr('addtype');

                        //反应物 催化剂
                        if (trType == 'substrates' || trType == 'catalysts') {
                            if ((mass || mass == '0') && (!v && v != '0')) { //只有质量 没有体积    显示质量和摩尔数
                                addThisData[dataKey] += '' + postData[start + '_mass'][i] + '';
                                addThisData[dataKey] += '' + mass_unit + '';
                                addThisData[dataKey] += ',' + ' ' + postData[start + '_nb'][i] + '';
                                addThisData[dataKey] += '' + nb_unit + '';
                            }
                            ;

                            if ((mass || mass == '0') && (v || v == '0')) { //质量体积都有    显示体积和摩尔数
                                addThisData[dataKey] += '' + postData[start + '_volume'][i] + '';
                                addThisData[dataKey] += '' + volume_unit + '';
                                addThisData[dataKey] += ',' + ' ' + postData[start + '_nb'][i] + '';
                                addThisData[dataKey] += '' + nb_unit + '';
                            }
                            if ((v || v == '0') && (!mass && mass != '0')) { //只有体积
                                addThisData[dataKey] += '' + postData[start + '_volume'][i] + '';
                                addThisData[dataKey] += '' + volume_unit + '';
                            }
                        }
                        ;

                        //溶剂
                        if (trType == 'solvent') {
                            if ((mass || mass == '0') && (v || v == '0')) { //质量和体积都有数据时，只显示体积；
                                addThisData[dataKey] += '' + postData[start + '_volume'][i] + '';
                                addThisData[dataKey] += '' + solvent_volume_unit + '';
                            } else if ((mass || mass == '0') && (!v && v != '0')) { //只有质量时，只显示质量；
                                addThisData[dataKey] += '' + postData[start + '_mass'][i] + '';
                                addThisData[dataKey] += '' + solvent_mass_unit + '';
                            } else if ((v || v == '0') && (!mass && mass != '0')) { //只有体积  显示体积
                                addThisData[dataKey] += '' + postData[start + '_volume'][i] + '';
                                addThisData[dataKey] += '' + solvent_volume_unit + '';
                            }
                        }
                        addThisData[dataKey] += ')  ';
                    }
                }
            }
            return addThisData;
        };
        var jsonAddData = {};

        var substratesData = {};
        getExperimentData('substrates', substratesData, thisId);
        jsonAddData['Reactant'] = getAddData('substrates', substratesData);

        var catalystsData = {};
        getExperimentData('catalysts', catalystsData, thisId);
        jsonAddData['Catalyst'] = getAddData('catalysts', catalystsData);

        var solventData = {};
        getExperimentData('solvent', solventData, thisId);
        jsonAddData['Solvent'] = getAddData('solvent', solventData, 1);

        var productData = {};
        getExperimentData('product', productData, thisId);
        jsonAddData['Product'] = getAddData('product', productData, 1);

        var substratesData = {};
        getExperimentData('product', substratesData, thisId, 1);

        var subData = {};
        for (var i = 0; i < substratesData['product_name'].length; i++) {
            var dataKey = '';
            if (typeof substratesData['product_name'] != 'undefined') {
                if (substratesData['product_name'][i] != '') {
                    dataKey = substratesData['product_name'][i] + '_*_' + substratesData['product_inchi'][i] + '_*_' + substratesData['product_smiles'][i] + '_*_' + [i];
                    subData[dataKey] = substratesData['product_name'][i];
                    subData[dataKey] += ' (';
                    //product: 显示质量、摩尔数和收率
                    var mass = substratesData['product_mass'][i];
                    var n = substratesData['product_nb'][i];
                    var yield = substratesData['product_yield'][i];

                    if (mass || mass == '0') {
                        subData[dataKey] += '' + mass + '';
                        subData[dataKey] += '' + produce_mass_unit + '';
                    }
                    if (n || n == '0') {
                        subData[dataKey] += ',' + ' ' + n + '';
                        subData[dataKey] += '' + product_nb_unit + '';
                    }

                    if (yield || yield == '0') {
                        subData[dataKey] += ',' + ' ' + yield +'%';
                    }


                    subData[dataKey] += ') ';

                }
            }
        }
        jsonAddData['Product'] = subData;
        console.log(jsonAddData);
        return jsonAddData;
    };

    function getExperimentData(start, postData, thisId, type) {
        if (typeof type == 'undefined') {
            postData[start + '_name'] = getSameNameValue(start + '_name', thisId, 0, 1, 0).arr;

            postData[start + '_salt_id'] = getSameNameValue(start + '_salt_id', thisId, 0, 1, 0).arr;
            postData[start + '_salt_num'] = getSameNameValue(start + '_salt_num', thisId, 0, 1, 0).arr;
            postData[start + '_solvent_id'] = getSameNameValue(start + '_solvent_id', thisId, 0, 1, 0).arr;

            postData[start + '_equivalent'] = getSameNameValue(start + '_equivalent', thisId).arr;
            postData[start + '_equivalent_isInput'] = getSameNameValue(start + '_equivalent', thisId).isInputArr;
            postData[start + '_nb'] = getSameNameValue(start + '_nb', thisId).arr;
            postData[start + '_nb_isInput'] = getSameNameValue(start + '_nb', thisId).isInputArr;
            postData[start + '_molweight'] = getSameNameValue(start + '_molweight', thisId).arr;
            postData[start + '_mass'] = getSameNameValue(start + '_mass', thisId).arr;
            postData[start + '_mass_isInput'] = getSameNameValue(start + '_mass', thisId).isInputArr;
            postData[start + '_density'] = getSameNameValue(start + '_density', thisId).arr;
            postData[start + '_volume'] = getSameNameValue(start + '_volume', thisId).arr;
            postData[start + '_volume_isInput'] = getSameNameValue(start + '_volume', thisId).isInputArr;
            postData[start + '_temperature'] = getSameNameValue(start + '_temperature', thisId).arr;
            postData[start + '_pressure'] = getSameNameValue(start + '_pressure', thisId).arr;
            postData[start + '_smiles'] = getSameNameValue(start + '_smiles', thisId, 0, 1, 0).arr;
            postData[start + '_inchi'] = getSameNameValue(start + '_inchi', thisId, 0, 1, 0).arr;
        } else {
            postData[start + '_num'] = getSameNameValue(start + '_num', thisId, 0, 1, 0).arr;
            postData[start + '_smiles'] = getSameNameValue(start + '_smiles', thisId, 0, 1, 0).arr;
            postData[start + '_inchi'] = getSameNameValue(start + '_inchi', thisId, 0, 1, 0).arr;
            postData[start + '_name'] = getSameNameValue(start + '_name', thisId, 0, 1, 0).arr;

            postData[start + '_salt_id'] = getSameNameValue(start + '_salt_id', thisId, 0, 1, 0).arr;
            postData[start + '_salt_num'] = getSameNameValue(start + '_salt_num', thisId, 0, 1, 0).arr;
            postData[start + '_solvent_id'] = getSameNameValue(start + '_solvent_id', thisId, 0, 1, 0).arr;

            postData[start + '_nb'] = getSameNameValue(start + '_nb', thisId).arr;
            postData[start + '_nb_isInput'] = getSameNameValue(start + '_nb', thisId).isInputArr;
            postData[start + '_molweight'] = getSameNameValue(start + '_molweight', thisId).arr;
            postData[start + '_theo'] = getSameNameValue(start + '_theo', thisId).arr;
            postData[start + '_mass'] = getSameNameValue(start + '_mass', thisId).arr;
            postData[start + '_mass_isInput'] = getSameNameValue(start + '_mass', thisId).isInputArr;
            postData[start + '_yield'] = getSameNameValue(start + '_yield', thisId).arr;
            postData[start + '_yield_isInput'] = getSameNameValue(start + '_yield', thisId).isInputArr;
            postData[start + '_purity'] = getSameNameValue(start + '_purity', thisId, 2).arr;
        }
    };

    function getSameNameValue(name, thisId, isCurr, isInput, isNum) {
        if (typeof isCurr == 'undefined')
            isCurr = 0;
        if (typeof isInput == 'undefined')
            isInput = 1;
        if (typeof isNum == 'undefined')
            isNum = 1;
        var arr = new Array();
        var isInputArr = new Array();

        var inputNameArr = $('.exp_conetnt.active .chendraw_data').find("input[name='" + name + "']");
        //var inputNameArr = $('.edit_experiment_content_' + thisId).children().find('table').find("input[name='" + name + "']");

        if (isCurr == 1) {
            inputNameArr = $(".exp_conetnt.active input[name='" + name + thisId + "']");
        } else if (isCurr == 2) {
            //inputNameArr = $('.edit_experiment_content_' + thisId).children().find('table').find("select[name='" + name + "']");
            inputNameArr = $('.exp_conetnt.active .chendraw_data').find("select[name='" + name + "']");
        }
        if (isInput == 0) {
            inputNameArr = $(".exp_conetnt.active span[name='" + name + thisId + "']");

            inputNameArr.each(function (index) {
                arr[index] = $(this).html();
            });
        } else {
            inputNameArr.each(function (index) {
                var inputVal = $(this).attr('isInput');
                if (typeof inputVal == 'undefined') {
                    inputVal = 0;
                }
                isInputArr[index] = inputVal;
                var thisVal = $(this).val();
                if (isNum == 1) {
                    if (thisVal == '') {
                        //thisVal = 0;
                    }
                }
                arr[index] = thisVal;
            });
        }
        return {'arr': arr, 'isInputArr': isInputArr};
    }

    //add by hkk 2019/3/15 获取当前物料表哪些字段是手动输入的信息
    function getUserInputString(selector) {
        //add by hkk 2019/3/15  改变手写输入标记的存储规则，直接存储到一个字符串中
        var userInputString ='';
        if( !!$(selector).find('[property="name"]').hasClass("userInputData")){
            userInputString +="name,";
        }
        if( !!$(selector).find('[property="equivalent"]').hasClass("userInputData")){
            userInputString +="eq,";
        }
        if( !!$(selector).find('[property="nb"]').hasClass("userInputData")){
            userInputString +="nb,";
        }
        if( !!$(selector).find('[property="molweight"]').hasClass("userInputData")){
            userInputString +="molweight,";
        }
        if( !!$(selector).find('[property="mass"]').hasClass("userInputData")){
            userInputString +="mass,";
        }
        if( !!$(selector).find('[property="dengsity"]').hasClass("userInputData")){
            userInputString +="density,"; // 质量
        }
        if( !!$(selector).find('[property="volume"]').hasClass("userInputData")){
            userInputString +="volume,";
        }
        if( !!$(selector).find('[property="temperature"]').hasClass("userInputData")){
            userInputString +="temperature,"; //反应物浓度
        }
        if( !!$(selector).find('[property="pressure"]').hasClass("userInputData")){
            userInputString +="pressure,";   //反应物纯度
        }
        if( !!$(selector).find('[property="theo"]').hasClass("userInputData")){
            userInputString +="theo,";      //产物理论质量
        }
        if( !!$(selector).find('[property="yield"]').hasClass("userInputData")){
            userInputString +="yield,";
        }
        if( !!$(selector).find('[property="purity"]').hasClass("userInputData")){
            userInputString +="purity,"; //产物纯度
        }
        if( !!$(selector).find('[property="ratio"]').hasClass("userInputData")){
            userInputString +="ratio,"; //产物纯度
        }

        return userInputString.substring(0,userInputString.length-1);//去掉最后的逗号
    }

    //add by hkk 2019/3/18 通过userInput字符串，给对应字段加上手动输入标记的class
    /**
     * @param {Object} data
     * @param {'substrates'|'catalysts'|'solvent'|'product'} type
     * @param {number} index
     */
    function addUserInputClass(data,type,index) {
        if( data && data.user_input_fields){
            var userInputArray = data.user_input_fields.split(","); // 类似[mass,volume,eq]
            for (var i = 0; i < userInputArray.length; i++) {
                const userInputFdName = userInputArray[i];
                const inputIdName = materielFn.userInputFd2InputIdMap[userInputFdName];
                const inputIdSelector = `.exp_conetnt.active #${type}_${inputIdName}_${index}`;
                $(inputIdSelector).addClass("userInputData");
                // var idSelector = ".exp_conetnt.active #" + type + "_" + userInputArray[i] + "_" + index ;
                // $(idSelector).addClass("userInputData");
            }
        }
    }

    /**
     *
     * @param sourceField2ValMap
     * @param fieldTrDom
     */
    function autoFillFileds(sourceField2ValMap, fieldTrDom) {
        // 数据源 field -> val

        // to 要填入的输入框
        var toTitleTrDomList = fieldTrDom.closest('table').find('tr:first input:visible');
        var toField2IndexMap = {};
        // field => index, 重复字段时, 匹配第一次出现的
        toTitleTrDomList.each(function (index, item) {
            var title = $(item).val().trim();
            if (title && typeof toField2IndexMap[title] === 'undefined') {
                toField2IndexMap[title] = index;
            }
        });
        // input - index => value
        var matchedInputIndexArr = [];
        var toFieldValMap = {};
        for (let field in toField2IndexMap) {
            if (typeof sourceField2ValMap[field] !== 'undefined') {
                toFieldValMap[toField2IndexMap[field]] = sourceField2ValMap[field];
                matchedInputIndexArr.push(toField2IndexMap[field])
            }
        }
        fieldTrDom.find('input:visible').each(function (index, item) {
            if (toFieldValMap[index]) {
                $(item).val(toFieldValMap[index]);
            }
        });

        return matchedInputIndexArr;
    }

    /**
     * 迭代反应式中的每个物质并返回该反应式的浅拷贝
     * @template T
     * @param {IParsedRxn} rxn
     * @param {(value: T, index: number) => T} mapFn
     * @returns {IParsedRxn}
     */
    const mapParsedRxn = (rxn, mapFn) => {
        return rxnCopy1 = Object.assign({}, rxn, {
            reactants: Array.from(rxn.reactants, (e, idx) => mapFn(e, idx)),
            catalysts: Array.from(rxn.catalysts, (e, idx) => mapFn(e, idx)),
            solvents : Array.from(rxn.solvents , (e, idx) => mapFn(e, idx)),
            products : Array.from(rxn.products , (e, idx) => mapFn(e, idx)),
        });
    }

    var materielFn = {
        synchronizing: false, // 标记是否从上到下同步中
        enNameArr: {},
        getAllName: {},

        /**
         * 字段值到<input>的property属性名称的映射
         */
        valueFdName2InputPropName: {
            eq:     'equivalent',
            n:      'nb',
            mw:     'molweight',
            mass:   'mass',
            d:      'dengsity',

            v:      'volume',
            c:      'temperature',

            theo:   'theo',
            yield:  'yield',
            purity: 'purity',
        },

        /**
         * 用户输入的值的字段名到<input> property属性的名称映射
         */
        userInputFd2InputPropMap: {
            eq:          'equivalent',
            nb:          'nb',
            molweight:   'molweight',
            mass:        'mass',
            density:     'dengsity',
            volume:      'volume',
            temperature: 'temperature',
            theo:        'theo',
            'yield':     'yield',
            purity:      'purity',
            ratio:       'ratio',
        },

        /**
         * 保存的用户输入值的字段名到<input> 的id中名称的映射
         */
        userInputFd2InputIdMap: {
            eq: `equivalent`,//当量值Eq
            nb: `nb`,//N
            molweight: `molweight`,//摩尔质量
            mass: `mass`,//质量
            density: `density`,//密度
            volume: `volume`,//体积
            temperature: `temperature`,//反应物C
            pressure: `pressure`,//反应物纯度
            theo: `theo`,//理论质量
            'yield': `yield`,//产物产率
            purity: `purity`,//纯度
            ratio: `ratio`, //溶剂比例
        },

        setData: function (type, expId, num, oldData, isPro) {
            $('.exp_conetnt.active .' + type + '_' + expId).find('#' + type + '_name_' + num).val(oldData['name']);
            $('.exp_conetnt.active .' + type + '_' + expId).find('#' + type + '_batch_num_' + num).val(oldData['batch_num']);
            $('.exp_conetnt.active .' + type + '_' + expId).find('#' + type + '_nb_' + num).val(oldData['nb']);
            $('.exp_conetnt.active .' + type + '_' + expId).find('#' + type + '_nb_' + num).attr('isinput', oldData['nb_isinput']);
            $('.exp_conetnt.active .' + type + '_' + expId).find('#' + type + '_molweight_' + num).val(oldData['molweight']);
            $('.exp_conetnt.active .' + type + '_' + expId).find('#' + type + '_mass_' + num).val(oldData['mass']);
            $('.exp_conetnt.active .' + type + '_' + expId).find('#' + type + '_mass_' + num).attr('isinput', oldData['mass_isinput']);

            $('.exp_conetnt.active .' + type + '_' + expId).find('#' + type + '_salt_id_' + num).val(oldData['salt_id']);
            $('.exp_conetnt.active .' + type + '_' + expId).find('#' + type + '_salt_name_' + num).val(oldData['salt_name']);
            $('.exp_conetnt.active .' + type + '_' + expId).find('#' + type + '_salt_num_' + num).val(oldData['salt_num']);
            $('.exp_conetnt.active .' + type + '_' + expId).find('#' + type + '_solvent_id_' + num).val(oldData['solvent_id']);
            $('.exp_conetnt.active .' + type + '_' + expId).find('#' + type + '_solvent_name_' + num).val(oldData['solvent_name']);

            $('.exp_conetnt.active .' + type + '_' + expId).find('#' + type + '_density_' + num).val(oldData['density']);
            $('.exp_conetnt.active .' + type + '_' + expId).find('#' + type + '_volume_' + num).val(oldData['volume']);
            $('.exp_conetnt.active .' + type + '_' + expId).find('#' + type + '_volume_' + num).attr('isinput', oldData['volume_isinput']);
            $('.exp_conetnt.active .' + type + '_' + expId).find('#' + type + '_temperature_' + num).val(oldData['temperature']);
            $('.exp_conetnt.active .' + type + '_' + expId).find('#' + type + '_pressure_' + num).val(oldData['pressure']);

            $('.exp_conetnt.active .' + type + '_' + expId).find('#' + type + '_theo_' + num).val(oldData['theo']);
            $('.exp_conetnt.active .' + type + '_' + expId).find('#' + type + '_theo_' + num).attr('isinput', oldData['theo_isinput']);
            $('.exp_conetnt.active .' + type + '_' + expId).find('#' + type + '_yield_' + num).val(oldData['yield']);
            $('.exp_conetnt.active .' + type + '_' + expId).find('#' + type + '_yield_' + num).attr('isinput', oldData['yield_isinput']);
            $('.exp_conetnt.active .' + type + '_' + expId).find('#' + type + '_purity_' + num).val(oldData['purity']);
            $('.exp_conetnt.active .' + type + '_' + expId).find('#' + type + '_purity_' + num).attr('isinput', oldData['purity_isinput']);

            var mw = $('.exp_conetnt.active .' + type + '_' + expId).find('#' + type + '_molweight_' + num).val();
            var saltNum = oldData['salt_num'];
            if (saltNum == '' || saltNum == 'undefined' || saltNum == false || saltNum == null || saltNum == 0) {
                saltNum = 0;
            }
            if (mw == '' || mw == 'undefined' || mw == false || mw == null || mw == 0) {
                mw = '';
            }

            var nmw = Number(mw) + Number(saltNum);
            if (nmw != 0) {
                $('.exp_conetnt.active .' + type + '_' + expId).find('#' + type + '_molweight_' + num).val(nmw);
            }

            if (mw != nmw) {
                //触发自动计算
                window.calc($('.exp_conetnt.active .' + type + '_' + expId).find('#' + type + '_molweight_' + num));
            }

            if (isPro == 1) {

                $('.exp_conetnt.active .' + type + '_' + expId).find('#' + type + '_yield_' + num).val(oldData['yield']);
                $('.exp_conetnt.active .' + type + '_' + expId).find('#' + type + '_yield_' + num).attr('isinput', oldData['yield_isinput']);

                $('.exp_conetnt.active .' + type + '_' + expId).find('#' + type + '_theo_' + num).val(oldData['theo']);
                $('.exp_conetnt.active .' + type + '_' + expId).find('#' + type + '_theo_' + num).attr('isinput', oldData['theo_isinput']);

                $('.exp_conetnt.active .' + type + '_' + expId).find('#' + type + '_purity' + num).attr('isinput', oldData['purity_isinput']);
                $('.exp_conetnt.active .' + type + '_' + expId).find('#' + type + '_purity' + num).val(oldData['purity']);

                $('.exp_conetnt.active .' + type + '_' + expId).find('.add_analysis_' + expId + '_' + num).html(oldData['analysis']);

            } else {
                $('.exp_conetnt.active .' + type + '_' + expId).find('#' + type + '_equivalent_' + num).val(oldData['eq']);
                $('.exp_conetnt.active .' + type + '_' + expId).find('#' + type + '_equivalent_' + num).attr('isinput', oldData['eq_isinput']);
                $('.exp_conetnt.active .' + type + '_' + expId).find('#' + type + '_density_' + num).val(oldData['density']);
                $('.exp_conetnt.active .' + type + '_' + expId).find('#' + type + '_volume_' + num).val(oldData['volume']);
                $('.exp_conetnt.active .' + type + '_' + expId).find('#' + type + '_volume_' + num).attr('isinput', oldData['volume_isinput']);
                $('.exp_conetnt.active .' + type + '_' + expId).find('#' + type + '_temperature_' + num).val(oldData['temperature']);
                $('.exp_conetnt.active .' + type + '_' + expId).find('#' + type + '_pressure_' + num).val(oldData['pressure']);
                $('.exp_conetnt.active .' + type + '_' + expId).find('#' + type + '_name_' + num).attr('come_from', oldData['come_from']);
            }
        },

        /**
         * 构造cal_value属性的指定键名
         * @param {string} frontFdName
         * @returns {`calc_val_${string}`}
         */
        fmtCalcDataFdKey: function (frontFdName) {
            return `calc_val_${frontFdName}`;
        },

        /**
         * 为请求得到的新数据添加对应的 cal_val 前缀标记
         * @param {Object} rowData
         * @returns {Object}
         */
        fmtNewRowDataCalcFdVal: function (rowData) {
            const fmtCalcFdArr = ['mw', 'd'];
            const fdNames = Reflect.ownKeys(rowData);
            const needFmtFdNames = fdNames.filter(fdn => fmtCalcFdArr.includes(fdn));
            needFmtFdNames.forEach(fdn => {
                const fdVal = Reflect.get(rowData, fdn);
                const calcFdName = materielFn.fmtCalcDataFdKey(fdn);
                Reflect.set(rowData, calcFdName, fdVal);
            });
            return rowData
        },

        /**
         * 从缓存数据中获取指定键的数据
         * @param {string} frontFdName
         * @param {Object} data
         * @returns {*}
         */
        getFdValueFromCacheData: function (frontFdName, data) {
            const fdKey = materielFn.fmtCalcDataFdKey(frontFdName);
            const val = data[fdKey];
            if (null == val) return '';
            // 兼容由于数据异常的情况
            if ('undefined' === val) return '';

            return val;
        },

        /**
         * 格式化用于input框显示的数字值
         * @param {Object} cachedData
         * @param {string} frontFdName - 数据中字段的值. 如: eq,n,d,v,
         * @param {number} fdDecimal
         * @param {string} defValue
         * @returns {string}
         */
        fmtInputDisplayValue: function (cachedData, {frontFdName, fdDecimal, defValue = ''}) {
            const originData = (null == cachedData) ? null : cachedData[frontFdName];

            /** @type {string} */
            const userInputFdsStr =  (null == cachedData.user_input_fields) ? '' : cachedData.user_input_fields;
            const userInputFdsArr = userInputFdsStr.split(',');

            // 当前字段值是用户输入的值,直接返回原始值
            const isUserInputFd = userInputFdsArr.some(userInputFd => {
                const userInputFdNameProp = materielFn.userInputFd2InputPropMap[userInputFd];
                const valueFdNameProp = materielFn.valueFdName2InputPropName[frontFdName];
                return userInputFdNameProp === valueFdNameProp;
            });
            if (isUserInputFd) {
                return originData;
            }

            // 如果值为空且默认值为空字符,则直接返回空字符
            if (null == originData && '' === defValue) {
                return defValue;
            }

            /** @type {number} - 用于显示的值 */
            let displayData = originData;
            if (null == originData) {
                displayData = defValue;
            }
            // 当前的值是计算值,经过舍入后显示
            return Number.parseFloat(displayData).toFixed(fdDecimal);
        },

        /** 获取物质各个字段cal_value的值到其值的映射
         * @param {jQuery} $tr
         * @param {Object} frontFdName2inputPropAttr - 字段在前端的名称与input元素的property属性名称的映射
         */
        getCalValAttrVal: function ({$tr, frontFdName2inputPropAttr,}) {
            const calcData = {};
            for (const frontFdName in frontFdName2inputPropAttr) {
                const inputPropAttr = frontFdName2inputPropAttr[frontFdName];
                const $valInput = $tr.find(`[property=${inputPropAttr}]`);
                const calc_val = $valInput.attr('cal_value');
                const fdKey = materielFn.fmtCalcDataFdKey(frontFdName);
                calcData[fdKey] = calc_val;
            }
            return calcData;
        },

        cacheData: function () {
            var cacheData = {
                reactants: [],
                reagents: [],
                products: [],
                solvents: [],
                conditions: []
            };
            $('.exp_conetnt.active tr.tr_').each(function () {
                var trData = {};
                var type = $(this).attr('addtype');

                const $tr = $(this);
                let frontFdName2inputPropMap = {};
                let calcDataMap = {};
                switch (type) {
                    case 'substrates':
                        trData = {
                            'inchi': $(this).find('[property=inchi]').val(),
                            'smiles': $(this).find('[property=smiles]').val(),
                            'text': $(this).find('[property=text]').val(),
                            'name': $(this).find('[property=name]').val(),
                            'batch_num': $(this).find('[property=batch_num]').val(),
                            'eq': $(this).find('[property=equivalent]').val(),
                            'eq_is_input': $(this).find('[property=equivalent]').attr('isInput'),
                            'n': $(this).find('[property=nb]').val(),
                            'n_is_input': $(this).find('[property=nb]').attr('isInput'),
                            'mw': $(this).find('[property=molweight]').val(),
                            'mw_is_input': $(this).find('[property=molweight]').attr('isInput'),
                            'mass': $(this).find('[property=mass]').val(),
                            'mass_is_input': $(this).find('[property=mass]').attr('isInput'),
                            'd': $(this).find('[property=dengsity]').val(),
                            'd_is_input': $(this).find('[property=dengsity]').attr('isInput'),
                            'v': $(this).find('[property=volume]').val(),
                            'v_is_input': $(this).find('[property=volume]').attr('isInput'),
                            'c': $(this).find('[property=temperature]').val(),
                            'c_is_input': $(this).find('[property=temperature]').attr('isInput'),
                            'purity': $(this).find('[property=pressure]').val(),
                            'purity_is_input': $(this).find('[property=pressure]').attr('isInput'),
                            'comment': $(this).find('[property=comment]').val(),
                            'is_base': $(this).find('[property="is_base"]')[0] && $(this).find('[property="is_base"]')[0].checked? 1:0, // add by hkk 2019/3/15
                            'user_input_fields': getUserInputString(this),
                            'cas': $(this).find('[property=cas]').val(), // add by hkk 2019/9/17
                            'wms_product_id': $(this).find('.wms_product_id').val(),
                            'wms_batch_id': $(this).find('.wms_batch_id').val(),
                            'wms_sync_status': $(this).find('.wms_sync_status').val(),
                            'salt': $(this).find('[property=salt]').val(),
                            'salt_eq': $(this).find('[property=salt_eq]').val(), //add by wy 2023/3/2
                        };

                        frontFdName2inputPropMap = {
                            'eq': materielFn.valueFdName2InputPropName.eq,
                            'n': materielFn.valueFdName2InputPropName.n,
                            'mw': materielFn.valueFdName2InputPropName.mw,
                            'mass': materielFn.valueFdName2InputPropName.mass,
                            'd': materielFn.valueFdName2InputPropName.d,
                            'v': materielFn.valueFdName2InputPropName.v,
                            'c': materielFn.valueFdName2InputPropName.c,
                        }
                        calcDataMap = materielFn.getCalValAttrVal({
                            $tr: $tr,
                            frontFdName2inputPropAttr: frontFdName2inputPropMap,
                        });
                        trData = Object.assign(trData, calcDataMap);
                        // add by hkk 2019/9/9 增加自定义的列缓存
                        $(this).find('.addedColumnValue').each(function (index, item) {
                            var dataName = $(item).parent().attr('data-name');
                            var dataValue = item.value;
                            trData[dataName] = dataValue;
                        })
                        cacheData.reactants.push(trData);
                        break;
                    case 'catalysts':
                        trData = {
                            'inchi': $(this).find('[property=inchi]').val(),
                            'smiles': $(this).find('[property=smiles]').val(),
                            'text': $(this).find('[property=text]').val(),
                            'name': $(this).find('[property=name]').val(),
                            'batch_num': $(this).find('[property=batch_num]').val(),
                            'eq': $(this).find('[property=equivalent]').val(),
                            'eq_is_input': $(this).find('[property=equivalent]').attr('isInput'),
                            'n': $(this).find('[property=nb]').val(),
                            'n_is_input': $(this).find('[property=nb]').attr('isInput'),
                            'mw': $(this).find('[property=molweight]').val(),
                            'mw_is_input': $(this).find('[property=molweight]').attr('isInput'),
                            'mass': $(this).find('[property=mass]').val(),
                            'mass_is_input': $(this).find('[property=mass]').attr('isInput'),
                            'd': $(this).find('[property=dengsity]').val(),
                            'd_is_input': $(this).find('[property=dengsity]').attr('isInput'),
                            'v': $(this).find('[property=volume]').val(),
                            'v_is_input': $(this).find('[property=volume]').attr('isInput'),
                            'c': $(this).find('[property=temperature]').val(),
                            'c_is_input': $(this).find('[property=temperature]').attr('isInput'),
                            'purity': $(this).find('[property=pressure]').val(),
                            'purity_is_input': $(this).find('[property=pressure]').attr('isInput'),
                            'comment': $(this).find('[property=comment]').val(),
                            'is_base': $(this).find('[property="is_base"]')[0] && $(this).find('[property="is_base"]')[0].checked? 1:0, // add by hkk 2019/3/15
                            'user_input_fields': getUserInputString(this),
                            'cas': $(this).find('[property=cas]').val(), // add by hkk 2019/9/17
                            'wms_product_id': $(this).find('.wms_product_id').val(),
                            'wms_batch_id': $(this).find('.wms_batch_id').val(),
                            'wms_sync_status': $(this).find('.wms_sync_status').val(),
                            'salt': $(this).find('[property=salt]').val(),
                            'salt_eq': $(this).find('[property=salt_eq]').val(), //add by wy 2023/3/2
                        };

                        frontFdName2inputPropMap = {
                            'eq': materielFn.valueFdName2InputPropName.eq,
                            'n': materielFn.valueFdName2InputPropName.n,
                            'mw': materielFn.valueFdName2InputPropName.mw,
                            'mass': materielFn.valueFdName2InputPropName.mass,
                            'd': materielFn.valueFdName2InputPropName.d,
                            'v': materielFn.valueFdName2InputPropName.v,
                            'c': materielFn.valueFdName2InputPropName.c,
                        }
                        calcDataMap = materielFn.getCalValAttrVal({
                            $tr: $tr,
                            frontFdName2inputPropAttr: frontFdName2inputPropMap,
                        });
                        trData = Object.assign(trData, calcDataMap);

                        // add by hkk 2019/9/9 增加自定义的列缓存
                        $(this).find('.addedColumnValue').each(function (index, item) {
                            var dataName = $(item).parent().attr('data-name');
                            var dataValue = item.value;
                            trData[dataName] = dataValue;
                        })

                        cacheData.reagents.push(trData);
                        break;
                    case 'product':
                        trData = {
                            'inchi': $(this).find('[property=inchi]').val(),
                            'smiles': $(this).find('[property=smiles]').val(),
                            'text': $(this).find('[property=text]').val(),
                            'name': $(this).find('[property=name]').val(),
                            'batch_num': $(this).find('[property=batch_num]').val(),
                            'sample_id': $(this).find('[property=sample_id]').val(),
                            'cms_code': $(this).find('[property=cms_code]').val(), // add by hkk 2021/7/23
                            'cms_id': $(this).find('[property=cms_id]').val(), // add by hkk 2021/7/23
                            'barcode': $(this).find('[property=barcode]').val(),
                            'n': $(this).find('[property=nb]').val(),
                            'n_is_input': $(this).find('[property=nb]').attr('isInput'),
                            'salt': $(this).find('[property=salt]').val(),
                            'salt_eq': $(this).find('[property=salt_eq]').val(),
                            'mw': $(this).find('[property=molweight]').val(),
                            'mw_is_input': $(this).find('[property=molweight]').attr('isInput'),
                            'theo': $(this).find('[property=theo]').val(),
                            'theo_is_input': $(this).find('[property=theo]').attr('isInput'),
                            'mass': $(this).find('[property=mass]').val(),
                            'mass_is_input': $(this).find('[property=mass]').attr('isInput'),
                            'yield': $(this).find('[property=yield]').val(),
                            'yield_is_input': $(this).find('[property=yield]').attr('isInput'),
                            'purity': $(this).find('[property=purity]').val(),
                            'purity_is_input': $(this).find('[property=purity]').attr('isInput'),
                            'comment': $(this).find('[property=comment]').val(),
                            'user_input_fields': getUserInputString(this)// add by hkk 2019/3/15
                        };

                        frontFdName2inputPropMap = {
                            'n': materielFn.valueFdName2InputPropName.n,
                            'theo': materielFn.valueFdName2InputPropName.theo,
                            'mw': materielFn.valueFdName2InputPropName.mw,
                            'mass': materielFn.valueFdName2InputPropName.mass,
                            'yield': materielFn.valueFdName2InputPropName.yield,
                            'purity': materielFn.valueFdName2InputPropName.purity,
                        }
                        calcDataMap = materielFn.getCalValAttrVal({
                            $tr: $tr,
                            frontFdName2inputPropAttr: frontFdName2inputPropMap,
                        });
                        trData = Object.assign(trData, calcDataMap);

                        // add by hkk 2019/9/9 增加自定义的列缓存
                        $(this).find('.addedColumnValue').each(function (index, item) {
                            var dataName = $(item).parent().attr('data-name');
                            var dataValue = item.value;
                            trData[dataName] = dataValue;
                        });

                        cacheData.products.push(trData);
                        break;
                    case 'solvent':
                        trData = {
                            'inchi': $(this).find('[property=inchi]').val(),
                            'smiles': $(this).find('[property=smiles]').val(),
                            'text': $(this).find('[property=text]').val(),
                            'name': $(this).find('[property=name]').val(),
                            'batch_num': $(this).find('[property=batch_num]').val(),
                            'ratio': $(this).find('[property=ratio]').val(),
                            'v': $(this).find('[property=volume]').val(),
                            'v_is_input': $(this).find('[property=volume]').attr('isInput'),
                            'mass': $(this).find('[property=mass]').val(),
                            'mass_is_input': $(this).find('[property=mass]').attr('isInput'),
                            'd': $(this).find('[property=dengsity]').val(),
                            'd_is_input': $(this).find('[property=dengsity]').attr('isInput'),
                            'bp': $(this).find('[property=bp]').val(),
                            'comment': $(this).find('[property=comment]').val(),
                            'user_input_fields': getUserInputString(this),// add by hkk 2019/3/15
                            'cas': $(this).find('[property=cas]').val(), // add by hkk 2019/9/17
                            'wms_product_id': $(this).find('.wms_product_id').val(),
                            'wms_batch_id': $(this).find('.wms_batch_id').val(),
                            'wms_sync_status': $(this).find('.wms_sync_status').val(),
                            'salt': $(this).find('[property=salt]').val(),
                            'salt_eq': $(this).find('[property=salt_eq]').val(), //add by wy 2023/3/2
                        };

                        frontFdName2inputPropMap = {
                            'v': materielFn.valueFdName2InputPropName.v,
                            'mass': materielFn.valueFdName2InputPropName.mass,
                            'd': materielFn.valueFdName2InputPropName.d,
                        }
                        calcDataMap = materielFn.getCalValAttrVal({
                            $tr: $tr,
                            frontFdName2inputPropAttr: frontFdName2inputPropMap,
                        });
                        trData = Object.assign(trData, calcDataMap);

                        // add by hkk 2019/9/9 增加自定义的列缓存
                        $(this).find('.addedColumnValue').each(function (index, item) {
                            var dataName = $(item).parent().attr('data-name');
                            var dataValue = item.value;
                            trData[dataName] = dataValue;
                        });

                        cacheData.solvents.push(trData);
                        break;
                    case 'condition':
                        trData = {
                            'inchi': $(this).find('[property=inchi]').val(),
                            'smiles': $(this).find('[property=smiles]').val(),
                            'text': $(this).find('[property=text]').val(),
                            'name': $(this).find('[property=name]').val(),
                            'temperature': $(this).find('[property=temperature]').val(),
                            'rt': $(this).find('[property=rt]').val(),
                            'pressure': $(this).find('[property=pressure]').val(),
                            'pg': $(this).find('[property=pg]').val(),
                            'heating': $(this).find('[property=heating]').val(),
                            'comment': $(this).find('[property=comment]').val()
                        };

                        // add by hkk 2019/9/9 增加自定义的列缓存
                        $(this).find('.addedColumnValue').each(function (index, item) {
                            var dataName = $(item).parent().attr('data-name');
                            var dataValue = item.value;
                            trData[dataName] = dataValue;
                        });

                        cacheData.conditions.push(trData);
                        break;
                }
            });
            return cacheData;
        },

        showMateriel: function (callback) {
            if (!$('.exp_conetnt.active .chendraw iframe')[0] || !$('.exp_conetnt.active .chendraw iframe')[0].contentWindow || !$('.exp_conetnt.active .chendraw iframe')[0].contentWindow.indraw) {
                return;
            }

            /**
             * 函数返回之前, 需要触发一些事件
             */
            const beforeReturn = () => {
                $('body').trigger('showMateriel');
            }

            const expId = $('.exp_conetnt.active #exp_id').val() || '0';

            var indraw = $('.exp_conetnt.active .chendraw iframe')[0].contentWindow.indraw;
            const inDrawWindow = $('.exp_conetnt.active .chendraw iframe')[0].contentWindow;
            const rdkit = inDrawWindow.RDKit;

            // hkk 2019/6/10 解析反应式前先关闭未输入完的文本框
            var TextField = $('.exp_conetnt.active .chendraw iframe')[0].contentWindow.TextField; // add by hkk 2019/6/10
            if(TextField.dom.isExist()){
                TextField.spacePointerDown(0, indraw);
                indraw.clearRedrawRequest()
            }

            if (indraw.isSameMol) {
                return;
            }

            $('.exp_conetnt.active .indraw_data').val(indraw.getMultiMol());

            // 解析反应式
            var reactionData = indraw.parseReaction(true, {
                fmtRxn: true,
            });

            // 去除indraw中chem.SmilesSaver.saveMolecule为茂环结构添加的错误的未成对电子标记(例如： C1CCCC1.C1CCCC1.Cl[Zr]Cl |^4:11,24|)
            reactionData = mapParsedRxn(reactionData, (chemStruct, idx) => {
                if (chemStruct.smiles) {
                    chemStruct.smiles = chemStruct.smiles.replace(/,?\s\|\^\d:\d*(,\d*)*\|/g, '');
                }
                return chemStruct;
            });

            //  text as structure 模式
            var textAsStructure = $('.exp_conetnt.active #TextAsStructure').val() == 1;
            if (!textAsStructure) {
                reactionData.reactants = reactionData.reactants.filter(function (item) {
                    return item.smiles;
                });
                reactionData.products = reactionData.products.filter(function (item) {
                    return item.smiles;
                });
            }

            // add by hkk 2023/5/10 校正部分老数据中含有空格问题
            if (reactionData.catalysts) {
                reactionData.catalysts = reactionData.catalysts.map(function (item) {
                    if (item.text) {
                        item.text = item.text.trim();
                    }
                    return item
                })
            }

            /** @type {InMaterialApi} */
            const InMaterial = require('in-material');
            const InMaterialUpdate = require('components/in_material/in_material_update');
            // 获取可能的实验或模板id
            const _experimentOrTemplateId = $('.exp_conetnt.active .module_data_store').attr('data-exp-id');
            const materialDataForUpdate = InMaterial.getMaterialFormatUpdate(_experimentOrTemplateId);
            const oldMaterialDataClone = $.extend({}, materialDataForUpdate, true);
            // 删除旧的表格配置以防止改动了表格配置
            delete materialDataForUpdate.base_data;

            // 检验当前画布上的反应式是否为空, 并且物料表也不为空
            const isRxnEmpty = isParsedRxnEmpty(reactionData);
            const isMaterialEmpty = InMaterialUtils.isMaterialDataEmpty(materialDataForUpdate);
            const materialDataFormatVersion = InMaterialUtils.getMaterialDataFormatVersion(oldMaterialDataClone);
            // 物料表数据格式是否是老版格式
            const isMaterialDataFormatV0 = materialDataFormatVersion === InMaterialUtils.MATERIAL_DATA_FORMAT_VERSION.VERSION_0;

            //<editor-fold desc="检测反应式是否发生变化, 如果反应式没有变化, 不发起请求更新物料表">
            const rxnFingerPrint = indraw.getRxnFingerprint(reactionData);
            const drawId = indraw.rdkitLoadSignal.drawId;
            const rxnFingerprintStore = new RxnFingerprintStore();
            const isRxnChanged = rxnFingerprintStore.isRxnChanged(drawId, rxnFingerPrint, {
                afterCompare: rxnFingerprintStore.updateFingerprint.bind(rxnFingerprintStore),
            });

            // 画布上反应式不为空 && 物料表数据为空 视为物料表由于后端异常没有和画布同步
            const isMaterialSyncedWithParsedRxn = !isRxnEmpty && !isMaterialEmpty;
            // 反应式没有改变, 但是物料数据没有同步画布上的反应式, 需要重新同步
            // bug#13622: 物料表的数据为老数据且没有更新为新版的数据, 仍然需要触发一次同步操作
            if (!isRxnChanged && isMaterialSyncedWithParsedRxn && !isMaterialDataFormatV0) {
                return beforeReturn();
            }
            //</editor-fold>

            //<editor-fold desc="画布为空,清空物料表">
            if (isRxnEmpty) {
                const oldMaterialBaseData = InMaterial.getBaseData(_experimentOrTemplateId);
                if (null == oldMaterialBaseData) return beforeReturn();
                InMaterial.updateMaterial(_experimentOrTemplateId, {
                    base_data: oldMaterialBaseData,
                });
                // 清空了物料表, 同步清空文本编辑中的物料信息
                InMaterialUpdate.dealDeleteRows(oldMaterialDataClone, materialDataForUpdate); // 处理编辑器中删掉的物料
                // 这里不return, 交给下面画布为空的老代码进行其他处理, 之后return
            }
            //</editor-fold>

            // 画布为空
            if (reactionData.reactants.length == 0 && reactionData.catalysts.length == 0 && reactionData.solvents.length == 0 && reactionData.products.length == 0) {
                var toRemoveTrs = $(".exp_conetnt.active .chendraw_data input.value_for_title[isinput=0]").closest('tr');
                if (toRemoveTrs.length > 0) {
                    window.noLoadTip = false;
                    toRemoveTrs.remove();
                }

                $('.exp_conetnt.active .details_tr').remove();
                $('.exp_conetnt.active .all_smiles').val('');

                $('body').trigger('showMateriel');

                // 删除物料完成后, 需要同步删除文本编辑模块中引用的物料数据
                 const InMaterialUpdate = require('components/in_material/in_material_update');
                 InMaterialUpdate.deaAllEditorRows();

                return;
            }

            // 合并smiles和名称
            var typeArr = ['reactants', 'catalysts', 'solvents', 'products'];
            var smilesArr = [];
            var nameArr = [];
            var allSmiles = '';
            for (var i = 0; i < typeArr.length; i++) {
                if (i == 3) {
                    if (allSmiles.substr(allSmiles.length - 1, 1) == '.') {
                        allSmiles = allSmiles.substr(0, allSmiles.length - 1);
                    }
                    allSmiles += '>>';
                }
                for (var j = 0; j < reactionData[typeArr[i]].length; j++) {
                    if (reactionData[typeArr[i]][j].smiles) {
                        allSmiles += reactionData[typeArr[i]][j].smiles + '.';
                        smilesArr.push(reactionData[typeArr[i]][j].smiles);
                    } else if (reactionData[typeArr[i]][j].text) {
                        nameArr.push(reactionData[typeArr[i]][j].text);
                    }
                }
            }
            var materialData = {
                smiles_arr: smilesArr,
                name_arr: nameArr
            }
            if (allSmiles.substr(allSmiles.length - 1, 1) == '.') {
                allSmiles = allSmiles.substr(0, allSmiles.length - 1);
            }
            $('.exp_conetnt.active .all_smiles').val(allSmiles);

            var indrawModuleId = $('.exp_conetnt.active .modul_line.chendraw').attr('id');
            // var expId = $('.exp_conetnt.active #exp_id').val() || '0';

            // 压缩数据
            var pako = require('pako1/pako');
            var zipData = pako.gzip(JSON.stringify(materialData), {to: 'string', level: 6});
            zipData = btoa(zipData);

            /* 通过synchronizing防止重复请求可能存在问题：如果synchronizing没有正常恢复为false，就会一直无法提交
            // 判断是否同步中，防止重复请求
            if (this.synchronizing) {
                return;
            }
            this.synchronizing = true;
            */

            // 根据SMILES获取化合物信息
            $.ajaxFn({
                url: ELN_URL + '?r=chem/materiel-data',
                data: {
                    exp_id: expId,
                    module_id: indrawModuleId,
                    zip_data: zipData
                },
                success: function (res) {
                    materielFn.synchronizing = false;
                    var $indrawModule = $('.modul_line.chendraw#' + indrawModuleId);
                    if ($indrawModule.length !== 1) {
                        return;
                    }

                    if (res.status == 1) {
                        var smilesData = res.data;

                        //! bug#34802,按物料表名称设置显示物料名称
                        let iupac_setting = res.data.iupac_lang;
                        if ($('iframe', $indrawModule)[0]) {
                            $('iframe', $indrawModule)[0].contentWindow.indraw.isSameMol = true;
                        }

                        const InMaterialUpdate = require('components/in_material/in_material_update');
                        const iupacLang = iupac_setting === 'cn_iupac'
                            ? InMaterialUpdate.LangType.ZH
                            : InMaterialUpdate.LangType.EN;

                        const newRxnData = reactionData;
                        // 更新chemService的数据: molweight...
                        const updatedChemServiceData = updateChemServiceData(
                            smilesArr,
                            smilesData,
                            {rdkit},
                        );
                        const updatedInMaterialData = InMaterialUpdate.getUpdatedMaterialRxnData({
                            expId: _experimentOrTemplateId,
                            langType: iupacLang,
                            oldMaterialMatrix: materialDataForUpdate,
                            newRxn: newRxnData,
                            chemServiceData: updatedChemServiceData,
                        });
                        InMaterial.updateMaterial(_experimentOrTemplateId, updatedInMaterialData);

                        InMaterialUpdate.dealDeleteRows(materialDataForUpdate, updatedInMaterialData); // 处理编辑器中删掉的物料


                        // 缓存当前表格数据
                        var cacheData = materielFn.cacheData();


                        // 清空表格
                        $('tr.tr_:not(.condition)', $indrawModule).remove();

                        // 生成新的表格
                        var newMaterials = { // 用于记录新增的物料，没有命中缓存 hkk2019/4/23
                            reactants: [],
                            catalysts: [],
                            products: [],
                        };

                        for (var i = 0; i < reactionData.reactants.length; i++) {
                            var lineData = reactionData.reactants[i];
                            lineData.name = '';
                            if (lineData.smiles && smilesData[lineData.smiles]) {
                                lineData.inchi = smilesData[lineData.smiles].inchi;
                                //modified by wy 2023/3/17 判断显示化合物的中/英文名
                                if (iupac_setting === 'cn_iupac') {
                                    lineData.name = smilesData[lineData.smiles].cn_iupac ;
                                } else {
                                    lineData.name = smilesData[lineData.smiles].name;
                                }

                                if (lineData.helmString) { // add by hkk 2023/3/24 HELM代替名称
                                    lineData.name = lineData.helmString
                                }

                                lineData.mw = smilesData[lineData.smiles].weight;
                                lineData.d = smilesData[lineData.smiles].density;
                                lineData.cas = smilesData[lineData.smiles].cas;

                                lineData.cLogP = smilesData[lineData.smiles].cLogP; // add by hkk 2019/9/17
                                lineData.tPSA = smilesData[lineData.smiles].tPSA;  // add by hkk 2019/9/17
                            }
                            if (lineData.text) {
                                lineData.name = lineData.text;
                                lineData.display_structure = 0;
                                if (smilesData[lineData.text]) {
                                    lineData.mw = smilesData[lineData.text].weight;
                                    lineData.d = smilesData[lineData.text].density;
                                    lineData.cas = smilesData[lineData.text].cas;
                                    lineData.cLogP = smilesData[lineData.text].cLogP;
                                    lineData.tPSA = smilesData[lineData.text].tPSA;
                                }
                            }

                            // 在缓存数据中查找
                            var isNewReactants = true;
                            for (var j = 0; j < cacheData.reactants.length; j++) {
                                var item = cacheData.reactants[j];
                                // 命中缓存数据 /// bug#1241,inchi不相同一定是不同的化学物质,防止由于化合物名称可能是空字符串而产生匹配到错误的缓存数据. add dx.
                                /// bug#1309, 当画布上的物质无法被解析为smiles时,才通过比较物质的文本内容进行比较,且不能为空字符串或空值. add dx.
                                if (item.smiles === lineData.smiles || item.inchi === lineData.inchi
                                    || $.strEqualNotEmpty(item.name, lineData.name)
                                ) {
                                    isNewReactants = false
                                    lineData = $.extend(lineData, item);
                                    cacheData.reactants.splice(j, 1);
                                    break;
                                }
                            }
                            if (isNewReactants) {
                                newMaterials.reactants.push({index: i + 1})  //存储需计算的新物质
                                // bug#5655, 从后端请求得到的新数据, 将其视作计算的数据, 为数据对象添加表示计算值的属性.
                                lineData = materielFn.fmtNewRowDataCalcFdVal(lineData);
                            }

                            $('tr.substrates', $indrawModule).last().after(createChemHTML('substrates', expId, i + 1, lineData));

                            //给手动输入加class user add by hkk 2019/3/18
                            addUserInputClass(lineData, 'substrates', i + 1);


                            // add by hkk 2023/6/10 修复刷新后反应物盐型消失问题
                            $('select[name="substrates_salt"]', $indrawModule).each(function () {
                                $(this).val($(this).attr('data-salt'));
                            });
                        }

                        for (var i = 0; i < reactionData.catalysts.length; i++) {
                            var lineData = reactionData.catalysts[i];
                            lineData.name = '';
                            if (lineData.smiles && smilesData[lineData.smiles]) {
                                lineData.inchi = smilesData[lineData.smiles].inchi;
                                //modified by wy 2023/3/17 判断显示化合物的中/英文名
                                if (iupac_setting === 'cn_iupac') {
                                    lineData.name = smilesData[lineData.smiles].cn_iupac;
                                } else {
                                    lineData.name = smilesData[lineData.smiles].name;
                                }

                                if (lineData.helmString) { // add by hkk 2023/3/24 HELM代替名称
                                    lineData.name = lineData.helmString
                                }
                                lineData.mw = smilesData[lineData.smiles].weight;
                                lineData.d = smilesData[lineData.smiles].density;
                                lineData.cas = smilesData[lineData.smiles].cas;

                                lineData.cLogP = smilesData[lineData.smiles].cLogP; // add by hkk 2019/9/17
                                lineData.tPSA = smilesData[lineData.smiles].tPSA;  // add by hkk 2019/9/17
                            }
                            if (lineData.text) {
                                lineData.name = lineData.text;
                                lineData.display_structure = 0;
                                if (smilesData[lineData.text]) {
                                    lineData.mw = smilesData[lineData.text].weight;
                                    lineData.d = smilesData[lineData.text].density;
                                    lineData.cas = smilesData[lineData.text].cas;
                                    lineData.cLogP = smilesData[lineData.text].cLogP;
                                    lineData.tPSA = smilesData[lineData.text].tPSA;
                                }
                            }
                            // 在缓存数据中查找
                            var isNewReagents = true;
                            for (var j = 0; j < cacheData.reagents.length; j++) {
                                var item = cacheData.reagents[j];
                                // 命中缓存数据 /// bug#1241,inchi不相同一定是不同的化学物质,以防止由于化合物名称可能是空字符串而产生匹配到错误的缓存数据的问题. add dx.
                                /// bug#1309, 当画布上的物质无法被解析为smiles时,才通过比较物质的文本内容进行比较,且不能为空字符串或空值. add dx.
                                if (item.smiles === lineData.smiles || item.inchi === lineData.inchi
                                    || $.strEqualNotEmpty(item.name, lineData.name)
                                ) {
                                    isNewReagents = false;
                                    lineData = $.extend(lineData, item);
                                    cacheData.reagents.splice(j, 1);
                                    break;
                                }
                            }
                            if (isNewReagents) {
                                newMaterials.catalysts.push({index: i + 1})  //存储需计算的新物质
                                // bug#5655, 从后端请求得到的新数据, 将其视作计算的数据, 为数据对象添加表示计算值的属性.
                                lineData = materielFn.fmtNewRowDataCalcFdVal(lineData);
                            }
                            $('tr.catalysts', $indrawModule).last().after(createChemHTML('catalysts', expId, i + 1, lineData));

                            //给手动输入加class user add by hkk 2019/3/18
                            addUserInputClass(lineData, 'catalysts', i + 1);
                        }

                        var solventIndex = 0;
                        for (var i = 0; i < reactionData.solvents.length; i++) {
                            var lineData = reactionData.solvents[i];
                            lineData.name = '';
                            if (!lineData.smiles && !lineData.text) {
                                continue;
                            }

                            // 判断否是为反应条件，如果text中包含：℃, min, atm, 分号等则认为是反应条件
                            var tempReg = /℃/; // 温度正则
                            var rtReg = /min|hr|overnight|over\sthe\sweekend/i; // 反应时间正则
                            var pReg = /atm|Mpa|psi|bar/i;  // 压强正则
                            var semicolonReg = /;|；/;  // 分号正则
                            var isCondition = lineData.text && (
                                tempReg.test(lineData.text) || rtReg.test(lineData.text)
                                || pReg.test(lineData.text) || semicolonReg.test(lineData.text)
                            );
                            if (isCondition) {
                                continue;
                            }

                            solventIndex++;
                            if (lineData.smiles && smilesData[lineData.smiles]) {
                                var sd = smilesData[lineData.smiles];
                                lineData.inchi = sd.inchi;
                                //modified by wy 2023/3/17 判断显示化合物的中/英文名
                                if (iupac_setting === 'cn_iupac') {
                                    lineData.name = smilesData[lineData.smiles].cn_iupac;
                                } else {
                                    lineData.name = smilesData[lineData.smiles].name;
                                }
                                if (lineData.helmString) { // add by hkk 2023/3/24 HELM代替名称
                                    lineData.name = lineData.helmString
                                }

                                lineData.mw = sd.weight;
                                lineData.d = sd.density;
                                lineData.cas = sd.cas;
                                lineData.bp = sd.bp;
                                lineData.cLogP = sd.cLogP; // add by hkk 2019/9/17
                                lineData.tPSA = sd.tPSA; // add by hkk 2019/9/17
                            }

                            if (lineData.text) {
                                lineData.name = lineData.text;
                                lineData.display_structure = 0;
                                if (smilesData[lineData.text]) {
                                    lineData.mw = smilesData[lineData.text].weight;
                                    lineData.d = smilesData[lineData.text].density;
                                    lineData.cas = smilesData[lineData.text].cas;
                                    lineData.cLogP = smilesData[lineData.text].cLogP;
                                    lineData.tPSA = smilesData[lineData.text].tPSA;
                                }
                            }

                            // 在缓存数据中查找
                            for (var j = 0; j < cacheData.solvents.length; j++) {
                                var item = cacheData.solvents[j];
                                // 命中缓存数据 /// bug#1241,inchi不相同一定是不同的化学物质,以防止由于化合物名称可能是空字符串而产生匹配到错误的缓存数据的问题. add dx.
                                /// bug#1309, 当画布上的物质无法被解析为smiles时,才通过比较物质的文本内容进行比较,且不能为空字符串或空值. add dx.
                                if (item.smiles === lineData.smiles || item.inchi === lineData.inchi
                                    || $.strEqualNotEmpty(item.name, lineData.name)
                                ) {
                                    lineData = $.extend(lineData, item);
                                    cacheData.solvents.splice(j, 1);
                                    break;
                                }
                            }
                            $('tr.solvent', $indrawModule).last().after(createChemHTML('solvent', expId, solventIndex, lineData));

                            //给手动输入加class user add by hkk 2019/3/18
                            addUserInputClass(lineData, 'solvent', i + 1);

                        }

                        for (var i = 0; i < reactionData.products.length; i++) {
                            var lineData = reactionData.products[i];
                            lineData.name = '';
                            // 产物的Batch Num自动生成，规则为当前实验编号 + '-P' + 序号
                            lineData.batch_num = $('.exp_conetnt.active .info_part .book_name').html() + '-P' + (i + 1);
                            if (lineData.smiles && smilesData[lineData.smiles]) {
                                lineData.inchi = smilesData[lineData.smiles].inchi;
                                //modified by wy 2023/3/17 判断显示化合物的中/英文名
                                if (iupac_setting == 'cn_iupac') {
                                    lineData.name = smilesData[lineData.smiles].cn_iupac;
                                } else {
                                    lineData.name = smilesData[lineData.smiles].name;
                                }
                                if (lineData.helmString) { // add by hkk 2023/3/24 HELM代替名称
                                    lineData.name = lineData.helmString
                                }

                                lineData.mw = smilesData[lineData.smiles].weight;
                                lineData.d = smilesData[lineData.smiles].density;
                                lineData.cas = smilesData[lineData.smiles].cas;

                                lineData.cLogP = smilesData[lineData.smiles].cLogP; // add by hkk 2019/9/17
                                lineData.tPSA = smilesData[lineData.smiles].tPSA;  // add by hkk 2019/9/17
                            }
                            if (lineData.text) {
                                lineData.name = lineData.text;
                                lineData.display_structure = 0;
                                if (smilesData[lineData.text]) {
                                    lineData.mw = smilesData[lineData.text].weight;
                                    lineData.d = smilesData[lineData.text].density;
                                    lineData.cas = smilesData[lineData.text].cas;
                                    lineData.cLogP = smilesData[lineData.text].cLogP;
                                    lineData.tPSA = smilesData[lineData.text].tPSA;
                                }
                            }
                            // 在缓存数据中查找
                            var isNewProduct = true;
                            for (var j = 0; j < cacheData.products.length; j++) {
                                var item = cacheData.products[j];
                                // 命中缓存数据 /// bug#1241,inchi不相同一定是不同的化学物质,以防止由于化合物名称可能是空字符串而产生匹配到错误的缓存数据的问题. add dx.
                                /// bug#1309, 当画布上的物质无法被解析为smiles时,才通过比较物质的文本内容进行比较,且不能为空字符串或空值. add dx.
                                if (item.smiles === lineData.smiles || item.inchi === lineData.inchi
                                    || $.strEqualNotEmpty(item.name, lineData.name)
                                ) {
                                    isNewProduct = false;
                                    lineData = $.extend(lineData, item);
                                    cacheData.products.splice(j, 1);
                                    break;
                                }
                            }
                            if (isNewProduct) {
                                newMaterials.products.push({index: i + 1})  //存储需计算的新物质序号
                                // bug#5655, 从后端请求得到的新数据, 将其视作计算的数据, 为数据对象添加表示计算值的属性.
                                lineData = materielFn.fmtNewRowDataCalcFdVal(lineData);
                            }
                            $('tr.product', $indrawModule).last().after(createChemHTML('product', expId, i + 1, lineData));

                            //给手动输入加class user add by hkk 2019/3/18
                            addUserInputClass(lineData, 'product', i + 1);

                            $('select[name="product_salt"]', $indrawModule).each(function () {
                                $(this).val($(this).attr('data-salt'));
                            });
                        }

                        /*for (var i = 0; i < cacheData.solvents.length; i++) {
                            lineData = cacheData.solvents[i];
                            if (lineData.smiles == '') {
                                var newIndex = $('.solvent_' + expId).length + 1;
                                $('tr.solvent', $indrawModule).last().after(createChemHTML('solvent', expId, newIndex, lineData));

                                //给手动输入加class user add by hkk 2019/3/18
                                addUserInputClass(lineData, 'solvent', i + 1);
                            }
                        }*/

                        /*for(var i = 0; i<cacheData.conditions.length; i++) {
                         lineData = cacheData.conditions[i];
                         $('tr.condition').last().after(createChemHTML('condition', expId, i+1, lineData));
                         }*/

                        //add by hkk 2019/4/23 新加的物料需要计算物料表 反应物产物和催化剂

                        var selector;
                        var $dom;
                        for (var i = 0; i < newMaterials.reactants.length; i++) {
                            selector = '#substrates_equivalent_' + newMaterials.reactants[i].index;
                            $dom = $(selector, $indrawModule);
                            $dom.trigger('change');
                            $dom.removeClass('userInputData');
                        }
                        for (var i = 0; i < newMaterials.catalysts.length; i++) {
                            selector = '#catalysts_equivalent_' + newMaterials.catalysts[i].index;
                            $dom = $(selector, $indrawModule);
                            $dom.trigger('change');
                            $dom.removeClass('userInputData');
                        }
                        for (var i = 0; i < newMaterials.products.length; i++) {
                            selector = '#product_molweight_' + newMaterials.products[i].index;
                            $dom = $(selector, $indrawModule);
                            $dom.trigger('change');
                            $dom.removeClass('userInputData');
                        }

                        createDetailHtml();

                        window.noLoadTip = false;

                        $('body').trigger('showMateriel');
                    }
                },
                errFn: function () {
                    materielFn.synchronizing = false;
                }
            }, null, false);
            indraw.addReactionSign(textAsStructure);
            return;
        }
    };

    //物料解析
    $('body').on('click', function (event) {
        var event = window.event || event;
        var obj = event.srcElement ? event.srcElement : event.target;
        if ($(obj).hasClass('chem_data_add_temp') || $(obj).parents('.choose_additives').length > 0 ||  ($('.exp_conetnt.active #exp_edit').val()!=='1' && $('.exp_conetnt.active .modul_line.chendraw .module-save').length == 0)) { // 关闭，痕迹，审核状态不解析物料add by hkk 2019/3/21
            return;
        }
        //indraw词条页面。不需要执行下面的操作。直接return;
        if ($('.exp_conetnt.active .indraw_word_modal').is(":visible")) {
            return;
        }

        materielFn.showMateriel();
    });

    // inDraw的RDKit加载完成, 解析画布上的反应式并记录到history中
    $('body').on('loaded:inDraw:rdkit', function (e, eventParams) {
        const {inDraw, drawId, expType} = eventParams;
        if (!inDraw) {
            return;
        }

        const parsedReaction = inDraw.parseReaction(true, {
            fmtRxn: true,
        });
        const rxnFingerprint = inDraw.getRxnFingerprint(parsedReaction);
        console.info(rxnFingerprint);
        (new RxnFingerprintStore()).updateFingerprint(drawId, rxnFingerprint);
    });

    var condationSearchAction = {
        init: function () {
            condationSearchAction.doSearchActioSmilesHref();
            condationSearchAction.doSearchInventory();
            condationSearchAction.doSearchSuppliersAll();
            condationSearchAction.doSearchFulltext();
            condationSearchAction.doSubmitChemical();
            condationSearchAction.doSubmitWms();
            condationSearchAction.showMsds();
            condationSearchAction.addSaltType();
            condationSearchAction.addFromInstruments(); // add by hkk 2019/11/6
            condationSearchAction.addFromCms(); // 从CMS添加(Story#973)
        },

        getSmiles: function (thisObj) { // 获取搜索的smiles
            var smiles = thisObj.parents('.name_td').find('.search_smiles').val();
            return smiles;
        },

        getName: function (thisObj) { // 获取搜索的文字
            return thisObj.parents('.name_td').find('.experiment_input_name').val();
        },

        getInventory: function (thisObj) { // 获取库存的id
            return thisObj.attr('inventory');
        },
        //合并后面2种采购商场的搜索
        doSearchSuppliersAll: function () {
            $('body').off('click.search_action_all_href').on('click.search_action_all_href', '.search_action_all_href', function (evt, mtParams) {
                var tr = $(this).parents('.tr_');
                let smiles = condationSearchAction.getSmiles($(this));
                let keywords = condationSearchAction.getName($(this));

                if (mtParams && mtParams.materialRowData) {
                    const _mtRowData = mtParams.materialRowData; // 物料数据
                    smiles = _mtRowData.smiles;
                    keywords = _mtRowData.name;
                }

                if ($.checkVal(smiles, 'noempty')) {
                    smiles = encodeURIComponent(smiles);
                    var url = SHOP_URL + "chem/chem/struct?smiles=" + smiles;
                    window.open(url, "_blank");
                } else if ($.checkVal(keywords, 'noempty')) {
                    var url = SHOP_URL + 'chem/chem/key?&keyword=' + keywords;
                    window.open(url, "_blank");
                } else {
                    $.showAlert(mainLang('name_empty'));
                }
            });
        },

        doSearchActioSmilesHref: function () { // 根据smiles搜索供应商信息
            $('body').off('click.search_action_smiles_href').on('click.search_action_indraw', '.search_action_indraw', function () {
                var tr = $(this).parents('.tr_');
                //ls20171012 为什么这里给自定义的tr的smiles去掉了。
                // if (tr.hasClass('custom_tr')) {
                // 	$.showAlert(mainLang('no_smiles'));
                // } else {
                var smiles = condationSearchAction.getSmiles($(this));
                if ($.checkVal(smiles, 'noempty')) {
                    smiles = encodeURIComponent(smiles);
                    var url = SHOP_URL + "chem/chem/struct?smiles=" + smiles;
                    window.open(url, "_blank");
                } else {
                    $.showAlert(mainLang('no_smiles'));
                }
                // }
            });
        },

        doSearchFulltext: function () { // 搜索基本信息
            $('body').off('click.search_action_name_href').on('click.search_action_name_href', '.search_action_name_href', function () {
                var keywords = condationSearchAction.getName($(this));
                if ($.checkVal(keywords, 'noempty')) {
                    //                    var url = INTEGLE_URL+'search/fulltext?kw='+keywords+'&type=list#grid';
                    var url = SHOP_URL + 'chem/chem/key?&keyword=' + keywords;
                    window.open(url, "_blank");
                } else {
                    $.showAlert(mainLang('name_empty'));
                }

            });
        },

        doSearchInventory: function () {

            // 根据smiles搜索库存 基于原执行流程梳理代码, by tianyang 20230610, 去掉 is_sync 同步设置
            $('body').off('click.add-to-batch-num').on('click.add-to-batch-num', '.add-to-batch-num', function () {
                var productId = $(this).attr('data-product_id');
                var batchId = $(this).attr('data-batch_id');
                var batchNum = $(this).attr('data-val');
                var wmsSyncStatus = $(this).attr('data-wms_sync_status');
                var inventoryId = $(this).attr('data-inventory_id');

                $batchNumInputTr.find('.wms_product_id').val(productId);
                $batchNumInputTr.find('.wms_batch_id').val(batchId);
                $batchNumInputTr.find('.wms_sync_status').val(wmsSyncStatus);
                $batchNumInputTr.find('.wms_inventory_id').val(inventoryId);

                // 10=材料仪器模块 || 13=自定义表格
                if($(this).attr('component_id') == 10 || $(this).attr('component_id') == 13){
                    // 根据批次同步状态动态渲染同步库存图标 jiangdm 2022/3/25
                    $batchNumInputTr.find('.warehouse_sync_ico').remove();
                    $batchNumInputTr.find('input[name="field_data"]').eq(2).removeClass('warehouse_sync_input');
                    if ($(this).attr('component_id') == 13 && wmsSyncStatus == 1) {
                        var amountInput = $batchNumInputTr.find('input[name="field_data"]').eq(2).addClass('warehouse_sync_input');
                        amountInput.after('<span class="warehouse_sync_ico" title="' + mainLang('sync_wms_ico_title') + '"></span>');
                    }

                    // 发请求从后端获取 字段 => 值, 自动填充
                    $.ajaxFn({
                        url: ELN_URL + '?r=chem/get-one-product-info-by-id',
                        data: {
                            'batch_id': batchId
                        },
                        type: 'POST',
                        success: function (res) {
                            if (1 == res.status) {
                                // 数据源 field -> val
                                var sourceFiled2ValMap = res.data.filed2val;
                                var matchedInputIndexArr = autoFillFileds(sourceFiled2ValMap, $batchNumInputTr);

                                // 若第二列未匹配中, 则填入默认项
                                if (!matchedInputIndexArr.includes(1)) {
                                    $batchNumInputTr.find('input:eq(1)').val(batchNum);
                                }
                            }
                        }
                    });
                }

                const materialEventParams = window._TMP_MATERIAL_EVENT_PARAMS_;
                // 1=indraw
                if ($(this).attr('component_id') == 1) {
                    $batchNumInput.val(batchNum);
                    // 根据批次同步状态动态渲染同步库存图标 jiangdm 2022/3/25
                    $batchNumInputTr.find('.warehouse_sync_ico').remove();
                    $batchNumInputTr.find('.materiel_mass_input').removeClass('warehouse_sync_input');
                    $batchNumInputTr.find('.materiel_vol_input').removeClass('warehouse_sync_input');
                    if (wmsSyncStatus == 1) {
                        var amountInput = $batchNumInputTr.find('.materiel_mass_input').addClass('warehouse_sync_input');
                        amountInput.after('<span class="warehouse_sync_ico" title="' + mainLang('sync_wms_ico_title') + '"></span>');
                    } else if (wmsSyncStatus == 2) {
                        var amountInput = $batchNumInputTr.find('.materiel_vol_input').addClass('warehouse_sync_input');
                        amountInput.after('<span class="warehouse_sync_ico" title="' + mainLang('sync_wms_ico_title') + '"></span>');
                    }

                    // 发请求从后端获取 字段 => 值, 自动填充
                    $.ajaxFn({
                        url: ELN_URL + '?r=chem/get-one-product-info-by-id',
                        data: {
                            'batch_id': batchId
                        },
                        type: 'POST',
                        success: function (res) {
                            if (1 == res.status) {
                                // 数据源 field -> val
                                var sourceField2ValMap = res.data.filed2val;

                                // 需要添加到物料信息的批次字段=>值的映射
                                const batchFdKeyMapValue = {
                                    wms_product_id: productId,
                                    wms_batch_id: batchId,
                                    wms_sync_status: wmsSyncStatus,
                                    wms_inventory_id: inventoryId,
                                };
                                // 按照批次的字段名填充物料表中对应的字段
                                materialEventParams?.onConfirmAddBatchInfo({
                                    wmsFdKeyMapVal: batchFdKeyMapValue,
                                    wmsFdNameMapVal: sourceField2ValMap,
                                });

                                // to 要填入的输入框
                                var toTitleDomList = $batchNumInputTr.closest('table').find('tr:first .addedMaterialColumn:visible');
                                var toField2IndexMap = {};
                                // field => index, 重复字段时, 匹配第一次出现的
                                toTitleDomList.each(function (index, item) {
                                    var title = $(item).text().trim();
                                    if (title && typeof toField2IndexMap[title] === 'undefined') {
                                        toField2IndexMap[title] = index;
                                    }
                                });
                                // input - index => value
                                var toFieldValMap = {};
                                for (let field in toField2IndexMap) {
                                    if (typeof sourceField2ValMap[field] !== 'undefined') {
                                        toFieldValMap[toField2IndexMap[field]] = sourceField2ValMap[field];
                                    }
                                }
                                // 填入匹配项
                                var fieldTrDomList = $batchNumInputTr.find('.addedColumnValue:visible');
                                fieldTrDomList.each(function (index, item) {
                                    if (toFieldValMap[index]) {
                                        $(item).val(toFieldValMap[index]);
                                    }
                                });
                            }
                        }
                    });
                }
                $('.pop_modal').modal('hide');
            });

            // 物料表搜索库存
            $('body').off('click.search_inventory_btn').on('click.search_inventory_btn', '.search_inventory_btn', function (event, evtParams) {
                // var smiles = condationSearchAction.getSmiles($(this));
                let smiles = $(this).attr('searchSmiles') || condationSearchAction.getSmiles($(this));
                let name = $(this).parents('td').find('input[property=name]').val();

                if (evtParams?.evtSrc === MATERIAL_EVT_SRC && evtParams?.materialRowData) {
                    const _mtRowData = evtParams.materialRowData; // 物料数据
                    smiles = _mtRowData.smiles;
                    name = _mtRowData.name;
                }

                var relay_id = $(this).closest('.modul_part').find('input.modul_part_id').attr('data-id');
                var temp_relay_id = $(this).parents('.modul_line').attr('temp_relay_id');
                var component_id = $(this).parents('.modul_line').attr('component_id');
                var data = {};
                if (smiles && smiles != '') {
                    data.smiles = smiles;
                } else if (name && name != '') {
                    data.key = name.trim();
                }

                data.relay_id = relay_id;
                data.temp_relay_id = temp_relay_id;
                data.component_id = component_id ? component_id : 1;

                window.$batchNumInput = $(this).parents('tr').find('[property=batch_num]');
                window.$batchNumInputTr = $(this).parents('tr');
                window.$fieldDataInput = $(this).parents('tr').find('[name=field_data]');

                // 如果设置了物料表参数
                if (evtParams?.evtSrc === MATERIAL_EVT_SRC) {
                    // 物料表搜索库存的参数
                    window._TMP_MATERIAL_EVENT_PARAMS_ = evtParams;
                }
                $('.pop_modal .modal-dialog').addClass('modal-lg');
                var html = '<div id="wms-list-wrap" style="margin-bottom: 10px;"></div><div id="wms-result-wrap"></div>';
                var title = mainLang('wms_search_result') + `<span class="tip-box iblock relative" style="vertical-align: middle;">
                        <i class="tip-ico mouse hover_tip" style="top: unset;left: unset;position: relative" data-tip-content="${mainLang('search_inventory_pop_tip')}"></i>
                    </span>`;
                window.wmsResultDialog = $.popContent(html, title, function () {
                }, function () {
                    requestWms(data, false);
                }, false);

                /**
                 * 搜索库存
                 * @param data 搜索条件
                 * @param onlyRefreshTableData 是否仅刷新表格数据
                 */
                window.requestWms = function (data, onlyRefreshTableData) {
                    $.ajaxFn({
                        url: ELN_URL + '?r=chem/search-wms',
                        type: 'POST',
                        data: data,
                        success: function (res) {
                            if (res.status == 1) {
                                if (!onlyRefreshTableData) {
                                    var inventoryId = res.data.inventory_id;
                                    var inventoryName = '';
                                    var inventoryList = res.data.inventory_list;
                                    var selectHtml = '';
                                    selectHtml += '<span class="iblock ml20">' + mainLang('sub_inventory') + '</span>';
                                    selectHtml += '<div class="select-beautify iblock" style="width: 200px;"><span class="block select-text inv"></span>';
                                    selectHtml += '<select class="inventory_select select">';
                                    for (var i = 0; i < inventoryList.length; i++) {
                                        var selected = '';
                                        if (inventoryList[i].id == inventoryId) {
                                            selected = ' selected';
                                            inventoryName = inventoryList[i].inventory_name;
                                        }
                                        selectHtml += '<option value="' + inventoryList[i].id + '" ' + selected + '>' + inventoryList[i].inventory_name + '</option>';
                                    }
                                    selectHtml += '</select></div>';

                                    selectHtml += '<span class="iblock ml20">' + mainLang('wms_inv_filter') + '</span>';

                                    selectHtml += '<div class="select-beautify iblock ml10" style="width: 200px;"><span class="block select-text">' + mainLang('wms_inv_filter_quantity_gt0') + '</span>';
                                    selectHtml += '<select class="quantity_select select">';
                                    selectHtml += '<option value="0">' + mainLang('wms_inv_filter_quantity_gt0') + '</option>';
                                    selectHtml += '<option value="1">' + mainLang('wms_inv_filter_all') + '</option>';
                                    selectHtml += '<option value="2">' + mainLang('wms_inv_filter_quantity_eq0') + '</option>';
                                    selectHtml += '<option value="3">' + mainLang('wms_inv_filter_available_gt0') + '</option>';
                                    selectHtml += '</select></div>';

                                    $('#wms-list-wrap').html(selectHtml);
                                    $('#wms-list-wrap .select-text.inv').html(inventoryName);
                                }

                                var productList = res.data.product_list;
                                var sync_wms = res.data.sync_wms;
                                var sync_wms_len = res.data.sync_wms_length;
                                var component_id = res.data.component_id;
                                var tableHtml = '<table><thead><tr>' +
                                    '<th>' + mainLang('wms_product_number') + '</th>' +
                                    '<th>' + mainLang('wms_product_pic') + '</th>' +
                                    '<th>' + mainLang('wms_product_info') + '</th>' +
                                    '<th>' + mainLang('wms_product_specs') + '</th>' +
                                    '<th>' + mainLang('wms_product_brand') + '</th>' +
                                    '<th>' + mainLang('wms_product_category') + '</th>' +
                                    // '<th>' + mainLang('wms_product_warehouse') + '</th>' +
                                    '<th>' + mainLang('wms_product_area') + '</th>' +
                                    '<th>' + mainLang('wms_product_available_total') + '</th>' +
                                    '<th>' + mainLang('wms_product_available_mass') + '</th>' +
                                    '<th style="min-width: 66px;">' + mainLang('wms_product_action') + '</th>' +
                                    '</tr></thead>';
                                if (productList.length == 0) {
                                    tableHtml += '<tr style="height:80px;"><td colspan="10">' + mainLang('wms_search_no_result') + '</td></tr>';
                                } else {
                                    for (var i = 0; i < productList.length; i++) {
                                        var product = productList[i];
                                        tableHtml += '<tr>';
                                        tableHtml += '<td><span>' + product['product_num'] + '</span></td>';
                                        tableHtml += '<td><img src="' + product['img_url'] + '" width="100"></td>';
                                        var productInfo = HtmlEncode(product['product_name']);
                                        if (product['cas'] && product['cas'] != '') {
                                            productInfo += '</br>' + product['cas'];
                                        }
                                        if (product['ics'] && product['ics'] != '') {
                                            productInfo += '</br>' + product['ics'];
                                        }
                                        if (product['batch_num'] && product['batch_num'] != '') {
                                            productInfo += '</br>' + product['batch_num'];
                                        }
                                        tableHtml += '<td>' + productInfo + '</td>';
                                        tableHtml += '<td>' + product['specs'] + '</td>';
                                        tableHtml += '<td>' + product['brand_id'] + '</td>';
                                        tableHtml += '<td>' + product['category_id'] + '</td>';
                                        // tableHtml += '<td>' + product['warehouse_name'] + '</td>';
                                        tableHtml += '<td>' + product['area_name'] + '</td>';
                                        tableHtml += '<td>' + (product['frame_number'] - product['locked'] - product['ordered']) + '/' + (product['on_hand_val'] - 0) + ' ' + product['unit_name'] + '</td>';
                                        tableHtml += '<td>' + (product['available_mass_val'] >= 0 || product['available_mass'] === 0 ? product['available_mass'] : '') + '</td>';
                                        var selectedProjectId = 0 ;
                                        if($('.project_link .fs-option.selected').length>0){
                                            selectedProjectId=$('.project_link .fs-option.selected').attr('data-value')
                                        }
                                        if(sync_wms_len>0){
                                            tableHtml += '<td><a href="' + INVENTORY_URL + 'home/index?inventory_id='+res.data.inventory_id+'&url=/apply-return/material-inventory&key=' + product['batch_num'] + '&eln_selected_project_id=' + selectedProjectId + '" target="_blank" style="display:inline-block;transform:rotate(180deg);" title="'+ mainLang("wms_add_to_cart") +'"><img src="/image/add_to_cart.svg"></a>';
                                            tableHtml += '<span class="add-to-batch-num" is_sync=1 component_id='+component_id+' ';
                                            tableHtml += 'data-val="' + product['batch_num'] + '" ';
                                            tableHtml += 'data-product_id="' + product['product_id'] + '" ';
                                            tableHtml += 'data-batch_id="' + product['batch_id'] + '" ';
                                            tableHtml += 'data-inventory_id="' + res.data.inventory_id + '" ';
                                            for(var key in sync_wms){
                                                var val = product[sync_wms[key]]?product[sync_wms[key]]:'';
                                                if(key=='field2' && val==''){
                                                    val=product['batch_num'];
                                                }
                                                tableHtml += 'data-'+key+'="'+val+'" ';
                                            }
                                            tableHtml += 'title="'+ mainLang("wms_add_to_batch_num") +'"><img src="/image/import.svg"></span></td>';

                                        }else
                                        {
                                            tableHtml += '<td><a href="' + INVENTORY_URL + 'home/index?inventory_id='+res.data.inventory_id+'&url=/apply-return/material-inventory&key=' + product['batch_num'] + '&eln_selected_project_id=' + selectedProjectId + '" target="_blank" style="display:inline-block;transform:rotate(180deg);" title="'+ mainLang("wms_add_to_cart") +'"><img src="/image/add_to_cart.svg"></a>';
                                            tableHtml += '<span class="add-to-batch-num" is_sync=0 component_id='+component_id+' data-val="' + product['batch_num'] + '" data-product_id="' + product['product_id'] + '" data-batch_id="' + product['batch_id'] + '" data-wms_sync_status="' + product['wms_sync_status'] + '" data-inventory_id="' + res.data.inventory_id + '" title="'+ mainLang("wms_add_to_batch_num") +'"><img src="/image/import.svg"></span></td>';

                                        }
                                        tableHtml += '</tr>';
                                    }
                                }
                                tableHtml += '</table>';
                                $('#wms-result-wrap').html(tableHtml);
                            }
                        },
                        error: function () {
                            console.log('request failed')
                        }
                    }, null, true);
                }

                $('body').off('change.inventory_select').on('change.inventory_select', '.inventory_select', function () {
                    data.inventory_id = $(this).val();
                    data.stock_type = $('#wms-list-wrap .quantity_select').val();
                    requestWms(data, true);
                });

                $('body').off('change.quantity_select').on('change.quantity_select', '.quantity_select', function () {
                    data.inventory_id = $('#wms-list-wrap .inventory_select').val();
                    data.stock_type = $(this).val();
                    requestWms(data, true);
                });
            });
        },

        doSubmitChemical: function () { //提交化合物 modified by hkk 2019/8/7 新的注册化合物用表单方式提交
            $('body').off('click.do_submit_chemical').on('click.do_submit_chemical', '.do_submit_chemical', function (evt, eventParams) {
                // 获取对应化合物详情里是否有文件
                var that = $(this);
                // 获取对应化合物详情的文件
                var currentNum = that.closest('tr').find('.num').text(); // 化合物名标记,类似 P1 P2 R1 R2
                var detailTr = $('.exp_conetnt.active .details_tr').find(".num:contains(" + currentNum +")").closest('tr'); //找到对应化合物详情的tr,
                var filesArray = [];
                detailTr.find('.single_detail_file').each(function(){
                    var allFileData = $.formSerializeFn($(this));
                    var fileData = {
                        dep_path: allFileData.dep_path ? allFileData.dep_path : '',
                        save_name: allFileData.save_name ? allFileData.save_name : '',
                        real_name: allFileData.file_name ? allFileData.file_name : ''
                    };
                    filesArray.push(fileData)
                });
                var filesString = JSON.stringify(filesArray);

                // 获取注册化合物当前行的基本信息
                var tr = that.closest('tr');
                var trData = $.formSerializeFn(tr);

                var project_id = $(".exp_conetnt.active [name=project_id]").val() || '';
                // var dom = $(".my_exp_detial.on");
                // var eln_id = dom ? dom.attr('data-id') || '0' : $('.exp_conetnt.active #exp_id').val();
                var eln_id = $('.exp_conetnt.active #exp_id').val(); // add by hkk 2020/6/19

                var mol = trData.product_mol || '';
                var smiles = that.closest('.name_td').find('.product_smiles').val();
                var batch_no = trData.product_batch_num || '';
                var purity = trData.product_purity || '';
                var mass = trData.product_mass || ''; //默认状态由0改为空
                var mass_unit = $('.exp_conetnt.active [data-type="proMassUnit"]').find('option:selected').text();
                var group_id = $.trim($(".exp_conetnt.active .exp_group_id").val());

                // todo 添加 盐型 数量 条形码 int信息
                var salt_name = trData.product_salt  || '';
                var salt_number = trData.product_salt_eq || '';
                var barcode = trData.product_barcode || '';

                // 如果是物料表的信息, 传入对应的参数
                if (eventParams?.evtSrc === MATERIAL_EVT_SRC && eventParams?.materialRowData) {
                    const _mtRowData = eventParams.materialRowData; // 物料数据
                    console.info('注册化合物到cms', _mtRowData);
                    //<editor-fold desc="获取对应化合物详情的文件">
                    // 物料对应的详情数据
                    const _dtlRowData = eventParams.dtlRowData;
                    // 待注册的产物单位配置
                    const _prdUnitOpts = eventParams.prdUnitOptions;
                    const dtlUploadFiles = null == _dtlRowData?.detail_upload_files
                        ? []
                        : typeof _dtlRowData?.detail_upload_files === 'string'
                            ? JSON.parse(_dtlRowData.detail_upload_files)
                            : _dtlRowData.detail_upload_files;
                    const dtlFileArr = dtlUploadFiles.map((dtlFile) => {
                        return {
                            dep_path : dtlFile.dep_path ?? '',
                            save_name: dtlFile.save_name ?? '',
                            // 物料表内部的文件名字段为real_name, 但是上传文件返回的是file_name, 这里兼容异常数据
                            real_name: dtlFile.real_name ?? dtlFile.file_name ?? '',
                        };
                    });
                    if (dtlFileArr.length > 0) {
                        filesArray.push(...dtlFileArr);
                    }
                    //</editor-fold>
                    filesString = JSON.stringify(dtlFileArr);
                    mol = _mtRowData.molstring ?? '';
                    smiles = _mtRowData.smiles ?? '';
                    batch_no = _mtRowData.batch_num ?? '';
                    purity = _mtRowData.purity ?? '';
                    mass = eventParams.materialRow?.mass?.val_formatted; // 取真实输入值
                    mass_unit = _prdUnitOpts?.mass?.unitLabel ?? '';

                    salt_name = _mtRowData.salt;
                    salt_number = _mtRowData.salt_eq;
                    barcode = _mtRowData.barcode;
                }

                if (!smiles) {
                    $.showAlert(mainLang('material_not_save'));
                    return;
                }

                 // 给indraw模块表单添加隐藏值
                $('.exp_conetnt.active #eln_id_register').val(eln_id);
                $('.exp_conetnt.active #project_id_register').val(project_id);
                $('.exp_conetnt.active #smiles_register').val(encodeURIComponent(smiles));
                $('.exp_conetnt.active #batch_no_register').val(batch_no);
                $('.exp_conetnt.active #group_id_register').val(group_id);
                $('.exp_conetnt.active #purity_register').val(purity);
                $('.exp_conetnt.active #mass_register').val(mass);
                $('.exp_conetnt.active #mass_unit_register').val(mass_unit);
                $('.exp_conetnt.active #mol').val(mol);

                $('.exp_conetnt.active #salt_name_register').val(salt_name);
                $('.exp_conetnt.active #salt_number_register').val(salt_number);
                $('.exp_conetnt.active #barcode_register').val(barcode);

                var expId = $('.exp_conetnt.active #exp_id').val() || '0';
                $.ajaxFn({
                    url: '/?r=chem/submit-to-cms&experiment_id=' + expId,
                    success: function (res) {
                        if (res.status === 1) {
                            var visibleGroupList = res.data.group_list;
                            var routeStr = JSON.stringify(res.data.route_data);
                            $('.exp_conetnt.active #scheme_int_register')[0].innerHTML = routeStr;

                            //从本地读取上次选择的鹰群
                            var lastChooseGroup = localStorage.getItem('last_cms_group');
                            var popHtml = '<div class="info_part mudole_info_part" style="margin:20px 0"><label>' + mainLang('register to group') + '</label><select id="choose_group_to_cms" name="select_group" style="width: 198px;margin-left: 10px;" class="angle_input iblock group_select fs_select_2" name="select_group">';
                            for (var i = 0; i < visibleGroupList.length; i++) {
                                if (lastChooseGroup === null || lastChooseGroup === "") {
                                    if (i == 0) {
                                        popHtml += '<option style="width: 198px" selected value=' + visibleGroupList[i].group_id + '>' + visibleGroupList[i].group_name + '</option>';
                                    } else {
                                        popHtml += '<option style="width: 198px" value=' + visibleGroupList[i].group_id + '>' + visibleGroupList[i].group_name + '</option>';
                                    }
                                } else {
                                    if (visibleGroupList[i].group_id == lastChooseGroup) {
                                        popHtml += '<option style="width: 198px" selected value=' + visibleGroupList[i].group_id + '>' + visibleGroupList[i].group_name + '</option>';
                                    } else {
                                        popHtml += '<option style="width: 198px" value=' + visibleGroupList[i].group_id + '>' + visibleGroupList[i].group_name + '</option>';
                                    }
                                }
                            }
                            popHtml += '</select></div>';
                            if (filesArray.length > 0) {
                                popHtml += '<div><input class="isSyncImportFile" type="checkbox" name="isSyncImportFile"> ' +
                                    '<span>该产物对应的文件同步导入到化合物管理系统(CMS)中</span></div>' +
                                    '<div style="margin: 10px 0">如果选择同步导入，对应的文件会在化合物管理系统(CMS)的"上传文件"——"Files from InELN"中显示(请在CMS系统中预先配置好"上传文件"模块)</div>';
                            }

                            $.popContent(popHtml, '注册到CMS', function () {
                                group_id = $.trim($("#choose_group_to_cms option:selected").val());
                                //将这次选择的鹰群存储到本地
                                localStorage.setItem('last_choose_group',group_id);
                                var $form = $('.exp_conetnt.active #postFrom_register');
                                var formAction = $form.attr('action');
                                $form.attr('action', formAction + '?change_group=1&group_id=' + group_id);
                                $('.exp_conetnt.active #group_id_register').val(group_id);
                                var check = $('.isSyncImportFile').is(':checked');
                                if (check) {
                                    // 带上文件提交表单
                                    $('.exp_conetnt.active #filesString_register').val(filesString);
                                }
                                $form.submit();
                                localStorage.setItem('last_cms_group', group_id);
                                $.closeModal();
                            },
                            // 弹框加载完成调用插件设置下拉框组件
                            function () {
                                // bug#9415, 鹰群选择弹框设置为可搜索筛选框
                                $('#choose_group_to_cms').useFSelect({
                                    width: '200px',
                                    fsWrapClassNameList: ['ml-10'],
                                });
                            });
                        }
                    }
                });
            });

            // add by hkk 2019/7/4 材料与仪器模块的注册化合物
            $('body').off('click.do_submit_chemical_biology').on('click.do_submit_chemical_biology', '.do_submit_chemical_biology', function () {
                var that = $(this);
                // 获取注册化合物当的名称，规格，批号
                var tr = that.closest('tr');
                var $headTr = tr.parents('table').find('tr.td_h');
                var headCols = $headTr.find('[name=field]');
                //var trData = $.formSerializeFn(tr);
                var trData = $(tr).find('[name=field_data]');
                var project_id = $(".exp_conetnt.active [name=project_id]").val() || '';

                // var dom = $(".exp_conetnt.active .my_exp_detial.on");
                // var eln_id = dom ? dom.attr('data-id') || '0' : $('.exp_conetnt.active #exp_id').val();
                var eln_id = $('.exp_conetnt.active #exp_id').val(); // add by hkk 2020/6/19

                var group_id = $.trim($(".exp_conetnt.active .exp_group_id").val());
                var smiles = '';

                //var name = trData.name || '';                      //名称 -- iupac名称
                //var batch_no = trData.batch_num || '';             //批号 -- 实验编号
                //var sequence_no = trData.product_model || '';       //规格 -- 序列号

                var name = $(trData[0]).val() || '';                      //名称 -- iupac名称
                var batch_no = $(trData[1]).val() || '';             //批号 -- 实验编号
                var sequence_no = $(trData[2]).val() || '';       //规格 -- 序列号


                // 获取上传文件信息
                var filesArray = [];
                tr.find('.single_detail_file').each(function(){
                    var allFileData = $.formSerializeFn($(this));
                    var fileData = {
                        dep_path: allFileData.dep_path ? allFileData.dep_path : '',
                        save_name: allFileData.save_name ? allFileData.save_name : '',
                        real_name: allFileData.file_name ? allFileData.file_name : ''
                    };
                    filesArray.push(fileData)
                });
                var filesString = JSON.stringify(filesArray);


                //要弹框并发送文件路径到CMS
                var jumpUrl;
                //add by wy 2023/4/28 新增注册到鹰群的选项
                $.ajaxFn({
                    url: '/?r=selection/get-group-project&only_active=1',
                    success: function (data) {
                        if (data.status === 1) {
                            var visibleGroupList = data.data.groupData;
                            //从本地读取上次选择的鹰群
                            var lastChooseGroup = localStorage.getItem('last_cms_group');
                            var popHtml = '<div class="info_part mudole_info_part" style="margin:20px 0"><label>' + mainLang('register to group') + '</label><select id="choose_group_to_cms" name="select_group" style="width: 198px;margin-left: 10px;" class="angle_input iblock group_select fs_select_2" name="select_group">';
                            for (var i = 0; i < visibleGroupList.length; i++) {
                                if (lastChooseGroup === null || lastChooseGroup === "") {
                                    if (i == 0) {
                                        popHtml += '<option style="width: 198px" selected value=' + visibleGroupList[i].group_id + '>' + visibleGroupList[i].group_name + '</option>';
                                    } else {
                                        popHtml += '<option style="width: 198px" value=' + visibleGroupList[i].group_id + '>' + visibleGroupList[i].group_name + '</option>';
                                    }
                                } else {
                                    if (visibleGroupList[i].group_id == lastChooseGroup) {
                                        popHtml += '<option style="width: 198px" selected value=' + visibleGroupList[i].group_id + '>' + visibleGroupList[i].group_name + '</option>';
                                    } else {
                                        popHtml += '<option style="width: 198px" value=' + visibleGroupList[i].group_id + '>' + visibleGroupList[i].group_name + '</option>';
                                    }
                                }
                            }
                            popHtml += '</select></div>';

                            if (filesArray.length > 0) {
                                popHtml += '<div><input class="isSyncImportFile" type="checkbox" name="isSyncImportFile"> ' +
                                    '<span>该产物对应的文件同步导入到化合物管理系统(CMS)中</span></div>' +
                                    '<div style="margin: 5px 0">如果选择同步导入，对应的文件会在化合物管理系统(CMS)的"上传文件"——"Files from InELN"中显示(请在CMS系统中预先配置好"上传文件"模块)</div>';
                            }
                            popHtml += '<div >以下是ELN中数据对接到CMS中的对应表: </div> '
                            popHtml += '<div><table> ' +
                                '<tr><td style="width: 50%">ELN</td><td>CMS</td></tr>' +
                                '<tr><td>' + $(headCols[0]).val() + '</td><td>名称</td></tr>' +
                                '<tr><td>' + $(headCols[1]).val() + '</td><td>实验编号/批号</td></tr>' +
                                '<tr><td>' + $(headCols[2]).val() + '</td><td>序列</td></tr>' +
                                '</table> </div> '

                            $.popContent(popHtml, '注册到CMS', function () {
                                var group_id = $.trim($("#choose_group_to_cms option:selected").val());
                                var check = $('.isSyncImportFile').is(':checked');
                                if (check) {
                                    //此时要带上文件信息
                                    jumpUrl = window.open();
                                    setTimeout(function () {
                                        jumpUrl.location = INCMS_URL + 'chemical/submit-step-1?eln_id=' + eln_id + '&project_id=' + project_id + '&batch_no=' + batch_no + '&change_group=1&group_id=' + group_id + '&sequence_name=' + name + '&sequence_no=' + sequence_no + '&smiles=' + smiles + '&filesString=' + encodeURIComponent(filesString);
                                    }, 100);
                                } else {
                                    jumpUrl = window.open();
                                    setTimeout(function () {
                                        jumpUrl.location = INCMS_URL + 'chemical/submit-step-1?eln_id=' + eln_id + '&project_id=' + project_id + '&batch_no=' + batch_no + '&change_group=1&group_id=' + group_id + '&sequence_name=' + name + '&sequence_no=' + sequence_no + '&smiles=' + smiles;
                                    }, 100);

                                }
                                //将这次选择的鹰群存储到本地
                                localStorage.setItem('last_cms_group',group_id);
                                $.closeModal();
                            });
                        }
                    }
                })
            });
        },

        doSubmitWms: function () { // 入库
            $('body').off('click.do_submit_wms').on('click.do_submit_wms', '.do_submit_wms', function (evt, eventParams) {

                var that = $(this);
                // 获取对应化合物详情的文件
                var expId = $('.exp_conetnt.active #exp_id').val();
                var expNum = $('.tag_bar .on .name').html();
                const InMaterial = require('in-material');
                const [materialData, err] = InMaterial.getMaterialFormatDB(expId);
                const materialForTextInsert = InMaterial.getMaterialFormatDisplay(expId)

                console.log('new-----')
                var prdList = materialForTextInsert.prd
                // 定义一个map 以 prdList 中的 uid_in_exp 字段作为map的key 以对应的行为map的值
                var prdMap = prdList.reduce(function(map, prdItem) {
                    // 检查 prdItem 是否有 uid_in_exp 字段
                    if (prdItem.hasOwnProperty('uid_in_exp')) {
                        // 将 uid_in_exp 字段作为 key，整个 prdItem 对象作为 value
                        map[prdItem.uid_in_exp] = prdItem;
                    }
                    return map;
                }, {}); // 初始化为一个空对象
                console.log(prdMap);
                // 将pro
                var produce_nb_unit = materialData.base_data.produce_nb_unit; // N 单位
                var produce_theo_unit = materialData.base_data.produce_theo_unit; // 理论质量 单位
                var produce_mass_unit = materialData.base_data.produce_mass_unit; // 实际质量 单位
                var configJson =  JSON.parse(materialData.base_data.material_column_config);
                console.log(configJson);

                var productConfig = configJson.productConfig

                console.log(productConfig);

                if (eventParams?.evtSrc === MATERIAL_EVT_SRC && eventParams?.materialRowData) {
                    const _mtRowData = eventParams.materialRowData; // 物料数据
                    console.log(_mtRowData)
                    productConfig.forEach(function(item) {

                        item.value = _mtRowData[item.name]?_mtRowData[item.name]:"";
                        item.unit = "";
                        // 批次号
                        if(item.name === "product_Batch_No"){
                            item.value = _mtRowData["batch_num"]?_mtRowData["batch_num"]:'';
                        }
                        // N
                        if(item.name === "product_N"){
                            item.unit = getUnit(item.name,produce_nb_unit)
                            console.log(prdMap[_mtRowData.uid_in_exp])

                            if (prdMap[_mtRowData.uid_in_exp]?.nb?.val_formatted) {
                                item.value = prdMap[_mtRowData.uid_in_exp].nb.val_formatted;
                            } else {
                                // 否则使用 _mtRowData["nb"]，如果不存在则使用空字符串
                                item.value = _mtRowData["nb"] ? _mtRowData["nb"] : '';
                            }

                        }
                        // 盐
                        if(item.name === "product_Salt"){
                            item.value = _mtRowData["salt"]?_mtRowData["salt"]:'';
                        }
                        // 盐当量
                        if(item.name === "product_Salt_Eq"){
                            item.value = _mtRowData["salt_eq"]?_mtRowData["salt_eq"]:'';
                        }
                        // MW
                        if(item.name === "product_MW"){
                            console.log(prdMap[_mtRowData.uid_in_exp])

                            if (prdMap[_mtRowData.uid_in_exp]?.molweight?.val_formatted) {
                                item.value = prdMap[_mtRowData.uid_in_exp].molweight.val_formatted;
                            } else {
                                // 否则使用 _mtRowData["nb"]，如果不存在则使用空字符串
                                item.value = _mtRowData["molweight"]?_mtRowData["molweight"]:'';
                            }

                        }
                        // 理论质量
                        if(item.name === "product_Theo_Mass"){
                            item.unit = getUnit(item.name,produce_theo_unit)
                            console.log(prdMap[_mtRowData.uid_in_exp])

                            if (prdMap[_mtRowData.uid_in_exp]?.theo?.val_formatted) {
                                item.value = prdMap[_mtRowData.uid_in_exp].theo.val_formatted;
                            } else {
                                // 否则使用 _mtRowData["nb"]，如果不存在则使用空字符串
                                item.value = _mtRowData["theo"]?_mtRowData["theo"]:'';
                            }

                        }
                        // 实际质量
                        if(item.name === "product_Actual_Mass"){
                            item.unit = getUnit(item.name,produce_mass_unit)
                            if (prdMap[_mtRowData.uid_in_exp]?.mass?.val_formatted) {
                                item.value = prdMap[_mtRowData.uid_in_exp].mass.val_formatted;
                            } else {
                                // 否则使用 _mtRowData["nb"]，如果不存在则使用空字符串
                                item.value = _mtRowData["mass"]?_mtRowData["mass"]:'';
                            }

                        }

                        // 产率
                        if(item.name === "product_Yield"){
                            if (prdMap[_mtRowData.uid_in_exp]?.yield?.val_formatted) {
                                item.value = prdMap[_mtRowData.uid_in_exp].yield.val_formatted;
                            } else {
                                // 否则使用 _mtRowData["yield"]，如果不存在则使用空字符串
                                item.value = _mtRowData["yield"]?_mtRowData["yield"]:'';
                            }
                        }

                        // 纯度
                        if(item.name === "product_Purity"){
                            item.value = _mtRowData["purity"]?_mtRowData["purity"]:'';
                        }
                        // 样品ID
                        if(item.name === "product_Sample_Id"){
                            item.value = _mtRowData["sample_id"]?_mtRowData["sample_id"]:'';
                        }
                        // 条码
                        if(item.name === "product_Barcode"){
                            item.value = _mtRowData["barcode"]?_mtRowData["barcode"]:'';
                        }
                        // 注册编号
                        if(item.name === "cms_chem_code"){
                            item.value = _mtRowData["cms_chem_code"]?_mtRowData["cms_chem_code"]:'';
                        }
                        // cLogP
                        if(item.name === "cLogP"){
                            item.value = _mtRowData["cLogP"]?_mtRowData["cLogP"]:'';
                        }
                        // tPSA
                        if(item.name === "tPSA"){
                            item.value = _mtRowData["tPSA"]?_mtRowData["tPSA"]:'';
                        }
                        // 单价
                        if(item.name === "product_unitPrice"){
                            item.value = _mtRowData["product_unitPrice"]?_mtRowData["product_unitPrice"]:'';
                        }
                        // 总金额
                        if(item.name === "product_price"){
                            item.value = _mtRowData["product_price"]?_mtRowData["product_price"]:'';
                        }
                        // 质量收率
                        if(item.name === "product_mass_yield"){

                            item.value = _mtRowData["product_mass_yield"]?_mtRowData["product_mass_yield"]:'';
                        }
                        // smiles
                        if(item.name === "smiles"){
                            item.name_zh = "SMILES";
                            item.name_en = "SMILES";
                            item.value = _mtRowData["smiles"]?_mtRowData["smiles"]:'';
                        }
                        // 结构式
                        if(item.name === "molstring"){
                            item.name_zh = "结构式";
                            item.name_en = "结构式";
                            item.value = _mtRowData["molstring"]?_mtRowData["molstring"]:'';
                        }
                    });

                    productConfig.push({
                        'name':'smiles',
                        'name_zh':'SMILES',
                        'name_en':'SMILES',
                        'value': _mtRowData["smiles"]?_mtRowData["smiles"]:'',
                        'unit':''
                    })

                    productConfig.push({
                        'name':'molstring',
                        'name_zh':'结构式',
                        'name_en':'结构式',
                        'value': _mtRowData["molstring"]?_mtRowData["molstring"]:'',
                        'unit':''
                    })

                    productConfig.push({
                        'name':'exp_num',
                        'name_zh':'记录本页码',
                        'name_en':'记录本页码',
                        'value':expNum,
                        'unit':''
                    })
                    console.log(productConfig)

                }
                // 过滤值为空和show为false的记录
                const filteredData = productConfig.filter(item => {
                    // 确保 item 存在，并且 show 不是 false，value 不是空值
                    return item && item.show !== false &&
                        (item.value !== "" && item.value !== null && item.value !== undefined);
                });
                var resultObject = {};
                filteredData.forEach(item => {
                    const key = window.lang === "cn"
                        ? item.name_zh ?? item.name_defined
                        : item.name_en ?? item.name_defined;

                    if (key) { // 确保 key 存在，避免 undefined/null 作为键
                        resultObject[key] = {
                            value: item.value,
                            unit: item.unit ?? "", // 如果 unit 不存在，默认空字符串
                        };
                    }
                });
                var lastChooseInventory = localStorage.getItem('last_chosse_inventory');    
                var lastChooseTemplate = localStorage.getItem('last_chosse_template');    
                $.ajaxFn({
                    url: '/?r=chem/get-inventory-and-template',
                    data: {
                        inventory_id: lastChooseInventory? lastChooseInventory:'',
                        template_id:lastChooseTemplate ? lastChooseTemplate:''
                    },
                    success: function (data) {
                        if (data.status === 1) {

                            var popHtml = data.data.contentHtml

                            $.popContent(popHtml, mainLang('stock_in_wms'), function () {
                                var inventory_id = $.trim($("#choose_inventory_to_wms option:selected").val());
                                var template_id = $.trim($("#choose_template_to_wms option:selected").val());
                                if (!inventory_id) {
                                    $.showAlert(mainLang('select_inventory_tips'));
                                    return;
                                }
                                if (!template_id) {
                                    $.showAlert(mainLang('select_template_tips'));
                                    return;
                                }
                                // 先发送 POST 请求，获取跳转 URL
                                var form = document.createElement('form');
                                form.method = 'POST';
                                form.action = INVENTORY_URL + '?url=/eln-in/add-product-eln&type=add_eln&inventory_id='+inventory_id+'&template_id='+template_id + '&fromInDraw=true';
                                form.target = '_blank'; // 在新窗口打开

                                // 添加参数
                                var params = {
                                    inventory_id: inventory_id,
                                    template_id: template_id,
                                    productList: [resultObject]
                                };

                                // 动态创建 input 并添加到 form
                                Object.keys(params).forEach(function(key) {
                                    var input = document.createElement('input');
                                    input.type = 'hidden';
                                    input.name = key;

                                    if (key === 'productList') {
                                        input.value = JSON.stringify(params[key]); // 转为 JSON 字符串
                                    } else {
                                        input.value = params[key];
                                    }

                                    form.appendChild(input);
                                });

                                document.body.appendChild(form);
                                form.submit(); // 提交表单，在新窗口打开
                                document.body.removeChild(form); // 移除临时表单



                                //将这次选择的鹰群存储到本地
                                localStorage.setItem('last_chosse_inventory',inventory_id);
                                localStorage.setItem('last_chosse_template',template_id);
                                $.closeModal();
                            });
                        }
                    }
                })
            });

            // add by hkk 2019/7/4 材料与仪器模块的注册化合物
            $('body').off('click.do_submit_wms_biology').on('click.do_submit_wms_biology', '.do_submit_wms_biology', function () {
                var that = $(this);
                // 获取注册化合物当的名称，规格，批号
                var expId = $('.exp_conetnt.active #exp_id').val();
                var expNum = $('.tag_bar .on .name').html();
                var tr = that.closest('tr');
                var $headTr = tr.parents('table').find('tr.td_h');
                var headCols = $headTr.find('[name=field]');

                console.log(headCols)

                var trData = $(tr).find('[name=field_data]');


                var resultObject = {};
                headCols.each(function(index) {
                    var key = $(this).val();
                    var value = $(trData[index]).val();
                    if (value) {
                        resultObject[key] = {
                            value: value,
                            unit: ""
                        };
                    }
                });

                const key = window.lang === "en"
                    ? "InELN Page Number" :"记录本页码";

                resultObject[key] = {
                    value: expNum,
                    unit: ""
                }



                console.log(resultObject);

                //要弹框并发送文件路径到CMS
                var jumpUrl;
                //add by wy 2023/4/28 新增注册到鹰群的选项
                var lastChooseInventory = localStorage.getItem('last_chosse_inventory');    
                var lastChooseTemplate = localStorage.getItem('last_chosse_template');    
                $.ajaxFn({
                    url: '/?r=chem/get-inventory-and-template',
                    data: {
                        inventory_id: lastChooseInventory? lastChooseInventory:'',
                        template_id:lastChooseTemplate ? lastChooseTemplate:''
                    },
                    success: function (data) {
                        if (data.status === 1) {

                            var popHtml = data.data.contentHtml

                            $.popContent(popHtml, mainLang('stock_in_wms'), function () {
                                var inventory_id = $.trim($("#choose_inventory_to_wms option:selected").val());
                                var template_id = $.trim($("#choose_template_to_wms option:selected").val());
                                if (!inventory_id) {
                                    $.showAlert(mainLang('select_inventory_tips'));
                                    return;
                                }
                                if (!template_id) {
                                    $.showAlert(mainLang('select_template_tips'));
                                    return;
                                }
                                // 先发送 POST 请求，获取跳转 URL
                                var form = document.createElement('form');
                                form.method = 'POST';
                                form.action = INVENTORY_URL + '?url=/eln-in/add-product-eln&type=add_eln&inventory_id='+inventory_id+'&template_id='+template_id;
                                form.target = '_blank'; // 在新窗口打开

                                // 添加参数
                                var params = {
                                    inventory_id: inventory_id,
                                    template_id: template_id,
                                    productList: [resultObject]
                                };

                                console.log(params)

                                // 动态创建 input 并添加到 form
                                Object.keys(params).forEach(function(key) {
                                    var input = document.createElement('input');
                                    input.type = 'hidden';
                                    input.name = key;

                                    if (key === 'productList') {
                                        input.value = JSON.stringify(params[key]); // 转为 JSON 字符串
                                    } else {
                                        input.value = params[key];
                                    }

                                    form.appendChild(input);
                                });

                                document.body.appendChild(form);
                                form.submit(); // 提交表单，在新窗口打开
                                document.body.removeChild(form); // 移除临时表单



                                //将这次选择的鹰群存储到本地
                                localStorage.setItem('last_chosse_inventory',inventory_id);
                                localStorage.setItem('last_chosse_template',template_id);
                                $.closeModal();
                            });
                        }
                    }
                })
            });
        },

        showMsds: function () { // 查看msds show_msds
            $('body').off('click.show_msds').on('click.show_msds', '.show_msds', function (evt, materialParams) {
                console.info('搜索MSDS', materialParams);

                const materialRow = materialParams.materialRow;

                // var smiles = condationSearchAction.getSmiles($(this));

                // var msdsname = $(this).parents('.name_td').find('input[property="name"]')[0].value;
                // var msdsCas = $('.exp_conetnt.active .details_tr').find('input[value="'+ msdsname +'"]').parents('.name_td').next().find('input[property="cas"]')[0].value;

                const smiles = materialRow.smiles;
                const msdsCas = materialRow.cas;

                if ($.checkVal(smiles, 'noempty') || $.checkVal(msdsCas, 'noempty')) {
                    $.ajaxFn({
                        url: ELN_URL + '?r=chem/show-msds',
                        data: {
                            smiles: smiles,
                            msdsCas:msdsCas ? msdsCas:''
                        },
                        success: function (msg) {
                            if (msg.status != 1) {
                                $.showAlert(msg.info);
                                return;
                            }

                            $.showContent('warning', 'MSDS', msg.data.file);
                            $(".modal_alert_box").css("padding", 0);
                        }
                    });
                } else{
                   // $.showAlert(mainLang('no_smiles'));
                    $.showAlert(mainLang('no_msds_data'));
                }
            });
        },

        addSaltType: function () { // 添加盐型
            $('body').off('click.salt_type_view').on('click.salt_type_view', '.salt_type_view', function () {
                var tr = $(this).parents('.tr_');
                var num = parseInt($('span.num', tr).text());
                var expId = $('.exp_conetnt.active #exp_id').val() || $('.exp_conetnt.active #temp_id').val();
                var type = tr.attr('addtype');
                var saltId = $('.exp_conetnt.active .' + type + '_' + expId).find('#' + type + '_salt_id_' + num).val();
                var saltNum = $('.exp_conetnt.active .' + type + '_' + expId).find('#' + type + '_salt_num_' + num).val();

                $.ajaxFn({
                    url: ELN_URL + '?r=chem/salt-list',
                    data: {
                        key: num,
                        type: type,
                        expId: expId,
                        saltNum: saltNum,
                        saltId: saltId
                    },
                    success: function (msg) {
                        if (msg.status != 1) {
                            $.showAlert(msg.info);
                            return;
                        }

                        $.popContent(msg.data, mainLang('salt_type'), function () {
                            var newSaltNum = $('.salt_modal_body .input_salt_num').val();
                            var newSaltId = $('.salt_modal_body .select_salt_type').val();
                            var newSaltName = $('.salt_modal_body .select_salt_type option:selected').attr('salt-name');

                            if ('1' == newSaltId) {
                                if ('0' != newSaltNum) {
                                    $.showAlert(mainLang('no_salt_type'));
                                    return;
                                }
                            }

                            if ('1' != newSaltId) {
                                if ('0' == newSaltNum) {
                                    $.showAlert(mainLang('no_salt_num'));
                                    return;
                                }
                            }

                            $('.exp_conetnt.active .' + type + '_' + expId).find('#' + type + '_salt_id_' + num).val(newSaltId);
                            $('.exp_conetnt.active .' + type + '_' + expId).find('#' + type + '_salt_name_' + num).val(newSaltName);
                            $('.exp_conetnt.active .' + type + '_' + expId).find('#' + type + '_salt_num_' + num).val(newSaltNum);
                            $('.exp_conetnt.active .' + type + '_' + expId).find('#' + type + '_solvent_id_' + num).val(0);
                            $('.exp_conetnt.active .' + type + '_' + expId).find('#' + type + '_solvent_name_' + num).val('');

                            // 修改mw
                            var mw = $('.exp_conetnt.active .' + type + '_' + expId).find('#' + type + '_molweight_' + num).val();
                            if (saltNum == '' || saltNum == 'undefined' || saltNum == false || saltNum == null || saltNum == 0) {
                                saltNum = 0;
                            }
                            if (newSaltNum == '' || newSaltNum == 'undefined' || newSaltNum == false || newSaltNum == null || newSaltNum == 0) {
                                newSaltNum = 0;
                            }
                            if (mw == '' || mw == 'undefined' || mw == false || mw == null || mw == 0) {
                                mw = '';
                            }

                            var nmw = Number(mw) + Number(newSaltNum) - Number(saltNum);
                            if (nmw != 0) {
                                $('.exp_conetnt.active .' + type + '_' + expId).find('#' + type + '_molweight_' + num).val(nmw);
                            }

                            if (mw != nmw) {
                                //触发自动计算
                                window.calc($('.exp_conetnt.active .' + type + '_' + expId).find('#' + type + '_molweight_' + num));
                            }

                            $.closeModal();
                        });
                    }
                });
            });
        },

        // add by hkk 2019/11/6 从仪器库添加仪器弹出框 材料与仪器模块
        addFromInstruments:function () {
            var that = this;
            $('body').off('click.add_from_instruments').on('click', '.add_from_instruments', function () {

                // 此行加添加数据标记用于添加仪器信息
                $('.exp_conetnt.active .toAddInstrument').removeClass('toAddInstrument'); // 移除所有添加标记
                $(this).parents('tr.defineData').addClass('toAddInstrument');
                $(this).parents('tr.biologyData ').addClass('toAddInstrument');  // 老的材料与仪器也能添加仪器
                var instrumentName = $(this).parent('.dropdown-menu').siblings('.reference_name').val();


                $.ajaxFn({
                    url: ELN_URL + '?r=instrument/get-pop-instruments-page',
                    data: {
                        type: 'fromBiology',
                        instrument_name: instrumentName,
                        needUpdateAllPage: true
                    },
                    success: function (data) {
                        if (data.status == 1) {
                            $(".instrument_page").remove();
                            $("body").append(data.data.file);
                            $(".instrument_page").modal('show');

                            that.pageData = 1;
                            that.popInstrumentsPageFn();//调用分页插件

                        }
                    }
                });
            });

            // 仪器库弹出框 搜索提交事件 add by hkk 2019/11/6
            $("body").on('click', '.instruments_pop_page .search-instrument-pop', function (event) {

                // 获取查询条件和页数信息
                var data = $.formSerializeFn($("#search-instrument-pop"));
                data['page'] = 1;
                data['limit'] = 5;
                data['needUpdateAllPage'] = 0;
                data['type'] = 'fromBiology';
                that.pageData = 1;

                $.ajaxFn({
                    url: ELN_URL + '?r=instrument/get-pop-instruments-page',
                    data: data,
                    success: function (data) {
                        if (data.status == 1) {

                            $('.exp_conetnt.active ').removeClass('search');
                            $('.instruments_pop_table').html(data.data.file);

                            that.popInstrumentsPageFn();//调用分页插件
                        }
                    }
                })
            });

            // 仪器库弹出框 pop预约事件 add by hkk 2019/11/6
            $('body').on('click', '.instruments_pop_page .popOperateInstrument', function () {
                var type = $(this).attr('data-type'); // add view edit repair history book
                var instrumentId =  $(this).parents('tr').attr('data-id'); // 除了新增都有id
                $.ajaxFn({
                    url: ELN_URL + '?r=instrument/get-page',
                    data: {
                        instrumentId:instrumentId,
                        type: type,
                    },
                    success: function (data) {
                        if (data.status == 1) {

                            $.popContent(data.data.file, mainLang('instrument_book'), function () {

                                // 提交预约
                                var type = 'book';
                                var instrumentData = {};
                                instrumentData.instrument_id = instrumentId;
                                instrumentData.start_time  = $('.instrument_book_pop .instrument_book [name="start_time"]').val();
                                instrumentData.end_time  = $('.instrument_book_pop .instrument_book [name="end_time"]').val();
                                instrumentData.remark  = $('.instrument_book_pop #book_instrument_remark').val();
                                instrumentData.related_experiment = $('.instrument_book_pop .related_experiments').val();
                                if(instrumentData.start_time === "" || instrumentData.end_time === "" ){
                                    $.showAlert(mainLang('instrument_book_tip1'));
                                    return
                                }

                                $.ajaxFn({
                                    url: ELN_URL + '?r=instrument/submit-page',
                                    data: {
                                        instrumentData:instrumentData,
                                        type: type,
                                    },
                                    success: function (data) {
                                        if (data.status == 1) {

                                            $(".instrument_book_pop").remove();
                                            $.showAlert(mainLang('book_successful'));
                                            $('.pop_modal').modal('hide');
                                        }
                                    },
                                })

                            }, function () {

                                $('.instrument_book_pop').parents('.modal-dialog').addClass('modal-dialog-840');

                                //预约界面调用日历插件
                                $('.datetimepicker.dropdown-menu').remove();
                                if(type === "popBook"){
                                    var dateOpts = {
                                        format: 'yyyy-mm-dd hh:ii',
                                        autoclose: true,
                                        minView: 0, // 0精确到分钟 2精确到小时
                                        clearBtn: true,
                                    };

                                    if( $.fn.datetimepicker){
                                        $('.instrument_book_pop .datetimepicker').datetimepicker(dateOpts).on('click', function () {

                                            if ($('.instrument_book_pop [name="end_time"]').val() != '') {
                                                var startTimer = $('.instrument_book_pop [name="end_time"]').val() ;
                                                $('.instrument_book_pop [name="start_time"]').datetimepicker('setEndDate', startTimer);
                                            }

                                            if ($('.instrument_book_pop [name="start_time"]').val() != '') {
                                                var endTimer = $('.instrument_book_pop [name="start_time"]').val();
                                                $('.instrument_book_pop [name="end_time"]').datetimepicker('setStartDate', endTimer);
                                            }
                                        })
                                    }

                                }

                                // add by hkk 2020/7/15 预约界面自动填写当前实验编号
                                if($('.exp_title .on.tag').attr('data-func')==='getExpContent'){
                                    var exp_page = $('.exp_title .on.tag').attr('title') || '';
                                    $('.instrument_book_pop .related_experiments').val(exp_page)
                                }

                                var text = $('.pop_modal h4').text();
                                $('.pop_modal h4').html(text + "<span>" + $('.instruments_pop_page .trBlue .instrument_name').text() +"</span>");

                            });

                        }
                    }
                });

            });
            $('body').on('click', '.ineln_title .hideColumnBtn', function() {
                var tableId = $(this).attr('data-tableId');
                var basicHtml = `<div class="instrumentsShowHideModal title">
                            <input type="checkbox" class="beauty-checkbox-big" id="basic_title" />
                            <label for="basic_title">${mainLang('check_all')}</label>
                        </div>
                        <ul class="ml30 exp-list-cols clear basic">`;
                // var manageHtml = `<div class="instrumentsShowHideModal title">
                //             <input type="checkbox" class="beauty-checkbox-big" id="manage_title" />
                //             <label for="manage_title">${mainLang('manage_info')}</label>
                //         </div>
                //         <ul class="ml30 exp-list-cols clear manage">`;
                var th = $(`#${tableId}`).find('tr th');
                th.each(function(index, item) {
                    var checked = ($(this).is(':visible') || index === 0) ? 'checked' : '';
                    var field = $(this).attr('data-field'); // 从索引改为记录具体显示的字段
                    if ($(item).hasClass('basic_field')) {
                        basicHtml += `<li class="col-item basic" data-index="${field}" ${index === 0 ? 'style="display:none"' : ''}>
                                 <input type="checkbox" class="beauty-checkbox-big " id="idForShowColumn${index}" ${checked} />
                                 <label for="idForShowColumn${index}" title="${$(item).text().trim()}"><span>${$(item).text().trim()}</span>
                                 </label>
                              </li>`
                    }
                    // if ($(item).hasClass('manage_field')) {
                    //     manageHtml += `<li class="col-item manage" data-index="${field}">
                    //              <input type="checkbox" class="beauty-checkbox-big" id="idForShowColumn${index}" ${checked} />
                    //              <label for="idForShowColumn${index}" title="${$(item).text().trim()}"><span>${$(item).text().trim()}</span>
                    //              </label>
                    //             </li>`
                    // }
                });
                basicHtml +='</ul>';
                //manageHtml +='</ul>';
                var html = `<div class="instrumentShowModalContents">${basicHtml}<div>`;

                $.popContent(html, mainLang('show_hidden'), function() {

                    var table =  $(`#${tableId}`);

                    // 存储隐藏列的索引
                    var colNameArray = [];
                    $('.instrumentShowModalContents .exp-list-cols .col-item .beauty-checkbox-big:checkbox').each(function (index, item) {
                        var colIndex = $(item).parent('li').attr('data-index')
                        if ($(this).is(':checked')) {
                            table.find(`td[data-field="${colIndex}"]`).show();
                            table.find(`th[data-field="${colIndex}"]`).show();

                        } else {
                            table.find(`td[data-field="${colIndex}"]`).hide();
                            table.find(`th[data-field="${colIndex}"]`).hide();
                            colNameArray.push(colIndex);
                        }
                    });
                    localStorage.setItem(tableId + '_cols_index', colNameArray.join());


                    $(".exp-list-cols").parents('.pop_modal').modal('hide');
                },function () {

                    // 判断是否全选基础信息字段
                    if ($('.instrumentShowModalContents ul.basic input').length === $('.instrumentShowModalContents ul.basic input:checked').length) { // 判断子节点是否都选中则选中标题全选框
                        $('.instrumentShowModalContents input#basic_title').prop('checked', true)
                    }
                    // 判断是否全选管理信息字段
                    // var allNumber = $('.instrumentShowModalContents ul.manage input').length;
                    // var allCheckedNumber = $('.instrumentShowModalContents ul.manage input:checked').length;
                    // if (allCheckedNumber > 0 && allCheckedNumber === allNumber) {
                    //     $('.instrumentShowModalContents input#manage_title').prop('checked', true)
                    // }
                });
            });
            // //筛选列
            // $("body").on('click', '.ineln_title .hideColumnBtn', function(event) {
            //     var html = '<ul class="ml30 exp-list-cols clear">';
            //     $('.instruments_pop_table .list_title th').each(function (i, item) {
            //         var colFlag = $(this).attr('data-name');
            //         var checked = $(this).is(':visible') ? 'checked' : '';
            //         html += '<li class="col-item">' +
            //             '<input type="checkbox" class="beauty-checkbox-big" id="' + colFlag + '" ' + checked + '/>' +
            //             '<label for="' + colFlag + '">' + $(this).text() + '</label>' +
            //             '</li>';
            //     });
            //     $.popContent(html, mainLang('show_hidden'), function () {
            //         var $table = $('.instruments_pop_table table');
            //         $('.col-item :checkbox').each(function () {
            //             if ($(this).is(':checked')) {
            //                 $table.find('th[data-name="' + $(this).attr('id') + '"]').show();
            //                 $table.find('td[data-name="' + $(this).attr('id') + '"]').show();
            //             } else {
            //                 $table.find('th[data-name="' + $(this).attr('id') + '"]').hide();
            //                 $table.find('td[data-name="' + $(this).attr('id') + '"]').hide();
            //             }
            //         });
            //         $(".pop_modal").modal("hide");
            //     });
            // });

            // 仪器库弹出框 行点击选中事件 add by hkk 2019/11/7
            $('body').on('click', '.instruments_pop_table tr:not(:first-child)', function () {
                $('.instruments_pop_table tr').removeClass('trBlue');
                $(this).addClass('trBlue');
                $(this).find('input[name="chooseInstrument"]').prop("checked",true);
            });

            // 仪器库弹出框确定事件，写入数据到材料与仪器表格或文本编辑器 add by hkk 2019/11/6
            $('body').on('click', '.instruments_pop_page .instrument_conform_btn', function () {
                var chooseInstrument = $(this).parents('.modal-content').find('.instruments_pop_table .trBlue');
                if (chooseInstrument.length === 0) {
                    $.showAlert(mainLang('please_choose_instrument'));
                    return;
                }
                // 获取当前选中行仪器的数据
                var instrument_name = chooseInstrument.find('.instrument_name')[0].innerText;
                var instrument_batch_number = chooseInstrument.find('.instrument_batch_number').length > 0
                    ? chooseInstrument.find('.instrument_batch_number')[0].innerText
                    : '';

                var instrument_specification = chooseInstrument.find('.instrument_specification').length > 0
                    ? chooseInstrument.find('.instrument_specification')[0].innerText
                    : '';
                var instrument_model = chooseInstrument.find('.instrument_model').length > 0
                    ? chooseInstrument.find('.instrument_model')[0].innerText
                    : '';
                var instrument_manufacturer = chooseInstrument.find('.instrument_manufacturer').length > 0
                    ? chooseInstrument.find('.instrument_manufacturer')[0].innerText
                    : '';
                var instrument_id = chooseInstrument.attr('data-id'); // add by hkk 2021/4/30

                $(".instruments_pop_page").remove();

                // 写入数据到材料与仪器表格
                if ($(this).attr('data-type') === 'fromBiology') {
                    var toAddTr = $('.exp_conetnt.active .toAddInstrument');
                    if (toAddTr.length !== 1) {
                        return;
                    }
                    // 发请求从后端获取 字段 => 值
                    $.ajaxFn({
                        url: ELN_URL + '?r=instrument/get-info-by-id',
                        data: {
                            'instrument_id' : instrument_id
                        },
                        type: 'POST',
                    }).then((res) => {
                        if (res.status === '0') {
                            return;
                        }
                        // 数据源 field -> val
                        var sourceField2ValMap = res.data.filed2val;
                        // 自动填充
                        var matchedInputIndexArr = autoFillFileds(sourceField2ValMap, toAddTr);
                        // 若第一列和第二列未匹配中, 则填入默认项
                        if (!matchedInputIndexArr.includes(0)) {
                            toAddTr.find('input:eq(0)').val(instrument_name);
                        }
                        if (!matchedInputIndexArr.includes(1)) {
                            toAddTr.find('input:eq(1)').val(instrument_batch_number);
                        }

                        // 操作栏增加运行按钮 2021/4/30
                        var operateDom = '<i class="instrument-running-record-ico beginOrEndOperateRecordByExp"  data-type="begin"  data-id="'+ instrument_id + '" style="margin: -1px 0 0 5px;" title="' + mainLang('instrument_operate') +'"> </i>';
                        if(toAddTr.find('.instrument-running-record-ico').length >= 1){ //存在就要先删掉 只删除了运行
                            toAddTr.find('.instrument-running-record-ico').remove();
                        }
                        if(toAddTr.find('.instrument-end-ico').length >= 1){ //存在就要先删掉 删除结束
                            toAddTr.find('.instrument-end-ico').remove();
                        }
                        toAddTr.find('.add_literature_part_next').after(operateDom);

                        toAddTr.removeClass('toAddInstrument');

                    });
                    return;
                }
                // 写入数据到文本编辑器
                var editorHtml = instrument_name;
                var otherInfo = [];
                if (instrument_batch_number) {
                    otherInfo.push(instrument_batch_number);
                }
                if (instrument_specification) {
                    otherInfo.push(instrument_specification);
                }
                if (instrument_model) {
                    otherInfo.push(instrument_model);
                }
                if (instrument_manufacturer) {
                    otherInfo.push(instrument_manufacturer);
                }
                if (otherInfo.length > 0) {
                    editorHtml += '(' + otherInfo.join(', ') + ')';
                }
                console.log(editorHtml);
                var currentEditor = UE.getEditor($(this).attr('data-editorKey'));
                currentEditor.execCommand('insertHtml', editorHtml);

                //! todo bug#34786 由于插入仪器的逻辑修改,选中仪器的数据增加了判空操作,并优化了代码结构,旧代码先注释,以后再删 cmt dx
                // if (chooseInstrument.length > 0) {
                //
                //     // 获取当前选中行仪器的数据
                //     var instrument_name = chooseInstrument.find('.instrument_name')[0].innerText;
                //     var instrument_batch_number = chooseInstrument.find('.instrument_batch_number')[0].innerText;
                //
                //     var instrument_specification = chooseInstrument.find('.instrument_specification').length > 0
                //         ? chooseInstrument.find('.instrument_specification')[0].innerText
                //         : '';
                //     var instrument_model = chooseInstrument.find('.instrument_model').length > 0
                //         ? chooseInstrument.find('.instrument_model')[0].innerText
                //         : '';
                //     var instrument_manufacturer = chooseInstrument.find('.instrument_manufacturer').length > 0
                //         ? chooseInstrument.find('.instrument_manufacturer')[0].innerText
                //         : '';
                //     var instrument_id = chooseInstrument.attr('data-id'); // add by hkk 2021/4/30
                //
                //     $(".instruments_pop_page").remove();
                //
                //     // 写入数据到材料与仪器表格
                //     if ($(this).attr('data-type') === 'fromBiology') {
                //         var toAddTr = $('.exp_conetnt.active .toAddInstrument');
                //         if (toAddTr.length === 1) {
                //             // 发请求从后端获取 字段 => 值
                //             $.ajaxFn({
                //                 url: ELN_URL + '?r=instrument/get-info-by-id',
                //                 data: {
                //                     'instrument_id' : instrument_id
                //                 },
                //                 type: 'POST',
                //                 success: function (res) {
                //                     if (1 == res.status) {
                //                         // 数据源 field -> val
                //                         var sourceField2ValMap = res.data.filed2val;
                //                         // 自动填充
                //                         var matchedInputIndexArr = autoFillFileds(sourceField2ValMap, toAddTr);
                //                         // 若第一列和第二列未匹配中, 则填入默认项
                //                         if (!matchedInputIndexArr.includes(0)) {
                //                             toAddTr.find('input:eq(0)').val(instrument_name);
                //                         }
                //                         if (!matchedInputIndexArr.includes(1)) {
                //                             toAddTr.find('input:eq(1)').val(instrument_batch_number);
                //                         }
                //
                //                         // 操作栏增加运行按钮 2021/4/30
                //                         var operateDom = '<i class="instrument-running-record-ico beginOrEndOperateRecordByExp"  data-type="begin"  data-id="'+ instrument_id + '" style="margin: -1px 0 0 5px;" title="' + mainLang('instrument_operate') +'"> </i>';
                //                         if(toAddTr.find('.instrument-running-record-ico').length >= 1){ //存在就要先删掉
                //                             toAddTr.find('.instrument-running-record-ico').remove();
                //                         }
                //                         toAddTr.find('.add_literature_part_next').after(operateDom);
                //
                //                         toAddTr.removeClass('toAddInstrument');
                //                     }
                //                 }
                //             });
                //         }
                //     } else { // 写入数据到文本编辑器
                //         var editorHtml = instrument_name;
                //         var otherInfo = [];
                //         if (instrument_batch_number) {
                //             otherInfo.push(instrument_batch_number);
                //         }
                //         if (instrument_specification) {
                //             otherInfo.push(instrument_specification);
                //         }
                //         if (instrument_model) {
                //             otherInfo.push(instrument_model);
                //         }
                //         if (instrument_manufacturer) {
                //             otherInfo.push(instrument_manufacturer);
                //         }
                //         if (otherInfo.length > 0) {
                //             editorHtml += '(' + otherInfo.join(', ') + ')';
                //         }
                //         console.log(editorHtml);
                //         var currentEditor = UE.getEditor($(this).attr('data-editorKey'));
                //         currentEditor.execCommand('insertHtml', editorHtml);
                //     }
                // }else{
                //     $.showAlert(mainLang('please_choose_instrument'));
                // }

            });

        },

        // 从CMS添加
        addFromCms: function () {
            // 绑定事件：从CMS添加
            $('body').off('click.add_from_cms').on('click', '.add_from_cms', function () {
                const $trDom = $(this).parents('tr'); // 当前操作的行
                $.ajaxFn({
                    url: ELN_URL + '?r=popup/search-cms',
                    data: {
                        default_group_id: localStorage.getItem('search_cms_group')
                    },
                    success: function (res) {
                        if (res.status == 1) {
                            // 弹窗
                            $.popContent(res.data.html, mainLang('add_cms_samples'), function () {
                                const $popupEle = $('.search-cms-popup'); // 当前弹窗
                                const selectedChemicalIds = []; // 记录选中的化合物ID列表
                                $popupEle.find('table tr').each(function () {
                                    const $row = $(this);
                                    const isChecked = $row.find('input.checkboxBtn').is(':checked');
                                    if (isChecked) {
                                        const chemicalId = $row.data('chemical_id'); // 获取 data-chemical_id 属性值
                                        selectedChemicalIds.push(chemicalId);
                                    }
                                });

                                if (selectedChemicalIds.length === 0) {
                                    $.showAlert(mainLang('chemical_empty_tip'));
                                    return;
                                }

                                // 获取选中化合物的字段数据
                                const cmsModule = require('cms');
                                cmsModule.getFieldsDataByIds(selectedChemicalIds).then(function (res) {
                                    if (res.status == 1) {
                                        const chemicalData = res.data;
                                        const $table = $trDom.parents('table'); // 表格
                                        const $titleTr = $table.find('tr:first'); // 标题行
                                        const titleArr = []; // 标题数组
                                        $titleTr.find('th input[name=field]').each(function () {
                                            titleArr.push($(this).val().trim());
                                        });

                                        // 以当前行的HTML为模板
                                        const tempRowHtml = $trDom[0].outerHTML;
                                        const fragment = document.createDocumentFragment();
                                        // 遍历化合物
                                        for (const [chemicalId, data] of Object.entries(chemicalData)) {
                                            // 生成新行
                                            const $newRow = $(tempRowHtml);
                                            $newRow.find('td').each(function (tdIndex) {
                                                const $input = $(this).find('input[name="field_data"]');
                                                const tdTitle = titleArr[tdIndex];
                                                let tdValue = '';
                                                if (data[tdTitle] !== undefined) {
                                                    tdValue = data[tdTitle];
                                                } else {
                                                    // 没有匹配时，第一列用iupac，第二列用批号
                                                    if (tdIndex === 0 && data['iupac'] !== undefined) {
                                                        tdValue = data['iupac'];
                                                    } else if (tdIndex === 1 && data['exp_code'] !== undefined) {
                                                        tdValue = data['exp_code'];
                                                    }
                                                }
                                                $input.val(tdValue);

                                                // 上传文件列和操作列处理(仪器操作项)
                                                $(this).find('.detail_file_add').empty();
                                                $(this).find('i.beginOrEndOperateRecordByExp').remove();
                                            });
                                            fragment.appendChild($newRow[0]);
                                        }
                                        $trDom.after(fragment);
                                    }
                                });

                                // 记住鹰群
                                localStorage.setItem('search_cms_group', $popupEle.find('select.group_list').val());

                                // 关闭弹窗
                                $.closeModal();
                            });
                        }
                    }
                });
            });

            // 绑定事件：搜索CMS
            $('body').on('click', '.search-cms-popup .search-cms', function () {
                const $popupEle = $('.search-cms-popup'); // 当前弹窗
                const groupId = $popupEle.find('select.group_list').val();
                const isBatch = $(this).hasClass('batch-search') ? 1 : 0;
                let searchKey = $popupEle.find('.search-key').val().trim();
                if (isBatch) {
                    searchKey = $popupEle.find('textarea.batch-search-key').val().split("\n").filter(x => x.toString().trim() != '');
                }
                if (searchKey.length === 0) {
                    return;
                }

                // 搜索
                const cmsModule = require('cms');
                cmsModule.searchCms(groupId, searchKey, isBatch).then(function (res) {
                    // 根据返回的搜索结果渲染
                    const chemicalList = res.data;
                    if (chemicalList.length === 0) {
                        const nodataHtml = '<td colspan="6">' + mainLang('search_nodata') + '</td>';
                        $popupEle.find('.search-nodata').html(nodataHtml);
                        return;
                    }

                    $popupEle.find('.search-nodata').remove();
                    const currentIds = [];
                    $popupEle.find('tr').each(function () {
                        if ($(this).data('chemical_id')) {
                            currentIds.push($(this).data('chemical_id'));
                        }
                    });

                    for (let i = 0; i < chemicalList.length; i++) {
                        const chemical = chemicalList[i];
                        const basicData = chemical.basic;

                        // 跳过已存在的 chemical_id
                        if (currentIds.includes(chemical.chemical_id)) continue;

                        // 当前的量
                        let currentMass = '';
                        if (basicData.current_mass) {
                            currentMass = `${basicData.current_mass}${basicData.mass_unit}`;
                        }

                        // 构造行内容
                        const chemicalRow = `
                                <tr data-chemical_id="${chemical.chemical_id}">
                                    <td style="width:30px;">
                                        <input type="checkbox" class="checkboxBtn">
                                    </td>
                                    <td class="incms_code" style="width:150px;">
                                        <div class="break-inline" style="max-width: 150px;" title="${basicData.incms_code}">
                                            <a href="${INCMS_URL}/chemical/view?chem_id=${chemical.chemical_id}" target="_blank">
                                                ${basicData.incms_code}
                                            </a>
                                        </div>
                                    </td>
                                    <td class="exp_code" style="width:120px;">
                                        <div class="break-inline" style="max-width: 120px;" title="${basicData.exp_code}">
                                            ${basicData.exp_code}
                                        </div>
                                    </td>
                                    <td class="current_mass" style="width:100px;">
                                        <div class="break-inline" style="max-width: 100px;">
                                            ${currentMass}
                                        </div>
                                    </td>
                                    <td>${basicData.user_name}</td>
                                    <td style="width:100px;">${basicData.submit_time}</td>
                                </tr>
                            `.trim();

                        // 将构造的行添加到表格中
                        $popupEle.find('table').append($(chemicalRow));
                    }
                });

                if (isBatch) {
                    $popupEle.find('.batch-search-cms-pop').hide();
                }
            });

            // 绑定事件：批量搜索
            $('body').on('click', '.search-cms-popup .batch-search-cms', function () {
                const $popupEle = $('.search-cms-popup'); // 当前弹窗
                $popupEle.find('.batch-search-cms-pop').show();
            });

            // 绑定事件：批量搜索确定
            $('body').on('click', '.search-cms-popup .batch-search-cms-confirm', function () {
                const $popupEle = $('.search-cms-popup'); // 当前弹窗
                $popupEle.find('.batch-search-cms-pop').show();
            });

            // 绑定事件：批量搜索关闭
            $('body').on('click', '.search-cms-popup .batch-search-cms-close', function () {
                const $popupEle = $('.search-cms-popup'); // 当前弹窗
                $popupEle.find('.batch-search-cms-pop').hide();
            });
        },

        getSelectedTdTableMap(titleDom, trDom) {
            var headers = [];
            var data = [];

            titleDom.each(function(index) {
                headers[index] = $(this).text();
            });

            var rowData = {};
            trDom.each(function() {
                $(this).children('td ').each(function(index) {
                    rowData[headers[index]] = $(this).text();
                });
            });
        },

        popInstrumentsPageFn:function () {
            var that = this;
            var pageBox = $('.instruments_pop_page .page_box');
            var page = that.pageData || 1;
            pageBox.pagination(pageBox.attr('data-num'), {
                select_page: [5, 10, 15, 20, 50, 100, 200, 500],
                prev_text: mainLang('prev_page'),
                next_text: mainLang('next_page'),
                num_edge_entries: 2,
                num_display_entries: 4,
                current_page: page-1,
                callback: function (page_index, jq) {

                    //获取查询条件和页数
                    var data = $.formSerializeFn($("#search-instrument-pop"));
                    data['page'] = page_index + 1;
                    data['limit']= $('.instruments_pop_page .pager-select:visible').val() || this.default_page_size || undefined;
                    data['needUpdateAllPage'] = 0;
                    data['type'] = 'fromBiology';

                    $.ajaxFn({
                        url: ELN_URL + '?r=instrument/get-pop-instruments-page',
                        data: data,
                        type: 'POST',
                        success: function (data) {
                            if (1 == data.status) {
                                $('.exp_conetnt.active ').removeClass('search');
                                $('.instruments_pop_table').html(data.data.file);
                                that.pageData = page_index + 1;
                                that.popInstrumentsPageFn();
                                $('.instruments_pop_page').scrollTop(0);

                                //对placeholder的兼容处理。
                                $('input[placeholder]:visible').placeholder();
                            }
                        }
                    });


                },
                items_per_page:  pageBox.attr('data-limit'),
                default_page_size: pageBox.attr('data-limit')
            });
        }
    };

    condationSearchAction.init();

    // 快捷方式
    var quickTab = {

        chooseData: {},

        inputType: null,

        dataType: null,

        inputObj: null,

        defSearch: '',

        mol: null, //$('.chendraw iframe')[0] ? $('.chendraw iframe')[0].contentWindow.indraw.getMultiMol() : '',

        //事件委派
        bindEvent: function () {
            var that = this;
            var time;
            var customType;

            $('body').on('expchange', function () {
                that.defSearch = '';
            });

            $('body').on('input propertychange', '.name_td [property="name"]', function () {
                var name_td = $(this).parents('.name_td');
                if ($('.exp_conetnt.active .search_smiles', name_td).length == 0) {
                    $(this).attr('isinput', '1');
                }
            });

            //快速添加
            $('body').on('click', '.quick_btn', function () {
                window.noLoadTip = true;
                that.getStructure();
            });

            //选中结构式
            $('body').on('click', '.choose_quick_btn', function () {
                var smiles = $(this).attr('smiles');
                var mol = $(this).find('textarea').val();
                var src = $(this).find('img').attr('src');
                that.chooseStructure(smiles, mol, src);
            });

            //搜索
            $('body').on('keyup', '.search_quick', function (event) { //, .name_td [property="name"]
                // 优化代码  zhhj at 2018-09-28
                // 回车才搜索
                if (event.keyCode != 13) {
                    return;
                }

                var obj = $(this);
                window.noLoadTip = true;
                if (obj.hasClass('added')) {
                    return;
                }

                var searchStr = obj.val().trim();
                if (searchStr == '') {
                    $('.filter_select_box').hide();
                    return;
                }
                if (that.defSearch == obj.val().trim()) {
                    $('.filter_select_box').show();
                    return;
                }

                that.mol = $('.exp_conetnt.active .chendraw iframe')[0] && $('.exp_conetnt.active .chendraw iframe')[0].contentWindow && $('.exp_conetnt.active .chendraw iframe')[0].contentWindow.indraw ? $('.exp_conetnt.active .chendraw iframe')[0].contentWindow.indraw.getMultiMol() : '';
                that.inputObj = obj;
                if (obj.hasClass('search_quick')) {
                    that.inputQuick = true;
                    $('.filter_select_box').removeClass('custom');
                } else {
                    that.inputQuick = false;
                    $('.filter_select_box').addClass('custom').removeClass('bottom_position').removeClass('top_position');
                    $('.filter_select_box').css({
                        'position': 'fixed',
                        'left': obj.offset().left,
                        'top': obj.offset().top - $.getScrollTop() + 30
                    });
                    that.dataType = $(this).parents('.tr_').attr('addtype');
                }
                that.searchQuick(obj);
            });

            /**
             * 回车时间
             */
            $('body').on('input property', '.search_quick_select', function (event) {
                var text = $(this).find('option:selected').text();
                var tip_text = '请输入 ' + text + ' 并回车确认，快速添加反应物、催化剂、溶剂、产物';
                $('.search_quick_wrap .search_quick').attr('placeholder', tip_text);
                var str = $(".search_quick").val();
                if (str != '') {
                    that.searchQuick($(this));
                }
//				alert(text);
            })

            $('body').on('click', '.search_quick', function () { //, .name_td [property="name"]
                that.mol = $('.exp_conetnt.active .chendraw iframe')[0] && $('.exp_conetnt.active .chendraw iframe')[0].contentWindow && $('.exp_conetnt.active .chendraw iframe')[0].contentWindow.indraw ? $('.exp_conetnt.active .chendraw iframe')[0].contentWindow.indraw.getMultiMol() : ''
                var obj = $(this);
                if (obj.hasClass('added')) {
                    return;
                }
                if (obj.val().trim() == '') {
                    $('.filter_select_box').hide();
                    return;
                }
                that.inputObj = obj;
                if (obj.hasClass('search_quick')) {
                    that.inputQuick = true;
                    $('.filter_select_box').removeClass('custom');
                } else {
                    that.inputQuick = false;
                    that.dataType = $(this).parents('.tr_').attr('addtype');
                }
                if (obj.val().trim() == '') {
                    $('.filter_select_box').hide();
                    return
                }
                if (that.defSearch != obj.val().trim()) {
                    that.searchQuick(obj);
                } else {
                    $('.filter_select_box').show();
                    that.setSearchPosition();
                }
            });
            //搜索很多delete_action

            $('body').on('keydown', '.search_quick', function () { //, .name_td [property="name"]
                time ? clearTimeout(time) : '';
            });

            //删除 快捷
            $('body').on('click', '.delete_quick', function () {
                that.deleteQuick($(this));
            });

            //编辑快捷方式
            $('body').on('click', '.edit_quick', function () {
                that.openEditQuick($(this));
            });

            //新建 修改提交快捷
            $('body').on('click', '#save_quick_submit', function () {
                that.createUpdateQick($(this));
            });
        },

        //调整快捷键 选择位置
        setSearchPosition: function () {
            var harfHei = $.getWindowH() / 2;
            var offsetT = $('.search_quick').offset().top + $('.search_quick').outerHeight() / 2 - $.getScrollTop();
            if (offsetT > harfHei) { //位于下半部
                $('.filter_select_box').addClass('custom').addClass('top_position').removeClass('bottom_position');
            } else { //位于上半部
                $('.filter_select_box').addClass('custom').addClass('bottom_position').removeClass('top_position');
            }

            /*
             $('.filter_select_box').css({
             'position': 'fixed',
             'left': this.inputObj.offset().left,
             'top': this.inputObj.offset().top - $.getScrollTop() + 30
             });
             */
        },

        deleteQuick: function (obj) {
            var id = obj.attr('data-id');
            $.showContent('warning', mainLang('confirm'), mainLang('confirm_del_fast'), function () {
                $.ajaxFn({
                    url: ELN_URL + '?r=chem/delete-defined&id=' + id,
                    success: function (data) {
                        if (data.status == 1) {
                            $.closeModal();
                            obj.parents('li.overhidden').remove();
                            $.showAlert(mainLang('del_success'));
                        }
                    }
                });
            })
        },

        openEditQuick: function (obj) {
            var obj = obj;
            var src = obj.parents('li').find('img').attr('src');
            $.ajaxFn({
                url: ELN_URL + '?r=chem/show-defined&id=' + obj.attr('data-id'),
                success: function (data) {
                    if (data.status == 1) {
                        if ($('.quick_edit_modal').length == 0) {
                            data.data.update = 'update';
                            data.data.src = src;
                            var temp = _.template(require('text!popup/quick_edit.html'), {variable: 'data'})(data.data);
                            $('body').append(temp);
                        }
                        $('.quick_edit_modal').modal('show');
                    }
                }
            });
        },

        createUpdateQick: function (obj) {
            var data = $.formSerializeFn($('.quick_detial'));
            var url = ELN_URL + '?r=chem/defined-name'; //新建

            if (obj.hasClass('update')) { //编辑
                url = ELN_URL + '?r=chem/update-name';
            }
            var upData = data;
            $.ajaxFn({
                url: url,
                data: data,
                success: function (data) {
                    if (data.status == 1) {
                        if (obj.hasClass('update')) {
                            $.showAlert(mainLang('update_success'));
                            //下面是针对词条页面。
                            var trIndex = $('.quick_edit_modal').attr('tr-index');
                            if (trIndex) {
                                $(".exp_conetnt.active .biology-word-table tr").eq(trIndex).find('td').eq(1).html(HtmlEncode(upData.name));
                            }
                        } else {
                            $.showAlert(mainLang('add_success'));
                            $.closeModal();
                            require('tab').reloadActiveTag(); //刷新当前标签页
                        }

                        if ($('.indraw_word_modal').is(":visible")) {
                            $(".quick_edit_modal").modal('hide');
                            $(".close_word_indraw").attr('torefresh', 'true');
                        } else {
                            $.closeModal();
                        }

                    }
                }
            })
        },

        // add by hkk 2019/11/6 新的获取结构式，可以区分组合后
        getStructure: function () {
            // 有两个入口会调用到这里: InDraw模块物料表->设置快捷标签 和 我的词库->InDraw->添加行->确定
            var indrawIframe = $('.exp_conetnt.active .chendraw iframe')[0];
            if (!indrawIframe) {
                indrawIframe = $('.modal-body .chendraw iframe')[0];
            }
            if (!indrawIframe) {
                return;
            }

            var indraw = indrawIframe.contentWindow.indraw;
            indraw.sel.clear();
            var groups = indraw.mergedStructureGroups(); // 获取分组信息
            if (groups.length === 0) {
                $.showAlert(mainLang('struct_empty'));
                return;
            }

            var html = '';
            if ($('.quick_find_modal').length == 0) {
                var temp = _.template(require('text!popup/quick_find.html'))();
                $('body').append(temp);
            }

            for (var i = 0; i < groups.length; i++) {
                indraw.sel.selectPart(groups[i]);
                var smiles = indraw.getSelOneMol();
                if (smiles === "c1cccc1.c1cccc1.C[Fe]C") { // add by hkk 2019/11/8
                    smiles = "[cH-]1cccc1.[cH-]1cccc1.[Fe]";
                }
                if (smiles === "c1cccc1") { // add by hkk 2019/11/8
                    smiles = "[cH-]1cccc1";
                }
                var intMol = indraw.mol.getSelPartialMol(1, 1, 0);
                var imageSrc = indraw.savePic3(indraw.sel.getSelectedObject(), 0, indraw.sel.getBBox()); // add by hkk 2019/12/5

                html += '<span class="iblock border_all choose_quick_btn" smiles="' + smiles + '"><img src="' + imageSrc + '" />' +
                    '<textarea class="hidden">' + intMol + '</textarea>' +
                    '</span>';

                indraw.sel.clear();
            }

            $('.quick_find_modal .choose_list').html(html);
            $('.quick_find_modal').modal('show');
        },


        chooseStructure: function (smiles, mol, src) {
            $.ajaxFn({
                url: ELN_URL + '?r=chem/get-chem-data',
                data: {
                    smiles: smiles
                },
                success: function (data) {
                    if (data.status == 1) {
                        $('.quick_find_modal').modal('hide');
                        var data_ = data.data || {};
                        data_.smiles = smiles;
                        data_.mol = mol;
                        data_.src = src;

                        // 在物料表中进行查找CAS，通过smiles匹配
                        $('.exp_conetnt.active .chendraw_data tr').each(function () {
                            var $tr = $(this);
                            var $inchiInput = $tr.find('input[property=inchi]');
                            if ($inchiInput.length !== 0 && $inchiInput.val() == data.data.inchi) {
                                var $casInput = $tr.find('input[property=cas]');
                                if ($casInput.length !== 0) {
                                    data_.cas = $casInput.val();
                                }
                                var $densityInput = $tr.find('input[property=dengsity]');
                                if ($densityInput.length !== 0) {
                                    data_.density = $densityInput.val();
                                }

                                return false; // 跳出each循环
                            }
                        });

                        if ($('.quick_edit_modal').length == 0) {
                            var temp = _.template(require('text!popup/quick_edit.html'), {variable: 'data'})(data_);
                            $('body').append(temp);
                        }
                        $('.quick_edit_modal').modal('show');
                    }
                }
            });
        }
    };
    quickTab.bindEvent();


    //粘贴 smiles 或 cdx 事件
    /*window.exportImport = {
     handleSmiles: function(data) {
     if (data.status == 1) {
     var mol = data.data.mol;
     var indraw = $('#detial_indraw')[0].contentWindow.indraw;
     if ($("[data-name='modalbox']", $('#detial_indraw')[0].contentWindow.indraw.doc).data("modalbox")) {
     $("[data-name='modalbox']", $('#detial_indraw')[0].contentWindow.indraw.doc).data("modalbox").close();
     }
     $('.chendraw iframe')[0].contentWindow.indraw.loadMultiMol(mol);
     indraw.isSameMol = false;
     };
     }
     };*/

    window.materielFn = materielFn;
    var calculationTime;



    $(function () {
        var dom = $('.exp_conetnt.active .chendraw_data .search_quick');
        dom.placeholder();
            });

    // 反应总路线
    $('body').on('click', '.view-reaction-routes', function () {
        $('.pop_modal .modal-dialog').addClass('modal-lg');
        var html = '<iframe allowfullscreen="" mozallowfullscreen="" webkitallowfullscreen="" id="reaction_routes_indraw" width="100%" height="525" src="/indraw/index.html?source=eln_reaction_routes"></iframe>';
        var pdf_button = '<div style="margin-top:10px;height:1px"><a href="javascript:;" class="relation-route-exp exp_pdf " data-type="pdf"><i style="position: initial; height: 24px; background: url(../image/eln.png?v=5555); background-position:  -267px -54px;transform: scale(0.8);"></i> PDF</a>        <a href="javascript:;" class="relation-route-exp exp_word" data-type="word"><i style="position: initial; height: 24px; background: url(../image/eln.png?v=5555); background-position:  -299px -54px;transform: scale(0.8);"></i> Word</a></div><div class="get_exp_ids"></div><div class="get_exp_ids" style="visibility: hidden"></div>';

        $.popContent(html + pdf_button, mainLang('reaction_route'), function () {

        }, function () {
            $('body').addClass('overFlowHidden')
            var sendDate1 = (new Date()).getTime();
            console.log('准备发请求')
            $.ajaxFn({
                url: ELN_URL + '?r=experiment/view-routes',
                type: 'GET',
                data: {
                    exp_id: $('.exp_conetnt.active #exp_id').val()
                },
                success: function (res) {
                    if (res.status == 1) {
                        function importReactionRoute() {
                            const routeWindow = $('#reaction_routes_indraw')[0].contentWindow;
                            var routesIndraw = $('#reaction_routes_indraw')[0].contentWindow.indraw;
                            if (routesIndraw) {
                                const data = res.data.routeArr
                                if (data.length == 1) {
                                    routesIndraw.importReaction(data[0]);
                                } else {
                                    routesIndraw.importReactions(data);
                                }
                                const expId = parseInt($('.exp_conetnt.active #exp_id').val());
                                const idArr = res.data.idArr;
                                for (var i = 0; i < idArr.length; i++) {
                                    if (idArr[i] == expId) {
                                        idArr.splice(i, 1);
                                        i--;
                                    }
                                }
                                idArr.unshift(expId);
                                $('.get_exp_ids')
                                    .data('ids', idArr)
                                    .data('operate_from', 'exp_route');

                                routeWindow.$(routeWindow).trigger('resize');//! [bug]#569, 手动触发indraw resize以触发画布滚动条 mod dx
                            } else {
                                setTimeout(importReactionRoute, 100);
                            }
                        }

                        importReactionRoute();
                    }
                },
                error: function () {
                    console.log('请求失败')
                }
            },null,true); // add by hkk zhj 2019/2/25 增加两个参数防止请求被拦截导致路线空白


        }, false);
    });

    // 创建下一步反应
    $('body').on('click', '.create-next', function () {
        //var indraw = document.getElementById('detial_indraw').contentWindow.indraw;
        var indraw = $('.exp_conetnt.active #detial_indraw')[0].contentWindow.indraw; // add by hkk 2020/7/28
        var intData = indraw.createNextReaction();
        if(!intData){ // add by hkk 2020/7/28 没有产物提示
            $.showAlert(mainLang('no_product_tip'));
            return
        }

        $.ajaxFn({
            url: ELN_URL + '?r=experiment/create-next',
            type: 'POST',
            data: {
                exp_id: $('.exp_conetnt.active #exp_id').val(),
                indraw_data: {
                    int: encodeURIComponent(intData), //! 为了避免后端xss过滤将mol文件中的html格式字符串过滤掉,先将mol原文encode ,bug#513, mod dx
                    smiles: '',
                }
            },
            success: function (res) {
                if (res.status == 1) {
                    $('.exp_conetnt.active').removeClass('search');
                    $('.my_exp_list').removeClass('on');

                  //  $('.my_exp_detial').removeClass('on'); // todo tag

                    var num = parseInt($('.my_exp_list .count').text());
                    $('.my_exp_list .count').text(num + 1);
                    $('.my_exp_list').attr('title', $.trim($('.my_exp_list').text()));

                 /*   window.detialExp.getDetial(null, res.data.id, null, null, null, function () {
                        var indraw = document.getElementById('detial_indraw').contentWindow.indraw;
                        if (!indraw) {
                            setTimeout(function () {
                                var indraw = document.getElementById('detial_indraw').contentWindow.indraw;
                                indraw.formatReaction(true);
                            }, 300)
                        }
                    });*/

                    // add by hkk 2020/6/22  走新路径
                    require(['get_html'], function (get_html) {
                        get_html.genExpPage(res.data.id)
                    })

                }
            }
        });
    });

    $('body').on('click', '.details-switch', function () {
        if ($(this).hasClass('show')) {
            $(this).removeClass('show');
            $('.exp_conetnt.active .details_tr').hide();
            $(this).attr('title',mainLang('collapse_tip2'))
        } else {
            $(this).addClass('show');
            $('.exp_conetnt.active .details_tr').show();
            $(this).attr('title',mainLang('collapse_tip1'))
        }
    });

    $('body').on('click', '.add-route-exp-btn-exp', function () {
        var type = $(this).data('type');
        var expNumArr = [];
        $('.exp-num-input').each(function () {
            var expNum = $.trim($(this).val());
            if (expNum != '') {
                expNumArr.push(expNum);
            }
        });
        $.ajaxFn({
            url: ELN_URL + '?r=experiment/export-pdf',
            type: 'POST',
            data: {
                exp_id: $('.exp_conetnt.active #exp_id').val(),
                exp_num_arr: expNumArr,
                from: 'exp_route',
                type: `${type}_single`,
            },
            success: function (res) {
                if (res.status === 1) {
                    var chooseId = res.data;
                    $('.get_exp_ids')
                        .data('ids', chooseId)
                        .data('operate_from', 'exp_route')

                    if (type === 'pdf') {
                        require('tool').pdf();
                    } else if (type === 'word') {
                        require('tool').word(null, chooseId);
                    }

                }
            }
        });
    });

    $('body').on('click', '.relation-route-exp', function () {
        var type = $(this).data('type');
        if (type === 'pdf') {
            require('tool').pdf();
        } else if (type === 'word') {
            require('tool').word(null, $('.get_exp_ids').data('ids'));
        }
    });

    // add by hkk 2019/3/20 设置物料表BatchNum下拉框

    $('body').off('click', '.batchNoSign').on('click', '.batchNoSign', function (event, materialParams) {


        var batchNoString = $(this).prev().val();
        var inventoryId = $(this).parents('tr').find('.wms_inventory_id').val();
        var batchNoArray ;
        var batchNoDom ;

        if (materialParams) {
            batchNoString = materialParams?.batchNum;
        }

        //获取当前BatchNo字符串
        //列表已存在，点击会删除当前列表
        const parentBox = $(this).parents('.modul_box');
        if (parentBox.children().last().data('string') === batchNoString) {
            parentBox.children().last().remove();
            return
        } else {
            $(".exp_conetnt.active .batchNoList").remove(); // 有可能存在其他行的列表，先删除
        }


        // 空字符串直接返回
        if(!batchNoString){
            return
        }

        batchNoArray =  batchNoString.split(/,|;|\s|；/); // 用逗号，中英文分号或空格分割字符串

        var liString = "";
        for (var i = 0; i < batchNoArray.length; i++) {
            var batchNo =  batchNoArray[i].trim(); //去掉前后空格
            if(!!batchNo){
                liString += '<li>' + batchNo + '</li>';
            }
        }

        batchNoDom = `<ul class="batchNoList" data-string="${batchNoString}" inventory_id-string="${inventoryId}">${liString}</ul>`;

        //父元素添加batchNo列表
       //$(this).parent().append(batchNoDom);

        parentBox.append(batchNoDom);
        const offset = $(this).parent().offset();
        const offsetParent =  parentBox.offset();
        const height = $(this).parent().outerHeight();
        $('.batchNoList').css('left',  `${offset.left - offsetParent.left}px`);
        $('.batchNoList').css('top',  `${offset.top - offsetParent.top + height }px`);
    });

    // add by hkk 2019/3/20 物料表BatchNo列表点击打开新实验
    $('body').on('click', '.batchNoList li', function(){

        var batchNum = $(this)[0].innerText.trim();
        if(!batchNum){return;}

        //var inventoryId = $(this).parents('tr').find('.wms_inventory_id').val();
        var inventoryId = $(this).parent("ul").attr("inventory_id-string");
        // batchNum以BN开头，认为是库存商品，打开库存系统
        if(batchNum.indexOf('BN') === 0) {
            if (inventoryId) {
                window.open(INVENTORY_URL + '?inventory_id=' + inventoryId + '&key=' + batchNum);
            }
            else {
                window.open(INVENTORY_URL + '?key=' + batchNum);
            }
            return;
        }
        // 实验页码
        if (batchNum.match(/(.*\d{6,}-\d{3})(.*)/)) {
            batchNum = batchNum.match(/(.*\d{6,}-\d{3})(.*)/)[1]
            $.ajaxFn({
                noLoad: true,
                url: ELN_URL + '?r=experiment/get-exp-id-by-exp-num',
                type: 'GET',
                data: {
                    exp_num: batchNum
                },
                success: function (res) {
                    if (res.status === 1) {
                        if (res.data) { // 获取到了非空id
                            //window.detialExp.getDetial(null, res.data); //打开新窗口
                            require(['get_html'], function (get_html) {
                                get_html.genExpPage(res.data)
                            });
                        } else {
                            $.showAlert(mainLang('exp_not_exist_tip'));
                            /*$.showAlert(mainLang('some_tip'));*/
                        }
                    }
                }
            });
            return;
        }

        // 检测是否是仪器 设备ID
        $.ajaxFn({
            noLoad: true,
            url: ELN_URL + '?r=instrument/get-instrument-id',
            data: {
                batch_number: batchNum,
            },
            success: function (res) {
                if (res.status == 1) {
                    var instrumentId = res.data.instrument_id;
                    if (instrumentId) {
                        require('eln_setting').getInstrumentPage(instrumentId, 'view');
                    }
                } else {
                    $.showAlert(mainLang('some_tip'));
                }
            }
        });
    })

    //add by hkk 2019/3/20 物料表BatchNo列表点击空白区域隐藏列表
    $(document).mouseup(function (e) {
        var con = $(".batchNoList,.batchNoSign");   // 设置目标区域
        if (!con.is(e.target) && con.has(e.target).length === 0) {
            $(".exp_conetnt.active .batchNoList").remove();
        }
    });

    // add by hkk 2019/3/27 实验页面更改所有新的input下拉框
    $("body").on("focus", "input[data-list$='options']", function () {
        // 判断其他下拉列表是否存在，存在则都隐藏
        if ($(".new-input-select-list")) {
            $(".new-input-select-list").css('display', 'none');
        }

        // 判断本输入框下拉列表是否已存在,存在则显示,
        if ($(this).next().length > 0) {
            $(this).next().css('display', 'block');
            $(".exp_conetnt.active .new-input-select-list").css("width", $(this).css('width'));//重新设定宽度
            return
        }

        // 获取对应的下拉列表选择器
        var listSelector = "#" + $(this).attr('data-list') + " option";
        if ($(listSelector).length === 0) {
            return;
        }

        // 生成下拉栏列表
        var liString = "";
        $(listSelector).each(function (index, element) {
            liString += '<li>' + $(element).val() + '</li>';
        });
        var listDom = '<ul class="new-input-select-list">' + liString + '</ul>';

        $(this).parent().append(listDom);

        //设定宽度为input框的宽度一致, 设置高度，超过高度会出现滚动条
        $(".exp_conetnt.active .new-input-select-list")
            .css("width", $(this).css('width'))
            .css("max-height", "300px");
    });

    //add by hkk 2019/3/27 绑定input下拉框 点击事件
    $('body').on('click', ".new-input-select-list li", function () {
        $(this).parent().prev().val($(this)[0].innerText);
        const $input = $(this).parent().prev();
        $input[0].dispatchEvent(new Event('input'))
        $(this).parent().css('display','none');

        const exitEditEvtName = window.EVENT_NAMES.MATERIAL.EXIT_EDIT;
        $input[0].dispatchEvent(new CustomEvent(exitEditEvtName))
    });

    //add by hkk 2019/3/27 点击另外区域 input下拉框隐藏
    $(document).mouseup(function (e) {
        var con = $(".new-input-select-list,input[data-list$='options']");   // 设置目标区域
        if (!con.is(e.target) && con.has(e.target).length === 0) {
            $(".exp_conetnt.active .new-input-select-list").css('display','none');
        }
    });

    // add by hkk 2019/4/22 禁用鼠标滚轮改变数字框
    $('body').on('focus', 'input[type=number]', function (e) {
        $(this).on('mousewheel.disableScroll', function (e) {
            e.preventDefault()
        })
    });

    $('body').on('blur', 'input[type=number]', function (e) {
        $(this).off('mousewheel.disableScroll')
    });

    // add by hkk 2020/6/19 新的EqRadio点击事件
    $('body').on('click', 'input[property="is_base"]', function (e) {
        $('.exp_conetnt.active input[property="is_base"]:checked').prop('checked',false); // 当前页面其他Eq取消选中
        $(this).prop('checked',true);
    });

    // add by hkk 2019/7/1 添加物料表detail模块的文件上传 2019/7/3 材料与仪器也走此处
    $('body').on('click', '.modul_line .detail_upload_file', function(event, addFileParams) {
        var input = $(this);

        if (null != addFileParams) {
            event.preventDefault();
            event.stopPropagation();//阻止冒泡
        }
        var addFileWidth;
        if (input.closest('td').hasClass('files')) {
            addFileWidth = "width: 100px";
        } else {
            addFileWidth = "width: 60px";
        }
        input.ajaxfileupload({
            action: ELN_URL + '?r=upload/upload-file',
            params: {
                type: '2'
            },

            validate_extensions: false,
            onComplete: function(res) {
                $.closeLoading();
                //edge浏览器下，即使下面的onstart return false了。依然会走到这里来。
                //会影响start中的判断提示。
                if (!res) {
                    return;
                }
                if (res.status !== 1) {
                    input[0].outerHTML = input[0].outerHTML;
                    $.showAlert(res.info || mainLang('upload_file_error'));
                    return;
                }
                window.noLoadTip = false;

                const uploadedFiles = res.data;
                if (addFileParams?.onComplete) {
                    addFileParams.onComplete(uploadedFiles);
                }

                //todo 多文件同时上传
                for(var i=0; i<res.data.length; i++) {
                    var fileData = res.data[i];
                    if (/\.xlsx/.test(fileData.file_name) || /\.xls/.test(fileData.file_name)) { // excel用intable预览
                        var fileHtml = '<input type="hidden" name="dep_path" value="' + fileData.dep_path + '" />' +
                            '<input type="hidden" name="save_name" value="' + fileData.save_name + '" />' +
                            '<input type="hidden" name="file_name" value="' + fileData.file_name + '" />' +
                            '<a class="preview-excel" style="flex: 1;text-align: center">' + fileData.file_name + '</a><br>';
                    } else {
                        var fileHtml = '<div class="img_bottom">' +
                            '<a class="preview_file" style="flex: 1;text-align: center" target="_blank" > ' + fileData.file_name + ' </a><br>' +
                            '<input type="hidden" name="dep_path" value="' + fileData.dep_path + '" />' +
                            '<input type="hidden" name="save_name" value="' + fileData.save_name + '" />' +
                            '<input type="hidden" name="file_name" value="' + fileData.file_name + '" />' +
                            '</div>';
                    }
                    var html = '<div style="text-align: left" class="single_detail_file"> <span style="display: flex;justify-content: flex-end" class="ref_file_part file_up_box">' + fileHtml +
                        '<a style="margin-left:5px"  class="_btn del_file del_ico" title="' + mainLang('del') + '"></a>' +
                        '<a class="download_ico" target="_blank" title="' + mainLang('download') + '" href=?r=download/file&path=' + fileData.dep_path + '&name=' + fileData.save_name + '&file_name=' + encodeURIComponent(fileData.file_name) + '&exp_id=' + expId + '></a>' +
                        '<input type="hidden" name="dep_path" value="' + fileData.dep_path + '" />' +
                        '<input type="hidden" name="save_name" value="' + fileData.save_name + '" />' +
                        '<input type="hidden" name="file_name" value="' + fileData.file_name + '" /></span></div>';
                    input.parents('.upload_file').next().append(html)
                }
                input.val('');// 清空已经上传完成的文件的内容

            },
            onStart: function(a) {

                var dom = this;

                if (dom[0].files && dom[0].files[0]) {
                    console.log(dom[0].files[0].size);
                    if (Math.ceil(dom[0].files[0].size / 1024) > 51200) {
                        $.showAlert(mainLang('file_too_big'));
                        setTimeout(function() {
                            $.closeLoading();
                        }, 100);
                        return false;
                    }
                }
                $.loading();
            },
            onCancel: function() {
                $.closeLoading();

                // 取消上传文件
                // addFileParams.onCancelCallback();

                console.log('onCancel');
            }
        });
    });

    require('components/in_material/in_material_events');

    // add by kk 2019/9/3  物料表列显示的弹出框
    $('body').on('click', '.hide_show_column', function () {

        // 添加物料表反应物催化剂栏html到弹出框
        var popHtml = '<div data-type="substrates" class="divSelector"><span class="columnTitleStyle">'+ mainLang('reactant_reagent') +'</span>' +
            '<span  class="add_column_icon add_extra_column" title="' + mainLang('add_column') +'"></span>' +
            '<ul>';
        $('.exp_conetnt.active .substrates_title td').each(function () {
            addExtraColumn($(this));
        });
        popHtml+= "</ul></div>";

        // 添加溶剂栏html
        popHtml += '<div data-type="solvent" class="divSelector"><span class="columnTitleStyle">'+ mainLang('solvent') +'</span>' +
            '<span  class="add_column_icon add_extra_column" title="' + mainLang('add_column') +'"></span>' +
            '<ul>';
        $('.exp_conetnt.active .solvent_title td').each(function () {
            addExtraColumn($(this));
        });
        popHtml+= "</ul></div>";

        // 添加条件栏html
        popHtml += '<div data-type="condition" class="divSelector"><span class="columnTitleStyle">'+ mainLang('condition') +'</span>' +
            '<span class="add_column_icon add_extra_column" title="' + mainLang('add_column') +'"></span>' +
            '<ul>';
        $('.exp_conetnt.active .condition_title td').each(function () {
            addExtraColumn($(this));
        });
        popHtml+= "</ul></div>";

        // 添加产物栏html
        popHtml += '<div data-type="product" class="divSelector"><span class="columnTitleStyle">'+ mainLang('product') +'</span>' +
            '<span class="add_column_icon add_extra_column" title="' + mainLang('add_column') +'"></span>' +
            '<ul>';
        $('.exp_conetnt.active .product_title td').each(function () {
            addExtraColumn($(this));
        });
        popHtml+= "</ul></div>";

        // 添加详情栏html
        popHtml += '<div data-type="details" class="divSelector"><span class="columnTitleStyle">'+ mainLang('indraw_details') +'</span>' +
            '<span class="add_column_icon add_extra_column" title="' + mainLang('add_column') +'"></span>' +
            '<ul>';
        $('.exp_conetnt.active .details_title td').each(function () {
            addExtraColumn($(this));
        });
        popHtml+= "</ul></div>";

        function addExtraColumn(obj){
            if(obj.attr('data-name')){ // 没有data-name属性 就不添加
                var isCheck = obj.hasClass('hide') ? "": "checked";
                // 含有添加的自定义列，要增加input框和删除按钮
                if(obj.hasClass('addedMaterialColumn')){


                    if(obj.attr("data-name") === 'cLogP'){
                         popHtml += '<li style="display: inline-block;margin-right: 20px" class="AddedDefinedColumn">' +
                            '<input type="checkbox" class="columnCheckbox" name="cLogP" ' + isCheck + ' />cLogP</li>';

                    }else if(obj.attr("data-name") === 'tPSA'){
                         popHtml += '<li style="display: inline-block;margin-right: 20px" class="AddedDefinedColumn">' +
                            '<input type="checkbox" class="columnCheckbox" name="tPSA" ' + isCheck + ' />tPSA</li>';

                    }else if(obj.attr("data-name").indexOf('unitPrice')!== -1){ // 单价 add by hkk 2020/5/7
                        popHtml += '<li style="display: inline-block;margin-right: 20px" class="AddedDefinedColumn">' +
                            '<input type="checkbox" class="columnCheckbox" name="'+ obj.attr("data-name") + '" ' + isCheck + ' />'+ mainLang('indraw_price') +'</li>';

                    }else if(obj.attr("data-name").indexOf('price')!== -1){ // 价格 add by hkk 2020/5/7
                        popHtml += '<li style="display: inline-block;margin-right: 20px" class="AddedDefinedColumn">' +
                            '<input type="checkbox" class="columnCheckbox" name="'+ obj.attr("data-name") + '" ' + isCheck + ' />'+ mainLang('total_price') +'</li>';
                    }else if(obj.attr("data-name") === 'product_mass_yield'){ // 质量收率 add by hkk 2020/5/7
                        popHtml += '<li style="display: inline-block;margin-right: 20px" class="AddedDefinedColumn">' +
                            '<input type="checkbox" class="columnCheckbox" name="'+ obj.attr("data-name") + '" ' + isCheck + ' />'+ mainLang('mass_yield') +'</li>';
                    }
                    else{
                         popHtml += '<li class="AddedDefinedColumn" style="display: inline-block;margin-right: 20px" >' +
                            '<input type="checkbox" class="columnCheckbox" ' + isCheck + '  name="' + obj.attr("data-name") + '"  />' +
                            '<input  class="extra_column_input" value="' + obj.attr("data-name") + '" name="' + obj.attr("data-name") + '" type="text"  />' +
                            '<div class="addSelectSign" > <span class="addColumnSign" title="' + mainLang('connect_dict') +'" > </span></div>' +
                            '<span class="deleteColumnSign"  ></span>' +
                            '</li>';
                    }


                }else{
                    var displayName = obj.attr("data-name").replace(/[A-Za-z0-9]+_/,'').replace('_',' ');
                    //modified by wy 2023/3/17
                    displayName = displayName.replace('bp','b.p.').replace('temperature',mainLang('temperature')).replace('time',mainLang('reaction_time')).replace('pressure',mainLang('pressure'))
                                            .replace('Batch No',mainLang('batch_no')).replace('Salt Eq',mainLang('salt_eq')).replace('Eq',mainLang('eq')).replace('Salt',mainLang('salt'))
                                            .replace('Purity',mainLang('purity')).replace('Source',mainLang('source')).replace('Ratio',mainLang('ratio')).replace('Theo Mass',mainLang('theo_mass'))
                                            .replace('Actual Mass',mainLang('actual_mass')).replace('Yield',mainLang('yield')).replace('Sample Id',mainLang('sample_id')).replace('Barcode',mainLang('indraw_barcode'))
                                            .replace('Risk Assessment',mainLang('risk_assessment')).replace('Comment',mainLang('indraw_comment')).replace('Files',mainLang('files')).replace('Mass',mainLang('mass'))
                                             .replace('gas',mainLang('protection_gas')).replace('heating',mainLang('heating')).replace('comment',mainLang('indraw_comment')).replace('cms code',mainLang('compound_no'));
                    popHtml += '<li style="display: inline-block;margin-right: 20px" >' +
                        '<input type="checkbox" class="columnCheckbox" name=' + obj.attr("data-name")+ ' ' + isCheck + ' />'+ displayName +'</li>';
                }
            }
        }

        // $.popContent(popHtml, mainLang('hide_show_column'), function () {
        //     $.closeModal();
        //     $('body').removeClass('overflowAuto')
        //     }, function () {
        //     $('body').addClass('overflowAuto')
        // },false);


    });

    // add by kk 2019/9/3  物料表列的显示和隐藏
    $('body').on('change', '.columnCheckbox', function () {
        var obj = $(this);
        // 获取对应物料表列需列选择器
        var type = obj.parents('.divSelector').attr('data-type');
        var selector = $(".exp_conetnt.active ."+ type +"_title [data-name='" +  obj.attr('name') + "']" +","+ ".exp_conetnt.active ."+ type +"_value [data-name='" +  obj.attr('name') + "']");
        if(type === 'substrates'){ // 催化剂也得选中
            selector = $(".exp_conetnt.active .substrates_title [data-name='" +  obj.attr('name') + "']"
                        +","+ ".exp_conetnt.active .substrates_value [data-name='" +  obj.attr('name') + "']"
                        +","+ ".exp_conetnt.active .catalysts_title [data-name='" +  obj.attr('name') + "']");
        }

        //  $("#" + type + "ColResize").colResizable({disable:true}); // 增加列前要重新设置列宽拖拽插件
        // 记录隐藏之前各个列的宽度
        if (obj.prop('checked')) {//隐藏列选中状态
            selector.removeClass('hide');
            $(".exp_conetnt.active ." + type + "ColResize").colResizable({disable: true}); // 增加列前要重新设置列宽拖拽插件 改为加宽度后重置列宽
            // 调整宽度，新增或显示宽度统一设置为100px, 原来的宽度平均减小
            var currentLength = ($(".exp_conetnt.active tr." + type + "_title td").length - $(".exp_conetnt.active tr." + type + "_title td.hide").length - 1);//除新增列当前展示列的个数
            var minusWidth = Math.ceil(100 / currentLength);//调整列时每列减小的宽度,给新列预留100px
            $(".exp_conetnt.active tr." + type + "_title td").each(function (index, item) {
                if (!$(this).hasClass('hide')) {
                    if ($(this).attr('data-name') !== obj.attr('name')) {
                        var newWidth = (parseFloat($(this).css('width')) - minusWidth) + 'px';
                        $(this).css('width', newWidth);
                    } else {//由于精度问题，新列并不能够恰好100px,因此将新列宽度作为自适应列
                        $(this).removeAttr('width'); // add by hkk 2021/7/30 防止显示列width = NAN%
                    }
                }
            })
            colResizable(".exp_conetnt.active ." + type + "ColResize");//重新启动插件
        } else {//隐藏列取消选中状态
            //提前保存隐藏列之前的宽度用于个列后续校正
            $(".exp_conetnt.active ." + type + "ColResize").colResizable({disable: true}); // 减小列前要重新设置列宽拖拽插件
            $(".exp_conetnt.active tr." + type + "_title td").each(function (index, item) {
                if (!$(this).hasClass('hide')) {
                    $(this).attr('data-oldWidth', parseFloat($(this).css('width')));
                }
            })
            //隐藏取消的列
            selector.addClass('hide');
            //校正列宽
            var currentShowNumber = ($(".exp_conetnt.active tr." + type + "_title td").length - $(".exp_conetnt.active tr." + type + "_title td.hide").length)//得到需要分配宽度的列的个数
            var fixWidth = Math.ceil((parseFloat(selector.css('width')) / currentShowNumber) * 100) / 100;//调整宽度时每列增加的宽度，保留两位小数
            var randomNumber = parseInt(Math.random() * currentShowNumber);//随机取一个列自适应调整宽度
            $(".exp_conetnt.active tr." + type + "_title td").each(function (index, item) {
                if (!$(this).hasClass('hide')) {
                    if(index===randomNumber){
                        $(this).removeAttr('width')//宽度自适应列
                    }else{
                        var newWidth = (parseFloat($(this).attr('data-oldWidth')) + fixWidth) + 'px';
                        $(this).css('width', newWidth);
                    }
                }
                $(this).removeAttr('data-oldWidth')
            })
            colResizable(".exp_conetnt.active ." + type + "ColResize");
        }
    });


    // add by kk 2019/9/16  收起物料表
    $('body').on('click', '.collapse_material_table', function () {
        const expId = $('.module_data_store', '.exp_conetnt.active').data('exp-id');

        const InMaterial = require('in-material')

        let showDetail;
        var obj = $('.exp_conetnt.active .collapse_material_ico');
        if (obj.hasClass('show')) {
            obj.removeClass('show');
            $('[name=in-material]', '.exp_conetnt.active').hide();

            $('.exp_conetnt.active .colResizeTool').hide();
            obj.attr('title',mainLang('collapse_material2'));
            showDetail = 2; // 隐藏物料表
        } else {
            obj.addClass('show');
            $('[name=in-material]', '.exp_conetnt.active').show();

            $('.exp_conetnt.active .colResizeTool').show();
            $('.exp_conetnt.active .colResizeTool').colResizable({disable:true}); // 增加列前要重新设置列宽拖拽插件
            colResizable('.exp_conetnt.active .colResizeTool');
            obj.attr('title',mainLang('collapse_material'));

            showDetail = 0;
        }
        InMaterial.$patchBaseData(expId, (baseData) => {
            baseData.show_details = showDetail;
        });
    });

    // add by kk 2019/9/3  物料表弹出框增加自定义列
    $('body').on('click', '.add_extra_column', function () {


        // 超过10列提示不能创建,tpsa和clogp已经占两列

        var exceedNumber =  ($(this).parents('.divSelector').attr('data-type') ==='condition') ? 10:12;
        if( $(this).parents('.divSelector').find('li.AddedDefinedColumn').length >= exceedNumber){
            $.showAlert(mainLang('exceed_tip'));
            return;
        }


        var toAddHtml = '<li class="AddedDefinedColumn" style="display: inline-block;margin-right: 20px" >' +
            '<input type="checkbox" class="columnCheckbox" name="" />' +
            '<input  class="extra_column_input" type="text"  />' +
            '<div class="addSelectSign" > <span class="addColumnSign" title="' + mainLang('connect_dict') +'" > </span></div>'+
            '<span class="deleteColumnSign"  ></span>'+
            '</li>';
        $(this).next().append(toAddHtml);
        $(this).next().find('li:last-child').find('.extra_column_input').focus()
        //$('.extra_column_input').focus();

    });

    // add by kk 2019/9/26  物料表弹出框自定义新增列input框失去焦点 不能为空和重复名字
    $('body').on('blur', '.extra_column_input', function () {

        var obj = $(this);
        if(obj[0].value.trim() ===""){
            $.showAlert(mainLang('input_tip'));
            obj[0].value = '';
            obj.focus();
            return false
        }
    });

    // add by kk 2019/9/3  物料表弹出框自定义列的 input change事件，改变物料表自定义列；
    $('body').on('change', '.extra_column_input', function () {

        var obj = $(this);

        // 重复值会提示
        var isRepeat = false;
        obj.parents('.divSelector').find('.extra_column_input').each(function (index, item) {
            if( item !== obj[0] &&  (item.value === obj[0].value )){
                $.showAlert(mainLang('input_tip'));
                isRepeat = true
                return false
            }
        })
        if(isRepeat){
            obj[0].value = '';
            obj.focus();
            return
        }
        // END

        // 判断物料表对应栏是否已存在对因列，不存在添加，已存在修改对应属性
        var type = obj.parents('.divSelector').attr('data-type');
        var selector = $(".exp_conetnt.active ."+ type +"_title [data-name='" +  obj.prev().attr('name') + "']");
        var valueSelector = $(".exp_conetnt.active ."+ type +"_value [data-name='" +  obj.prev().attr('name') + "']");

        if(selector.length > 0){
            selector[0].innerText = $(this)[0].value;   // 修改标题列名
            selector.attr('data-name',$(this)[0].value); // 修改标题列属性值
            valueSelector.attr('data-name',$(this)[0].value); // 修改值属性值

            //催化剂标题也属于substrates,也要修改属性名
            if(type ==='substrates'){
                $(".exp_conetnt.active .catalysts_title [data-name='" +  obj.prev().attr('name') + "']").attr('data-name',$(this)[0].value);
            }


        }else{

            // 物料表标题栏增加一列隐藏
            var titleHtml = '<td class="hide addedMaterialColumn" data-name="' + $(this)[0].value + '" >' + $(this)[0].value + '</td>';
            $(".exp_conetnt.active ."+ type +"_title").append(titleHtml);

            // 催化剂标题也属于substrates,也要增加列
            if(type ==='substrates'){
                var catalystsHtml = '<td class="hide addedMaterialColumn" data-name="' + $(this)[0].value + '" ></td>';
                $('.exp_conetnt.active .catalysts_title').append(catalystsHtml);
            }

            // 物料表对应所有值栏添加一列隐藏
            var valueHtml = '<td  class="hide"  data-name="' + $(this)[0].value + '">' +
                '<input type="text" data-id="" class="addedColumnValue" autocomplete="off" data-list = "" value =""></td>';
            $(".exp_conetnt.active ."+ type +"_value").append(valueHtml);

        }

        obj.attr('name',$(this)[0].value); // 更新属性名字
        obj.prev().attr('name',$(this)[0].value);// 更新属性名字

    });

    // add by kk 2019/9/3  物料表弹出框删除添加的自定义列
    $('body').on('click', '.AddedDefinedColumn .deleteColumnSign', function () {

        var type = $(this).parents('.divSelector').attr('data-type');
        var selector1 = $(".exp_conetnt.active ."+ type +"_title [data-name='" +  $(this).prev().prev().attr('name') + "']");
        var selector2 = $(".exp_conetnt.active ."+ type +"_value [data-name='" +  $(this).prev().prev().attr('name') + "']");

        selector1.remove();
        selector2.remove();
        if(type ==='substrates'){
            $(".exp_conetnt.active .catalysts_title [data-name='" +  $(this).prev().prev().attr('name') + "']").remove();
        }

        // 删除父节点
        $(this).parent('.AddedDefinedColumn').remove();

       // colResizable();// 从新调用列宽拖拽插件

    });

    // add by kk 2019/9/3  物料表弹出框自定义列关联企业词库选择框, 若有选中传入data-id值
    $('body').on('click', '.AddedDefinedColumn .addColumnSign, .ma-pop .ma-pop__dict-link', function (event, linkDictParams) {

        var obj = $(this).parent('.addSelectSign');
        // 如果有vue组件传入的选项容器字段, 就使用提供的元素作为放置选项的container
        if (null != linkDictParams?.optionContainer) {
            obj = $(linkDictParams.optionContainer);
        }


        var type = obj.parents('.divSelector').attr('data-type');
        var selector = $("."+ type +"_title [data-name='" + obj.prev().attr('name') + "']");


        // 选择节点存在，再点击就隐藏
        if(obj.find('.attach_dictionary_options').length > 0){
            if(obj.find('.attach_dictionary_options').css('display') !=='none'){
                obj.find('.attach_dictionary_options').css('display','none');
            }else{
                obj.find('.attach_dictionary_options').css('display','block');
            }
        }else{
            var html = "<div class='attach_dictionary_options z-index10'><p class ='dict_title'>"+  mainLang('from_company_dict')+"</p><ul>";
            for (var i = 0; i < dict_category.length; i++) {
                const isChecked = dict_category[i].id === linkDictParams.dict_id;
                // html +=   "<li data-id='" + dict_category[i].id + "' data-name='" + dict_category[i].name + "'>";
                html += `<li data-id="${dict_category[i].id}" data-name="${dict_category[i].name}" class="${isChecked ? 'chosenDictionaryValue' : ''}">`;
                html += `<input type="checkbox" name="dict_options" class="dict_options" ${isChecked ? 'checked' : ''}>`; // 选项的checkbox
                html += dict_category[i].name;

                // 生成悬浮显示关联词库的值，只显示前5个
                html +=   "<div class='suspend_dict_value'><p class='defineTriangleSign'></p>";
                var listArray = dict_category[i]['dict'];
                var liString = "";
                for (var j = 0; j < 5; j++) {
                    if(listArray[j]){
                        liString += '<p>' + listArray[j] + '</p>';
                    }
                }
                liString += '<p>' + '...' + '</p>';

                html += liString;
                html +=   "</div></li>";
            }
            html+= "</ul></div>";
            obj.append(html);

            // 第一次打开若存在对应物料表列 则要选中对应关联词库选项
            if(selector.length > 0){
                obj.find('.attach_dictionary_options li[data-id=' +  selector.attr('data-id') + ']').addClass('chosenDictionaryValue');
                obj.find('.attach_dictionary_options li[data-id=' +  selector.attr('data-id') + ']' +' .dict_options').prop('checked',true);
            }
        }


    });

    // add by kk 2019/9/3  物料表弹出框自定义列关联词库项li点击事件
    $('body').on('click', '.AddedDefinedColumn .attach_dictionary_options li,' +
        ' .ma-pop__dict-container--bind-event .attach_dictionary_options li', function (evt) {
        const $curLi = $(this);
        const curDictId = $curLi.attr('data-id');
        const $checkbox = $curLi.find('.dict_options');
        // 选项li的容器
        const $liContainer = $curLi.parents('.ma-pop__dict-container--bind-event');
        const liContainerElm = $liContainer[0];
        if (null == liContainerElm) {console.error(`Err: 企业词库选项id: ${curDictId}没有对应的container对象`); return}
        // checkbox的新状态
        const checkboxNewStatus = (() => {
            if ($curLi.is(evt.target)) {return !$checkbox.prop('checked');} // 点击的是li, 新的选中状态为checkbox的反状态
            return $checkbox.prop('checked');// 其他情况默认点击的是checkbox
        })();

        // 切换 选中/不选
        // 先重置所有的li和checkbox状态
        const lastChosenLis = $('li.chosenDictionaryValue', $liContainer);
        lastChosenLis.each(function (idx, liElm) {
            $('[name=dict_options]', $(liElm)).prop('checked', false);
            $(liElm).removeClass('chosenDictionaryValue');
        });

        $checkbox.prop('checked', checkboxNewStatus);
        // 如果是选中当前的li, 为li添加样式class
        if (checkboxNewStatus) {
            $curLi.addClass('chosenDictionaryValue');
        }

        // 触发自定义事件以向组件内传递值
        const eventName = window.EVENT_NAMES.MATERIAL.CHANGE_DICT_ID;
        const emptyDictId = window.MATERIAL_CONFIG.EMPTY_DICT_ID;
        liContainerElm.dispatchEvent(new CustomEvent(eventName, {
            detail: {
                new_checked: checkboxNewStatus,
                new_dict_id: checkboxNewStatus ? curDictId : emptyDictId,
            },
        }));


        return;


        if($(this).hasClass('chosenDictionaryValue')){
            $(this).removeClass('chosenDictionaryValue');
        }else{
            if($(this).parent().find('.chosenDictionaryValue').length > 0){
                $(this).parent('ul').find('.chosenDictionaryValue .dict_options').prop("checked",false);
                $(this).parent('ul').find('.chosenDictionaryValue').removeClass('chosenDictionaryValue');
            }
            $(this).addClass('chosenDictionaryValue')
        }

        // 判断是否打勾
        if($(this).hasClass('chosenDictionaryValue')){
            $(this).find('.dict_options').prop("checked",true)
        }else{
            $(this).find('.dict_options').prop("checked",false)
        }

        // 改变对应物料表title列的data-id 2019/9/20
        var obj = $(this).parents('.addSelectSign');
        var type = $(this).parents('.divSelector').attr('data-type');
        var selector = $(".exp_conetnt.active ."+ type +"_title [data-name='" + obj.prev().attr('name') + "']");

        if($(this).hasClass('chosenDictionaryValue')){
            selector.attr('data-id', $(this).attr('data-id'));
            if(type ==='substrates'){
                var selector2 = $(".exp_conetnt.active .catalysts_title [data-name='" + obj.prev().attr('name') + "']");
                selector2.attr('data-id',$(this).attr('data-id'));
            }
        }else{
            // 去掉物料表标题栏的data-id属性
            selector.attr('data-id', '0');
            if(type ==='substrates'){
                var selector2 = $(".exp_conetnt.active .catalysts_title [data-name='" + obj.prev().attr('name') + "']");
                selector2.attr('data-id','0');
            }
        }



    });

    // add by hkk 2019/9/20 物料表关联词库弹框点击另外区域隐藏下拉表
    $(document).mouseup(function (e) {
        var con = $(".attach_dictionary_options,.addColumnSign");   // 设置目标区域以外隐藏
        if (!con.is(e.target) && con.has(e.target).length === 0) {
            $(".attach_dictionary_options").css('display','none');
        }
    });

    //add by hkk 2019/9/11 物料表弹出框自定义列关联词库项显示选项 悬浮事件
    $('body').on("mouseover mouseout", ".attach_dictionary_options li", function (event) {

        // 计算悬浮框显示的位置 left和top
        if(event.type == "mouseover"){
            var left = $(this).parents('.attach_dictionary_options').width() + 'px';
            var top  = $(this)[0].offsetTop - $(this).parent('ul').scrollTop() - $(this).find('.suspend_dict_value').height() / 2 + 'px';
            $(this).find('.suspend_dict_value').css('left',left);
            $(this).find('.suspend_dict_value').css('top',top);
            $(this).find('.suspend_dict_value').css('display','block');

            // 三角形高度top设置
            $(this).find('.defineTriangleSign').css('top',$(this).find('.suspend_dict_value').height() / 2 + 'px');

        }else if(event.type == "mouseout"){
            $(this).find('.suspend_dict_value').css('display','none');
        }
    });

    //add by hkk 2019/9/11 物料表自定义列获取下拉框列表
    $("body").on("focus", "input.addedColumnValue, input[data-dict-id]", function (evt) {

        // 判断其他下拉列表是否存在，存在则都隐藏
        if($(".new-define-select-list")){
            $(".new-define-select-list").css('display','none');
        }

        const $curInput = $(this);
        const dictId = $curInput.data('dict-id');

        // 判断标题属性是否有data-id，有就生成下拉框
        var obj = $(this);
        var dataName = obj.parent().attr('data-name');
        var titleName = obj.parent().parent('tr');
        var selector;
        if (titleName.hasClass('substrates_value')) {
            selector = $(".exp_conetnt.active .substrates_title [data-name='" + dataName + "']");
        } else if (titleName.hasClass('solvent_value')) {
            selector = $(".exp_conetnt.active .solvent_title [data-name='" + dataName + "']");
        } else if (titleName.hasClass('condition_value')) {
            selector = $(".exp_conetnt.active .condition_title [data-name='" + dataName + "']");
        } else if (titleName.hasClass('product_value')) {
            selector = $(".exp_conetnt.active .product_title [data-name='" + dataName + "']");
        } else if (titleName.hasClass('details_value')) {
            selector = $(".exp_conetnt.active .details_title [data-name='" + dataName + "']");
        }


        //判断本输入框下拉列表是否已存在,存在则显示,
        if (obj.next().length > 0) {

            /*// 若data-id修改过则重新设置下拉列表
            if (obj.next().attr('data-id') !== selector.attr('data-id')) {
                obj.next().remove();// 删除旧的下拉列表

                if(selector.attr('data-id')!=="0"){
                    createListDom(obj, selector) // 生成新的下拉列表
                }

            }*/
            obj.next().css('display', 'block');
            console.log('list show')
            console.log(obj.next());
            $(".exp_conetnt.active .new-define-select-list").css("width", $(this).css('width'));//重新设定宽度

        }
        else {

            // 生成下拉列表并显示
            // if (selector && selector.attr('data-id') && selector.attr('data-id') !== '0') {
            //     createListDom(obj, selector)
            // }
            const emptyDictId = window.MATERIAL_CONFIG.EMPTY_DICT_ID;
            if (dictId && emptyDictId !== dictId) {
                createListDom(obj);
            }
        }

        function createListDom(obj,selector) {
            /*selector:对应title标题栏的选择器；obj:需要生成下拉框的节点*/

            // var listArray = dict_category[parseInt(selector.attr('data-id'))-1]['dict'];

            const dictId = obj.data('dict-id');
            const dictIdNum = Number.parseInt(dictId);
            const listArray = window.dict_category[Number.parseInt(dictIdNum - 1)]['dict'];


            var liString = "";
            for (var i = 0; i < listArray.length; i++) {
                liString += '<li>' + listArray[i] + '</li>';
            }
            var listDom = '<ul class="new-define-select-list" data-id="'+ dictId +'">'+ liString + '</ul>';

            obj.parent().append(listDom);

            //设定宽度为input框的宽度一致
            obj.parent().find(".new-define-select-list").css("width",obj.css('width'));

            //下拉选项超过10行，设置高度，超过高度会出现滚动条
            if(listArray.length > 10){
                obj.parent().find(".new-define-select-list").css("height","300px");
            }
        }

    });

    //add by hkk 2019/9/11 物料表绑定define 的 input下拉框 点击事件
    $('body').on('click', ".new-define-select-list li", function () {
        $(this).parent().prev().val($(this)[0].innerText);
        const $input = $(this).parent().prev();
        $input[0].dispatchEvent(new Event('input'));
        $(this).parent().css('display','none');

        const exitEditEvtName = window.EVENT_NAMES.MATERIAL.EXIT_EDIT;
        $input[0].dispatchEvent(new CustomEvent(exitEditEvtName))
    });

    // add by hkk 2019/9/11 物料表点击另外区域 define 的 input下拉框隐藏
    $(document).mouseup(function (e) {
        var con = $(".new-define-select-list,input.addedColumnValue,.added_define_item_part input, input[data-dict-id]");   // 设置目标区域 10/16加上自定义项
        if (!con.is(e.target) && con.has(e.target).length === 0) {
            $(".exp_conetnt.active .new-define-select-list").css('display','none');
        }
    });

    // add by hkk 2019/9/12 让物料表表列宽可拖动,增加后需要重新计算宽度
    function colResizable(selector){
        //$(selector).colResizable({disable:true});
        $(selector).colResizable({
            liveDrag:true,
            gripInnerHtml:"<div class='grip'></div>",
            draggingClass:"dragging",
            resizeMode:'fit'
        });
    };

    //add by hkk 2019/10/15 设置实验头部自定义项弹框
    $('body').on('click', ".set_define_item", function () {

        var popHtml =
            '<div data-type="substrates" class="divSelector">' +
                '<span class="defineItemTitleStyle">' + mainLang('set_define_item_tip1') + '</span>' +
                '<span  class="add_column_icon add_extra_define_item" title="' + mainLang('set_define_item_tip2') + '"></span>' +
                '<ul>';

        // 读取已经存在的自定义项
        $('.exp_conetnt.active .exp_info .added_define_item_part').each(function () {
            var $input = $(this).find('input');
            var title = $input.attr('data-title');
            var value = $input.val();
            var dict_id = $input.attr('data-id') ? $input.attr('data-id') : '';
            var is_require = $input.attr('data-is_require') ? $input.attr('data-is_require') : '0'; // 是否为必填项
            var is_struct = $input.attr('data-is_struct') ? $input.attr('data-is_struct') : '0'; // 是否为结构化数据项

            popHtml +=
                '<li class="addDefineItem" data-is_require=' + is_require + ' data-is_struct=' + is_struct + '>' +
                    '<input  class="toAddTitle" style="width: 300px;" placeholder="'+ mainLang('set_define_item_tip3') + '"  value="'+ title  + '" type="text"   />' +
                    '<input  class="toAddValue"  value="'+ value  + '" type="hidden"  />' +
                    '<div class="addSelectSign" data-id ="'+ dict_id +'" > <span class="addColumnSign"  title="' + mainLang('connect_dict') +'" > </span></div>' +
                    '<span class="deleteColumnSign"  ></span>' +
                '</li>';
        })

        popHtml+=
                "</ul>" +
            "</div>";


        $.popContent(popHtml, mainLang('set_define_item'), function () {
            // 检查是否有空自定义项字段值，有的话提示
            var hasEmpty = false;
            $('.addDefineItem .toAddTitle').each(function () {
                if($(this).val().trim()===""){
                    hasEmpty = true;
                    return false
                }
            })

            if(hasEmpty){
                $.showAlert(mainLang('empty_item'));
                return
            }

            // 先删除实验头部自定义项
            $('.exp_conetnt.active .exp_info').find('.added_define_item_part').remove();

            // 把设置的自定义项重新写到实验头部
            $('.addDefineItem').each(function () {
                var title = $(this).find('.toAddTitle').val();
                var value = $(this).find('.toAddValue').val();
                var dict_id = $(this).find('.chosenDictionaryValue').length == 1 ? $(this).find('.chosenDictionaryValue').attr('data-id'):
                                $(this).find('.addSelectSign').attr('data-id') ? $(this).find('.addSelectSign').attr('data-id'):'';
                var is_require = $(this).attr('data-is_require') ? $(this).attr('data-is_require') : '0'; // 是否为必填项
                var is_struct = $(this).attr('data-is_struct') ? $(this).attr('data-is_struct') : '0'; // 是否为结构化数据项

                var toAddHtml = '<div class="info_part iblock added_define_item_part" style="height:42px;padding: 5px 0px;">' +
                                    '<div style="float:left"><label class="define_item_label body_left" title="'+ title+ '"><span style="color:#999"></span>' + HtmlEncode(title) + '：</label></div>'+
                                    '<div style="float:left;padding-left: 5px;">' +
                                        '<input style="width: 220px;" type="text" autocomplete="off" data-id="' + dict_id + '" value="' + value + '" data-is_require="' + is_require + '" data-is_struct="' + is_struct + '" name="title" data-title="' + title + '" />' +
                                    '</div>'+
                                '</div> '

                $('.exp_conetnt.active .exp_info .set_define_item').parent().before(toAddHtml);
            });

            $.closeModal();

        }, null);

    });

    // add by kk 2019/10/15 自定义项关联企业词库弹框
    $('body').on('click', '.addDefineItem .addColumnSign', function () {

        var obj = $(this).parent('.addSelectSign');

        // 选择节点存在，再点击就隐藏，不存在就新增
        if(obj.find('.attach_dictionary_options').length > 0){
            if(obj.find('.attach_dictionary_options').css('display') !=='none'){
                obj.find('.attach_dictionary_options').css('display','none');
            }else{
                obj.find('.attach_dictionary_options').css('display','block');
            }
        }else{
            var html = "<div class='attach_dictionary_options'><p class ='dict_title'>"+ mainLang('from_company_dict')+"</p><ul>";
            for (var i = 0; i < dict_category.length; i++) {
                html +=   "<li data-id='" + dict_category[i].id + "' data-name='" + dict_category[i].name + "'>" +"<input type='checkbox' name='dict_options' class='dict_options'>";
                html +=    dict_category[i].name;

                // 生成悬浮显示关联词库的值，只显示前5个
                html +=   "<div class='suspend_dict_value'><p class='defineTriangleSign'></p>";
                var listArray = dict_category[i]['dict'];
                var liString = "";
                for (var j = 0; j < 5; j++) {
                    if(listArray[j]){
                        liString += '<p>' + listArray[j] + '</p>';
                    }
                }
                liString += '<p>' + '...' + '</p>';

                html += liString;
                html +=   "</div></li>";
            }
            html+= "</ul></div>";
            obj.append(html);


            // 第一次打开要选中值

            if(obj.attr('data-id')){
                obj.find('.attach_dictionary_options li[data-id=' +  obj.attr('data-id') + ']').addClass('chosenDictionaryValue');
                obj.find('.attach_dictionary_options li[data-id=' +  obj.attr('data-id') + ']' +' .dict_options').prop('checked',true);
            }


        }


    });

    // add by kk 2019/10/15 自定义项关联词库项li点击事件
    $('body').on('click', '.addDefineItem .attach_dictionary_options li', function () {

        if($(this).hasClass('chosenDictionaryValue')){
            $(this).removeClass('chosenDictionaryValue');
        }else{
            if($(this).parent().find('.chosenDictionaryValue').length > 0){
                $(this).parent('ul').find('.chosenDictionaryValue .dict_options').prop("checked",false);
                $(this).parent('ul').find('.chosenDictionaryValue').removeClass('chosenDictionaryValue');
            }
            $(this).addClass('chosenDictionaryValue')
        }

        // 判断是否打勾
        if($(this).hasClass('chosenDictionaryValue')){
            $(this).find('.dict_options').prop("checked",true)
        }else{
            $(this).find('.dict_options').prop("checked",false)
        }


    });

    // add by kk 2019/10/15 自定义项弹框新增新的自定义项
    $('body').on('click', '.add_extra_define_item', function () {


        // 超过10项提示不能创建新的自定义项
        if( $(this).next().find('li.addDefineItem').length >= 10){
            $.showAlert(mainLang('exceed_number'));
            return;
        }


        var toAddHtml = '<li class="addDefineItem" >' +
            '<input  class="toAddTitle" style="width: 300px;"   placeholder="'+ mainLang('set_define_item_tip3') + '" type="text"  />' +
            '<input  class="toAddValue"   value="" type="hidden"  />' +
            '<div class="addSelectSign" > <span class="addColumnSign" title="' + mainLang('connect_dict') +'" > </span></div>' +
            '<span class="deleteColumnSign"  ></span>' +
            '</li>';
        $(this).next().append(toAddHtml);
        $(this).next().find('li:last-child').find('input').focus()

    });

    // add by kk 2019/10/15 自定义项弹框删除自定义项
    $('body').on('click', '.addDefineItem .deleteColumnSign', function () {
        // 删除父节点
        $(this).parent('.addDefineItem').remove();

    });

    //add by hkk 2019/10/15 实验头部自定义项输入框获取下拉框列表(下拉表点击事件同物料表自定义列)
    $("body").on("focus", ".added_define_item_part input", function () {

        // 判断其他下拉列表是否存在，存在则都隐藏
        if($(".new-define-select-list")){
            $(".new-define-select-list").css('display','none');
        }

        var obj = $(this);

        //判断本输入框下拉列表是否已存在,存在则显示,
        if (obj.next().length > 0) {
            obj.next().css('display', 'block');
        }
        else {
            // 判断标题属性是否有data-id，有就生成下拉框
            if (obj.attr('data-id') && obj.attr('data-id') !== '0') {
                createListDom(obj)
            }
        }

        function createListDom(obj) {
            /*obj:需要生成下拉框的节点,有data-id属性*/
            var listArray = dict_category[parseInt(obj.attr('data-id'))-1]['dict'];
            var liString = "";
            for (var i = 0; i < listArray.length; i++) {
                liString += '<li>' + listArray[i] + '</li>';
            }
            var listDom = '<ul class="new-define-select-list" data-id="'+ obj.attr('data-id') +'">'+ liString + '</ul>';

            obj.parent().append(listDom);

            //设定宽度为input框的宽度一致
            obj.parent().find(".new-define-select-list").css("width",obj.css('width'));

            //下拉选项超过10行，设置高度，超过高度会出现滚动条
            if(listArray.length > 10){
                obj.parent().find(".new-define-select-list").css("height","300px");
            }
        }

    });

    // add by hkk 2019/11/22 实现实验页面所有input输入过滤下拉表
    $("body").on('input propertychange','input[data-list$="options"],input.addedColumnValue,.added_define_item_part input',function(){
        var inputValue = $(this).val();
        $(this).next('ul').find('li').each(function(index,item){
            if(inputValue && $(this).text().indexOf(inputValue) === -1){
                $(this).addClass('hidden')
            }else{
                $(this).removeClass('hidden')
            }
        });

    })

    // 获取单位
    function getUnit(type,value) {
        const ProductNUnitEnum = {
            1: 'μmol',
            2: 'mmol',
            3: 'mol',
            4: 'kmol',
        };
        const productMassUnitEnum = {
            1: 'mg',
            2: 'g',
            3: 'kg',
            4: 'ton',
        };

        var unit  = "";
        if(type === "product_Theo_Mass" || type==="product_Actual_Mass"){
            unit = productMassUnitEnum[value]
        }
        if(type === "product_N"){
            unit = ProductNUnitEnum[value]
        }
        return unit;
    }

    return {
        materielFn,
    }
});
