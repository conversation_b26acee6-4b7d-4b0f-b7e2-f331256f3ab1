<script setup lang="ts">
import '@ag-grid-community/styles/ag-grid.css';
import '@ag-grid-community/styles/ag-theme-quartz.css';
import "@src/asserts/styles/grid-global.css"
import type {InMaterialProps} from "@src/components/InMaterial/types.ts";
import {computed, onBeforeMount, onUnmounted, ref} from "vue";
import {ModuleRegistry} from '@ag-grid-community/core'
import {ClientSideRowModelModule} from '@ag-grid-community/client-side-row-model';
import GridRct from "@src/components/GridRct/GridRct.vue";
import GridRgn from "@src/components/GridRgn/GridRgn.vue";
import GridSol from "@src/components/GridSol/GridSol.vue";
import GridPrd from "@src/components/GridPrd/GridPrd.vue";
import GridCon from "@src/components/GridCon/GridCon.vue";
import GridDtl from "@src/components/GridDtl/GridDtl.vue";
import {useMaterialStore} from "@src/stores/material_store/material_store.ts";
import {DtlShowRowEnum} from "@src/entities/materialData/materialBaseDataDBSaveFmt.ts";
import { useSwitchPiniaOnMouseDown } from '@src/components/hooks/useSwitchPiniaOnMouseDown.ts'
import {deleteMaterialStore, setMaterialStore} from "@src/globals/global_exp_id_to_loaded_material_record.ts";

ModuleRegistry.registerModules([ ClientSideRowModelModule ]);

const props = defineProps<InMaterialProps>();

const inMaterialRef = ref();
const rctAgGrid = ref()
const rgnAgGrid = ref()

const material = useMaterialStore()


// 获取当前注册的pinia实例, 以实现当mousedown时, 切换到激活的pinia
useSwitchPiniaOnMouseDown(props.pinia, [inMaterialRef]);

const showMaterialGrid = computed(() => material.chemBaseData.show_details !== DtlShowRowEnum.HIDE_MATERIAL);
const showGridRct = computed(() => {
  return showMaterialGrid.value && material.tableConfig.rct.show;
});
const showGridRgn = computed(() => {
  return showMaterialGrid.value && material.tableConfig.rgn.show;
});
const showGridSol = computed(() => {
  return showMaterialGrid.value && material.tableConfig.sol.show;
});
const showGridCnd = computed(() => {
  return showMaterialGrid.value && material.tableConfig.con.show;
});
const showGridPrd = computed(() => {
  return showMaterialGrid.value && material.tableConfig.prd.show;
});
const showGridDtl = computed(() => {
  return showMaterialGrid.value && material.tableConfig.dtl.show;
});


onBeforeMount(() => {
  // 加载完成, 添加内容加载完成的标记
  setMaterialStore(props.expId, props.materialStore);
});
onUnmounted(() => {
  // 关闭物料表, 需要删除记录
  deleteMaterialStore(props.expId);
});
</script>

<template>

  <div
      ref="inMaterialRef"
      class="in-material-container"
      style="width: 100%; height: fit-content;"
      @mousedown="ev => console.log('mouse-down-on-grid', ev)"
  >
    <!--反应物-->
    <GridRct
        v-show="showGridRct"
        ref="rctGridRef"
        v-model:ag-grid="rctAgGrid"
        :aligned-grids="() => [rgnAgGrid]"
    ></GridRct>

    <!--试剂-->
    <GridRgn
        v-show="showGridRgn"
        ref="rgnGridRef"
        v-model:ag-grid="rgnAgGrid"
        :aligned-grids="() => [rctAgGrid]"
    ></GridRgn>

    <!-- 溶剂 -->
    <GridSol
        v-show="showGridSol"
        ref="refGridSol"
    ></GridSol>

    <!-- 反应条件 -->
    <GridCon
        v-show="showGridCnd"
        ref="refGridCon"
    ></GridCon>

    <!-- 产物 -->
    <GridPrd
        v-show="showGridPrd"
        ref="refGridPrd"
    ></GridPrd>

    <!-- 反应明细 -->
    <GridDtl
        v-show="showGridDtl"
        ref="refGridDtl"
    ></GridDtl>

  </div>
</template>

<style scoped>
</style>