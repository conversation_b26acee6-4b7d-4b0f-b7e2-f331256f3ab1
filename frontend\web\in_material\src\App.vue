<script setup lang="ts">
import { defineAsyncComponent, ref } from "vue";
import {createMaterialFromDatabase} from "@src/middlewares/v1/material/material.ts";
import {useMaterialStore} from "@src/stores/material_store/material_store.ts";
import {setMaterialStore} from "@src/globals/global_exp_id_to_loaded_material_record.ts";
import {useWatchColConfig} from "@src/hooks/watch_col_config.ts";
import {AppProps} from "@src/App-types.ts";
import {DatabaseMaterial} from "@src/middlewares/v1/material/material_types.ts";
import {
  createMaterialBaseDataByDatabaseMaterial,
} from "@src/entities/materialData/materialBaseData.ts";
import { InMaterialProps } from '@src/components/InMaterial/types.ts'

const props = defineProps<AppProps>();

// 物料表store
const materialStore = useMaterialStore();

// 对物料表对象的引用
const inMaterialRef = ref();

// 每个App实例只会初始化一次, 故不用使用响应式数据
const databaseMaterial = props.material as DatabaseMaterial;

const _materialBaseData = createMaterialBaseDataByDatabaseMaterial(databaseMaterial);
// 构造物料表前先初始化物料基础数据, 防止初始化物料数据时基础数据不正确
materialStore.$patch((state) => {
  Object.assign(state.base_data, _materialBaseData);
});
// 创建物料表store的数据对象
const material = createMaterialFromDatabase(props.expId, databaseMaterial);
/**
 * 这里更新状态需要通过回调方式更新,
 * 如果通过materialStore.$patch({...materialData})的方式更新状态会导致多个实例之间的状态被共享的问题
 */
materialStore.$patch((state) => {
  state = Object.assign(state, material);
});
setMaterialStore(props.expId, materialStore);

// 监听对column config数组的修改
useWatchColConfig();


// 使用异步组件, 保证物料表相关的操作一定在Store创建完毕后执行
const InMaterial = defineAsyncComponent(() => import("@src/components/InMaterial/InMaterial.vue"));

const inMaterialProps: InMaterialProps = {
  expId: materialStore.exp_id,
  materialStore: materialStore,
  pinia: props.pinia,
}
</script>

<template>
  <InMaterial ref="inMaterialRef" v-bind="inMaterialProps"></InMaterial>
</template>