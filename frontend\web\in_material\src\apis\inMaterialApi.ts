import {createMaterialFromUpdate} from "@src/middlewares/v1/material/material.ts";
import {createPinia} from "pinia";
import {createApp} from "vue";
import App from "@src/App.vue";
import {
  getMaterialStore,
  isExpMaterialStoreLoaded,
  setMaterialStore
} from "@src/globals/global_exp_id_to_loaded_material_record.ts";
import {MaterialBaseData} from "@src/entities/materialData/materialBaseData.ts";
import {initDecimalConfig} from "@src/middlewares/v1/material_row_field_handlers/decimal_config_handler.ts";
import {
  fmtMaterialDisplay,
  fmtMaterialUpdate,
  getMaterialFormatDatabase
} from "@src/middlewares/v1/material/getMaterialFormatted.ts";
import {DatabaseMaterial, MaterialTextInsertFmt, MaterialUpdateFmt} from "@src/middlewares/v1/material/material_types.ts";
import {updateDtlRowsUid} from "@src/stores/table_stores/store_util.ts";
import {useDtlStore} from "@src/stores/table_stores/dtl_store.ts";
import {userUpdateInDraw} from "@src/calculations/calc_user/userUpdateInDraw.ts";
import {cloneDeep} from "lodash-es";
import {
  isUserCreateNextUpdateInDraw,
  userCreateNextUpdateInDraw
} from "@src/calculations/calc_user/userCreateNextUpdateInDraw.ts";
import {AppProps} from "@src/App-types.ts";
import {UpdateDtlSrcEnum} from "@src/stores/table_stores/store_util_types.ts";
import {IResult} from "@src/utils/utilTypes.ts";
import {GetMaterialErr} from "@src/constants/enums/material_err.ts";

// 对外暴露内部api
export {createMaterialFromDatabase} from "@src/middlewares/v1/material/material.ts";
export {getMaterialFormatDatabase} from "@src/middlewares/v1/material/getMaterialFormatted.ts";

/**
 * 生成物料表
 * @param element
 * @param ctx
 */
export function createMaterial(element: string | Element, ctx: {
  expId: number,
  material: any,
  decimalConfig: any,
}) {
  // 初始化小数点设置
  initDecimalConfig(ctx.decimalConfig)

  const materialClone = cloneDeep(ctx.material)

  const pinia = createPinia()
  const app = createApp(App, {
    expId   : Number(ctx.expId),
    material: materialClone,
    pinia: pinia,
  } as AppProps)
  app.use(pinia)
  app.mount(element);
}

/**
 * 刷新inDraw更新物料表数据
 * @param expId
 * @param materialData
 */
export function updateMaterial(expId: number | string, materialData: DatabaseMaterial) {
  const materialStore = getMaterialStore(expId)
  if (null == materialStore) {
    console.error(`实验id: ${expId} 没有对应的store对象`)
    return null
  }

  const data = createMaterialFromUpdate(Number(expId), materialData)
  // const materialDataStore = useMaterialStore()
  const materialDataStore = materialStore
  setMaterialStore(expId, materialDataStore)

  materialDataStore.$patchInSetTimeout((state) => {
    const baseData = materialData?.base_data
    // 确保传入的基础数据不为空
    if (baseData && typeof baseData === 'object' && Object.keys(baseData).length > 0) {
      state.base_data = data.base_data
    }
  }, 0)
  materialDataStore.$patchInSetTimeout((state) => {
    data.rct && (state.rct = data.rct)
  }, 0)
  materialDataStore.$patchInSetTimeout((state) => {
    data.rgn && (state.rgn = data.rgn)
  }, 0)
  materialDataStore.$patchInSetTimeout((state) => {
    data.sol && (state.sol = data.sol)
  }, 0)
  materialDataStore.$patchInSetTimeout((state) => {
    data.con && (state.con = data.con)
  }, 0)
  materialDataStore.$patchInSetTimeout((state) => {
    data.prd && (state.prd = data.prd)
  }, 0)
  // 更新反应明细
  setTimeout(() => {
    updateDtlRowsUid({update_dtl_src: UpdateDtlSrcEnum.UPDATE_DTL});
    const dtlStore = useDtlStore();
    dtlStore.$updateTBDtl();
  }, 0)

  // 触发新添加物质的计算
  setTimeout(() => {
    // 如果是创建的下一步反应后更新画布, 需要触发一次物料表整体计算
    if (isUserCreateNextUpdateInDraw()) {
      userCreateNextUpdateInDraw();
      return;
    }
    userUpdateInDraw();
  }, 3)

  return true
}

/**
 * 获取更新物料表的物料数据
 * @param expId
 */
export function getMaterialFormatUpdate(expId: number | string): MaterialUpdateFmt | null {
  const materialStore = getMaterialStore(expId)
  if (null == materialStore) {
    console.error(`实验id: ${expId} 没有对应的store对象`)
    return null
  }
  return fmtMaterialUpdate(materialStore)
}
window.getMaterialFormatUpdate = getMaterialFormatUpdate

/**
 * 获取数据库格式的物料数据
 * @param expId
 */
export function getMaterialFormatDB(expId: number | string): IResult<DatabaseMaterial | null, GetMaterialErr | null> {
  // 查看物料表是否已经加载完成
  const isMaterialLoaded = isExpMaterialStoreLoaded(expId);
  if (!isMaterialLoaded) {
    console.info(`实验: ${expId}, 物料表未加载完成`);
    return [null, GetMaterialErr.MaterialNotLoaded];
  }
  const materialDt = getMaterialStore(expId)
  if (null == materialDt) {
    console.error(`实验: ${expId} 没有对应的store对象`)
    return [null, GetMaterialErr.MaterialStoreErr];
  }
  const materialFormatDB = getMaterialFormatDatabase(materialDt);
  return [materialFormatDB, null];
}
window.getMaterialFormatDB = getMaterialFormatDB

/**
 * 获取物料表显示用的物料表数据格式
 * @param expId
 */
export function getMaterialFormatDisplay(expId: number | string): MaterialTextInsertFmt | null {
  const materialStore = getMaterialStore(expId)
  if (null == materialStore) {
    console.error(`实验id: ${expId} 没有对应的store对象`)
    return null
  }
  return fmtMaterialDisplay(materialStore)
}
window.getMaterialFormatDisplay = getMaterialFormatDisplay

/**
 * 获取调试模式的物料表信息
 * @param expId
 */
export function getMaterialFormatDebug(expId?: number | string): MaterialTextInsertFmt | null {
  const __expId = (() => {
    if (null != expId) return expId;
    if (typeof jQuery !== 'undefined') {
      const _expIdAttr = $('.module_data_store', '.exp_conetnt.active').data('exp-id');
      return Number(_expIdAttr);
    }
    return 0;
  })();

  const materialStore = getMaterialStore(__expId);
  if (null == materialStore) {
    console.error(`实验id: ${expId} 没有对应的store对象`)
    return null
  }
  return fmtMaterialDisplay(materialStore);
}
window.getMaterialFormatDebug = getMaterialFormatDebug;


/**
 * 提供给外界调用
 * @param expId
 */
export function getBaseData(expId: number | string): MaterialBaseData | null {
  const store = getMaterialStore(expId);
  if (null == store) {
    console.error(`实验id: ${expId} 没有对应的pinia对象`)
    return null
  }

  return cloneDeep(store.base_data)
}

export function $patchBaseData(expId: number | string, patchCB: (baseDataState: MaterialBaseData) => void) {
  const store = getMaterialStore(expId);
  if (null == store) {console.error(`实验id: ${expId} 没有对应的pinia对象`); return null}

  store.$patch((state) => {
    patchCB(state.base_data)
  })
}