<?php
namespace frontend\interfaces;

use common\components\CommonCurl;
use frontend\core\CommonServer;
use frontend\models\ElnRoleModel;
use frontend\models\ExpUnlockLog;
use frontend\models\InstrumentBindingFileServerModel;
use frontend\models\LoginLogModel;
use frontend\models\UserGroupRole;
use yii;
use yii\helpers\ArrayHelper;
use yii\helpers\Curl;


/**
 *
 * <AUTHOR> @copyright 2016-11-8
 */
class CenterInterface extends CommonServer{
    public $_centerInterfaceUrl = '';
    public function init(){
        $this->_centerInterfaceUrl = CENTER_URL . 'interface/';
    }

    /**
     * 验证密码是否正确
     *
     * <AUTHOR> @copyright 2016-11-8
     * @param int $userId
     * @param int $password
     * @return boolean
     */
    public function checkPwd($userId, $password, $account = '',$insertLog = 0) {
        $url = $this->_centerInterfaceUrl . 'check-pwd';

        $result = (new CommonCurl())->sendPostCurl($url, [
            'user_id' => $userId,
            'md5_pwd' => $password,
            'account' => $account
        ]);

        $checkRes = isset($result['status']) && (1 == $result['status']);

        // 添加日志
        if($insertLog){
            $expUnlockLog = new ExpUnlockLog();
            $expUnlockLog->user_id = $userId;
            $expUnlockLog->log_ip = $_SERVER['REMOTE_ADDR'];
            $expUnlockLog->result = $checkRes ? 1 : 2;
            $expUnlockLog->save();
        }

        return $checkRes;
    }

    /**
     * Notes: 验证账户密码
     * Author: hkk
     * Date: 2020/7/21 17:30
     * @param $userId
     * @param $password
     * @param string $account
     * @return bool
     */
    public function checkAccountPassword( $account,$password){
        $url = $this->_centerInterfaceUrl . 'check-account-password';
        $result = (new CommonCurl())->sendPostCurl($url, ['password'=>$password, 'account'=>$account]);

        if(isset($result['status']) && (1 == $result['status'])){

            return $result['data'];
        }

        return FALSE;
    }

    /**
     * 获取指定用户的eln有效鹰群
     * 对应老的$this->groupService('eln.listGroup')
     *
     * <AUTHOR> @copyright 2016-11-8
     *
     *   [0] => Array
        (
            [group_id] => 1023
            [user_id] => 13234
            [master_id] => 13234
            [master_name] => 管理员
            [member_count] => 9
            [group_code] => **********
            [group_name] => mm
        )
     */
    public function elnGroupListByUserId($userId, $companyId=0){
        $cacheKey = '/frontend/interfaces/CenterInterface/elnGroupListByUserId/' . json_encode([$userId, $companyId]);

        // 不使用缓存 bug36413
        // $cacheValue = \Yii::$app->redis->get($cacheKey);
        //
        // if ($cacheValue)
        // {
        //     return json_decode($cacheValue, true);
        // }

        $url = $this->_centerInterfaceUrl . 'list-user-groups';

        $result = (new CommonCurl())->sendPostCurl($url, ['user_id'=>$userId, 'app_id'=>\Yii::$app->params['eln_apply_id'], 'company_id' => $companyId]);
        $result['data'] = !empty($result['data']) ? $result['data'] : [];


        $groupIds = array_column($result['data'], 'group_id');
        // 一次性查询数据库获取所有符合条件的 group_id 和 role_id 为 3 的数据
        $groupManageData = UserGroupRole::findAll(['group_id' => $groupIds, 'role_id' => 3]);

        // 重新组织数据以便于匹配 group_id
        $groupManageIndexed = [];
        foreach ($groupManageData as $groupData) {
            $groupId = $groupData['group_id'];
            if (!isset($groupManageIndexed[$groupId])) {
                $groupManageIndexed[$groupId] = [];
            }
            $groupManageIndexed[$groupId][] = $groupData;
        }

        // 将获取的管理员数据与原始数据进行匹配
        foreach ($result['data'] as $key => $value) {
            $groupId = $value['group_id'];
            $result['data'][$key]['manager'] = isset($groupManageIndexed[$groupId]) ? $groupManageIndexed[$groupId] : [];
        }

        if(isset($result['status']) && (1 == $result['status'])){
            \Yii::$app->redis->setex($cacheKey, \Yii::$app->params['center_api_cache_timeout'], json_encode($result['data']));

            return $result['data'];
        }

        return [];
    }

    /**
     * Note: 根据用户id获取所在鹰群(不包含已解散或已被踢出的)
     * @author: szq
     * @date: 2021/6/11 17:14
     *
     * @param $userId
     * @return mixed
     */
    public function listGroupsByUserId($userId) {
        $url = $this->_centerInterfaceUrl . 'list-groups-by-user-id';
        $result = (new CommonCurl())->sendPostCurl($url, ['user_id' => $userId]);
        return $result['data'];
    }

    /**
     * Note: 根据用户id获取用户为群主鹰群
     * @author: jiangdm
     * @date: 2021/7/15
     *
     * @param $userId
     * @return mixed
     */
    public function listMasterGroupsByUserId($userId) {
        $url = $this->_centerInterfaceUrl . 'list-master-groups-by-user-id';
        $result = (new CommonCurl())->sendPostCurl($url, ['user_id' => $userId]);
        return $result['data'];
    }

   /**
    * 获取指定鹰群下开通ELN的成员列表
    *
    * 对应老的$this->groupService('eln.listMember')
    *
    * <AUTHOR> @copyright 2016-11-8
    * @param int $groupId
    * @return Ambigous <>|NULL
    * [0] => Array
        (
            [member_id] => 2744
            [email] => <EMAIL>
            [phone] =>
            [real_name] => 管理员
            [nick_name] =>
            [user_id] => 13234
        )
    */
    public function elnMemberListByGroupId($groupId){
        $url = $this->_centerInterfaceUrl . 'list-apply-group-member';

        $result = (new CommonCurl())->sendPostCurl($url, ['group_id'=>$groupId, 'app_id'=>\Yii::$app->params['eln_apply_id']]);

        if(isset($result['status']) && (1 == $result['status'])){
            $returnData = [];

            if(!empty($result['data'])){
            	foreach ($result['data'] as $data){
            	    if(!empty($data)){
                		$data['real_name'] = !empty($data['real_name']) ? $data['real_name'] : (!empty($data['nick_name']) ? $data['nick_name'] : (!empty($data['name']) ? $data['name'] : ''));
                		$returnData[] = $data;
            	    }
            	}
            }


            return $returnData;
        }

        return [];
    }

    /**
     * 根据鹰群号获取鹰群id
     *
     * <AUTHOR> @copyright 2016-11-8
     *
     *  [id] => 1023
        [user_id] => 13234
        [master_id] => 13234
        [master_name] => 管理员
        [member_count] => 9
        [group_code] => **********
        [group_name] => mm
        [country] =>
        [province] =>
        [city] =>
        [detail_address] =>
        [group_type] => 1
        [company_id] =>
        [company_name] =>
        [part_name] =>
        [approval_status] => 1
        [img] =>
        [status] => 1
        [create_time] => 2015-05-22 19:03:01
        [update_time] => 2016-11-08 10:36:30
     */
    public function getGroupByGroupCode($groupCode){
        $url = $this->_centerInterfaceUrl . 'get-group-by-code';

        $result = (new CommonCurl())->sendPostCurl($url, ['group_code'=>$groupCode]);

        if(isset($result['status']) && (1 == $result['status']) && !empty($result['data'][0])){
            return $result['data'][0];
        }

        return [];

    }

    /**
     * 根据编号获取 鹰群列表
     *
     * <AUTHOR> @copyright 2016-11-9
     * @param array $groupCodes
     * @return Ambigous <>|NULL
     * Array
        (
            [0] => Array
                (
                    [id] => 1023
                    [user_id] => 13234
                    [master_id] => 13234
                    [master_name] => 管理员
                    [member_count] => 9
                    [group_code] => **********
                    [group_name] => mm
                    [country] =>
                    [province] =>
                    [city] =>
                    [detail_address] =>
                    [group_type] => 1
                    [company_id] =>
                    [company_name] =>
                    [part_name] =>
                    [approval_status] => 1
                    [img] =>
                    [status] => 1
                    [create_time] => 2015-05-22 19:03:01
                    [update_time] => 2016-11-08 10:36:30
                )

        )
     *
     */
    public function listGroupByGroupCodes($groupCodes){
        $url = $this->_centerInterfaceUrl . 'get-group-by-code';

        $result = (new CommonCurl())->sendPostCurl($url, ['group_code'=>$groupCodes]);

        if(isset($result['status']) && (1 == $result['status'])){
            return $result['data'];
        }

        return [];

    }

    /**
     * 根据鹰群id获取指定鹰群的资料
     * 对应老的$this->groupService('eln.getGroup')
     *
     * <AUTHOR> @copyright 2016-11-8
     * @param array|int $groupId
     * @return array
     */
    public function groupByGroupIds($groupIds,$status=0){
        $cacheKey = '/frontend/interfaces/CenterInterface/groupByGroupIds/' . json_encode($groupIds);

        $cacheValue = \Yii::$app->redis->get($cacheKey);

        if ($cacheValue)
            return json_decode($cacheValue, true);

        $url = $this->_centerInterfaceUrl . 'list-group-by-ids';

        if(!is_array($groupIds)){
            $groupIds = [$groupIds];
        }

        $result = (new CommonCurl())->sendPostCurl($url, ['group_ids'=>$groupIds,'status'=>$status]);

        if(isset($result['status']) && (1 == $result['status'])){
            \Yii::$app->redis->setex($cacheKey, \Yii::$app->params['center_api_cache_timeout'], json_encode($result['data']));

            return $result['data'];
        }

        return [];
    }

    /**
     * 根据鹰群id获取鹰群信息
     *
     * <AUTHOR> @copyright 2016-11-9
     * @param int $groupId
     * @return string|multitype:
     * Array
        (
            [group_id] => 1023
            [user_id] => 13234
            [master_id] => 13234
            [master_name] =>
            [member_count] => 10
            [group_code] => **********
            [group_name] => mm
            [country] =>
            [province] =>
            [city] =>
            [detail_address] =>
            [group_type] => 1
            [company_id] => 13234
            [company_name] =>
            [part_name] =>
            [img] =>
            [approval_status] => 1
        )
     */
    public function getGroupByGroupId($groupId){
        $url = $this->_centerInterfaceUrl . 'list-group-by-ids';

        $groupIds = [$groupId];

        $result = (new CommonCurl())->sendPostCurl($url, ['group_ids'=>$groupIds]);

        if(isset($result['status']) && (1 == $result['status'])){
            return !empty($result['data'][0]) ? $result['data'][0] : '';
        }

        return [];
    }

    /**
     * Notes: 根据鹰群ids获取鹰群信息
     * Author: xie yuxiang
     * Date : 2023/7/31 10:16
     * @param $groupIds array/string
     * @return array|mixed|string
     */
    public function getGroupByGroupIds($groupIds)
    {
        $url = $this->_centerInterfaceUrl . 'list-group-by-ids';

        if (is_string($groupIds)) {
            $groupIds = explode(',', $groupIds);
        }

        $result = (new CommonCurl())->sendPostCurl($url, ['group_ids' => $groupIds]);

        if (isset($result['status']) && (1 == $result['status'])) {
            return !empty($result['data']) ? $result['data'] : '';
        }

        return [];
    }
    /**
     * 根据多个用户id获取共同的鹰群信息
     *
     * <AUTHOR> @copyright 2016-11-8
     */
    public function sampleGroupByUserIds($userIds){
        $url = $this->_centerInterfaceUrl . 'list-common-group-by-user-ids';

        $result = (new CommonCurl())->sendPostCurl($url, ['user_ids'=>$userIds]);

        if(isset($result['status']) && (1 == $result['status'])){
            return $result['data'];
        }

        return [];
    }

    /**
     * 根据用户id（数组，可能多个id）获取对应的用户信息
     * 对应老的$this->userService('user.lists')
     *
     * $fromCatch =true 可以从缓存那数据，但是信息不全，只要用户名等的可以传此参数节省时间
     * <AUTHOR> @copyright 2016-11-8
     * @param array|int $userIds
     */
    public function userDetailsByUserIds($userIds,$fromCatch=false){

//        $cacheKey = '/frontend/interfaces/CenterInterface/userDetailsByUserIds/' . json_encode($userIds);
//        $cacheValue = \Yii::$app->redis->get($cacheKey);
//        if ($cacheValue) return json_decode($cacheValue, true);

        $url = $this->_centerInterfaceUrl . 'list-user-by-ids';

        if (!is_array($userIds)) {
            $userIds = [$userIds];
        }

        $result = (new CommonCurl())->sendPostCurl($url, ['user_ids'=>$userIds]);
        if(isset($result['status']) && (1 == $result['status'])){
            $returnData = [];
            if(!empty($result['data'])){
            	foreach ($result['data'] as $data){
                    if (!empty($data)) {
                        // $data['real_name'] = CommonServer::displayUserName($data);
                	    $data['real_name'] = !empty($data['real_name']) ? $data['real_name'] : (!empty($data['nick_name']) ? $data['nick_name'] : (!empty($data['name']) ? $data['name'] : ''));
                	    $returnData[] = $data;
            	    }
            	}
            }

            //\Yii::$app->redis->setex($cacheKey, \Yii::$app->params['center_api_cache_timeout'], json_encode($returnData));

            return $returnData;
        }

        return [];
    }

    /**
     * 根据用户id获取对应的用户信息
     * $this->userService('user.get')
     *
     * <AUTHOR> @copyright 2016-11-8
     * @param int $userId
     * @return Ambigous <>|NULL
     */
    public function getUserByUserId($userId,$status=1){

        $cacheKey = '/frontend/interfaces/CenterInterface/getUserByUserId/' . json_encode($userId);

        $cacheValue = \Yii::$app->redis->get($cacheKey);

        if (0 && $cacheValue)
            return json_decode($cacheValue, true);

        $url = $this->_centerInterfaceUrl . 'get-user-by-id';
        if(is_array($userId)){
            $userId=$userId[0];
        }
        $result = (new CommonCurl())->sendPostCurl($url, ['user_id'=>$userId,'status'=>$status]);

        if(isset($result['status']) && (1 == $result['status'])){
            $data = $result['data'];
            if(!empty($data)){
                $data['real_name'] = !empty($data['real_name']) ? $data['real_name'] : (!empty($data['nick_name']) ? $data['nick_name'] : (!empty($data['name']) ? $data['name'] : ''));
            }

            \Yii::$app->redis->setex($cacheKey, \Yii::$app->params['center_api_cache_timeout'], json_encode($data));

            return $data;
        }

        return [];
    }

    /**
     * 根据用户email（数组，可能多个email）获取对应的用户信息
     * 对应老的$this->userService('user.lists_b')
     *
     * <AUTHOR> @copyright 2016-11-8
     * @param array|string $emails
     */
    public function userDetailsByEmails($emails){
        $url = $this->_centerInterfaceUrl . 'list-user-by-emails';

        if(!is_array($emails)){
            $emails = [$emails];
        }

        $result = (new CommonCurl())->sendPostCurl($url, ['emails'=>$emails]);

        if(isset($result['status']) && (1 == $result['status'])){
            $returnData = [];

            if(!empty($result['data'])){
            	foreach ($result['data'] as $data){
            	    if(!empty($data)){
            	       $data['real_name'] = !empty($data['real_name']) ? $data['real_name'] : (!empty($data['nick_name']) ? $data['nick_name'] : (!empty($data['name']) ? $data['name'] : ''));
            	       $returnData[] = $data;
            	    }
            	}
            }

            return $returnData;
        }

        return [];
    }

    /**
     * 根据输入的内容获取用户信息
     *
     * <AUTHOR> @copyright 2016-11-16
     * @param array|string $inputs
     * @return multitype:Ambigous <unknown, string> |multitype:
     */
    public function userDetailsByInput($inputs){
        $url = $this->_centerInterfaceUrl . 'list-user-info-by-inputs';

        $result = (new CommonCurl())->sendPostCurl($url, ['inputs'=>$inputs]);

        if(isset($result['status']) && (1 == $result['status'])){
            $returnData = $result['data'];

            if(!empty($result['data'])){
                if (is_array($inputs)){
                    foreach ($returnData as $key=>$data){
                        if(!empty($data)){
                            $data['real_name'] = !empty($data['real_name']) ? $data['real_name'] : (!empty($data['nick_name']) ? $data['nick_name'] : (!empty($data['name']) ? $data['name'] : ''));
                            $returnData[$key] = $data;
                        }

                    }
                }

                if(!is_array($inputs)){
                    $data = $result['data'];
                    $data['real_name'] = !empty($data['real_name']) ? $data['real_name'] : (!empty($data['nick_name']) ? $data['nick_name'] : (!empty($data['name']) ? $data['name'] : ''));

                    return $data;
                }
            }

            return $returnData;
        }

        return [];
    }

   /**
    * 根据用户id获取所有的鹰群信息，包括已解散的鹰群和退出的鹰群
    *
    * <AUTHOR> @copyright 2016-11-8
    */
    public function allGroupByUserId($userId){
        $url = $this->_centerInterfaceUrl . 'list-all-group-by-user-id';
        $result = (new CommonCurl())->sendPostCurl($url, ['user_id'=>$userId]);

        if(isset($result['status']) && (1 == $result['status'])){
            return $result['data'];
        }

        return [];
    }

    /**
     * 获取指定鹰群下自定义条件的成员列表
     *
     * <AUTHOR> @copyright 2016-11-8
     *
     * @param $options in ['id', 'user_id', 'status', 'role_flag'];
     * @param int $groupId
     * @return Ambigous <>|NULL
     */
    public function memberListByGroupId($groupId, $options=NULL, $applyId=2){
        $url = $this->_centerInterfaceUrl . 'list-member-by-options';

        $result = (new CommonCurl())->sendPostCurl($url, ['group_id'=>$groupId, 'options'=>$options, 'apply_id'=>$applyId]);

        if(isset($result['status']) && (1 == $result['status'])){
            $returnData = [];

            if(!empty($result['data'])){
            	foreach ($result['data'] as $data){
            	    if(!empty($data)){
            	       $data['real_name'] = !empty($data['real_name']) ? $data['real_name'] : (!empty($data['nick_name']) ? $data['nick_name'] : (!empty($data['name']) ? $data['name'] : ''));
            	       $returnData[] = $data;
            	    }
            	}
            }

            return $returnData;
        }

        return [];

    }

    /**
     * 获取鹰群用户和eln的用户，并且包括role
     *
     * <AUTHOR> @copyright 2017-4-10
     * @param unknown $groupId
     * @param string $options
     * @param number $applyId
     */
    public function userListAndElnUsers($groupId, $page=1, $limit=15, $options = NULL, $applyId = 2, $onlyApply = NULL, $onlyHistory = NULL) {
        $url = $this->_centerInterfaceUrl . 'user-list-and-apply-user';

        $result = (new CommonCurl())->sendPostCurl($url, [
            'group_id' => $groupId,
            'key' => $options,
            'apply_id' => $applyId,
            'limit' => $limit,
            'page' => $page,
            'only_apply' => $onlyApply,
            'only_history' => $onlyHistory
        ]);

        if (empty($result['data'])) {
            return [];
        }

        return $result['data'];
    }

    /**
     * 根据输入获取指定鹰群用户列表
     *
     * <AUTHOR> @copyright 2017-3-27
     * @param unknown $groupId
     * @param unknown $input
     */
    public function userListByInputAndGroup($groupId, $input){

        if(empty($groupId)){
            $url = $this->_centerInterfaceUrl . 'list-user-info-by-inputs';
            $result = (new CommonCurl())->sendPostCurl($url, ['inputs'=>$input]);

        }

        if(!empty($groupId)){
            $url = $this->_centerInterfaceUrl . 'list-user-info-by-options';
            $result = (new CommonCurl())->sendPostCurl($url, ['group_id'=>$groupId, 'options'=>['input'=>$input]]);
        }

        if(!empty($result['data'])){
        	return $result['data'];
        }

        return [];
    }

    /**
     * 根据用户id获取用户的图像
     *
     * 对应老的listUDAvatar
     *
     * <AUTHOR> @copyright 2016-11-8
     * @param array|int $userIds
     * @param bool $isMore 是否是多个
     */
    public function userImgByUserIds($userIds, $isMore=FALSE){
        if(!is_array($userIds)){
        	$userIds = [$userIds];
        }

        $users = $this->userDetailsByUserIds($userIds);

        $returnData = [];
        if(!empty($users)){
            $center_file_url = \Yii::$app->params['center_file_url'];

        	foreach ($users as $key=>$user){
        	    $img = $center_file_url.$user['small_img'];

        	    if(!$isMore){
        	         return $img;
        	    }
        		$user['user_img'] = $img;
        		$returnData[$user['user_id']] = $user;
        	}
        }

        return $returnData;
    }

    public function userAvatarByUserIds($userIds)
    {
        if (!is_array($userIds)) {
            $userIds = [$userIds];
        }

        $users = $this->userDetailsByUserIds($userIds);

        if (!empty($users)) {
            return $users[0]['small_img'];
        }else{
            return '';
        }
    }

    /**
     * 根据用户id获取能添加成员的鹰群
     * 暂时没用
     *
     * <AUTHOR> @copyright 2016-11-8
     */
    public function adminGroupListByUserId($userId){
        $url = $this->_centerInterfaceUrl . 'list-group-to-join';

        $result = (new CommonCurl())->sendPostCurl($url, ['user_id'=>$userId]);

        if(isset($result['status']) && (1 == $result['status'])){
            return $result['data'];
        }

        return [];
    }

    /**
     * Notes: 获取指定角色的用户鹰群列表
     *
     * Author: zhhj
     * Date: 2020/7/20
     * @param $userId
     * @param $roleArr
     * @return array|mixed
     */
    public function listUserGroupByRole($userId, $roleArr) {
        $url = $this->_centerInterfaceUrl . 'list-group-by-role';

        $result = (new CommonCurl())->sendPostCurl($url, [
            'userId' => $userId,
            'roleFlag' => $roleArr,
        ]);

        if (isset($result['status']) && (1 == $result['status'])) {
            return $result['data'];
        }

        return [];
    }

    /**
     * 根据用户id判断是否有eln访问权限
     *
     * @deprecated
     * <AUTHOR> @copyright 2016-11-8
     * @param int $userId
     */
    public function isElnByUserId($userId, $companyId=0){
        $cacheKey = '/frontend/interfaces/CenterInterface/isElnByUserId/' . json_encode([$userId, $companyId]);

        $cacheValue = \Yii::$app->redis->get($cacheKey);

        if ($cacheValue)
            return json_decode($cacheValue, true);

        $url = $this->_centerInterfaceUrl . 'check-app-access';

        $result = (new CommonCurl())->sendPostCurl($url, ['user_id'=>$userId, 'apply_id'=>\Yii::$app->params['eln_apply_id'], 'company_id' => $companyId]);

        if(!isset($result['status'])) {
            return [];
        }

        \Yii::$app->redis->setex($cacheKey, \Yii::$app->params['center_api_cache_timeout'], json_encode($result));

        return $result;
    }

    /**
     * 根据用户id判断是否是有eln的权限
     *
     * <AUTHOR> @copyright 2016-11-12
     * @param array  $userIds
     */
    public function elnUserListByUserId($userIds){

    }

    /**
     * eln添加消息到平台
     *
     * <AUTHOR> 0-站内信 1-鹰群消息 2-订单处理消息 3-物流消息 4-用户消息 5-ELN消息 6-库存消息 7-采购需求消息 8-服务消息 9-InStock消息 10-InCMS消息 11-订单审批消息
     * 5 复核审核 通知
     *
     * 51评论
       52点赞
       53整本下载
       54实验复核
       55通过复核
       56拒绝复核
       57系统消息
       58实验分享
       59实验取消分享
       500模板分享
       501模板取消分享
       502词条分享
       503词条取消分享

     * .
     * @copyright 2016-11-8
     */
    public function addMessage($userId, $toUserId, $title, $content, $category=3, $companyId=0,$type=5,$url=''){

        $data = [
	        'category' => $category, // 1系统消息 3应用消息
	        'application' => 3,
	        'type' => $type,
	        'title' => $title,
            'url' => $url,
	        'content' => $content,
	        'userId' => $userId,
	        'toUserId' => $toUserId,
            'company_id' => $companyId
        ];

        $url = CENTER_URL . 'api/send-single-message';

        $result = (new CommonCurl())->sendPostCurl($url, $data);

        if(isset($result['status']) && (1 == $result['status']) && (!empty($result['data']['id']))){
            return $result['data']['id'];
        }

        return FALSE;

    }

    /**
     * 删除消息
     *
     * <AUTHOR> @copyright 2016-11-8
     * @param array $msgId
     * @return boolean
     */
    public function delMessage($msgIds){

        $url = CENTER_URL . 'api/delete-message';

        $result = (new CommonCurl())->sendPostCurl($url, ['id'=>$msgIds]);

        if(isset($result['status']) && (1 == $result['status'])){
            return TRUE;
        }

        return FALSE;

    }

    /**
     * 获取登录的用户信息
     *
     * <AUTHOR> @copyright 2016-11-9
     * @param unknown $ticket
     * @return boolean
     */
    public function getUserInfo($ticket){
        $url = CENTER_URL . 'api/user-info';


        $params = ['ticket' => $ticket, 'apply_id'=>\Yii::$app->params['eln_apply_id']];


        $result = (new CommonCurl())->sendPostCurl($url, $params);

        if(isset($result['status']) && (1 == $result['status'])){
            $data = $result['data'];

            if(!empty($data)){
                $data['real_name'] = !empty($data['real_name']) ? $data['real_name'] : (!empty($data['nick_name']) ? $data['nick_name'] : (!empty($data['name']) ? $data['name'] : ''));
            }
            if (isset($data['app_access'])) {
                \Yii::$app->view->params['access_eln'] = $data['app_access'];
            }
            return $data;
        }

        return FALSE;
    }

    /**
     * 添加鹰群成员
     *
     * <AUTHOR>
     * @copyright 2017-04-11
     * @param $data = [
                'group_id' => 群id,
                'account' => 账号集合，数组格式,
                'user_id' => 操作者用户id,
              ];
     * @return array
     */
    public function addGroupMember($data)
    {
        $url = $this->_centerInterfaceUrl . 'add-group-member';

        $result = (new CommonCurl())->sendPostCurl($url, $data);

        return $result;
    }

    /**
     * 分配应用
     *
     * <AUTHOR>
     * @copyright 2017-04-12
     * @param $data = [
                'dispatcher_id' => 分配者用户id,
                'member_ids' => 待分配的用户id集合，数组格式,
                'app_id' => 应用id,
              ];
     * @return array
     */
    public function dispatchApp($data)
    {
        $url = $this->_centerInterfaceUrl . 'dispatch-app';
        $lang = \Yii::$app->language;
        $langArr = \Yii::$app->params['language_list'];
        $centerLang = 'CN';
        if($lang == $langArr['en']){
            $centerLang = 'EN';
        }
        $data['language'] = $centerLang;

        $result = (new CommonCurl())->sendPostCurl($url, $data);

        return $result;
    }

    /**
     * 取消分配应用
     *
     * <AUTHOR>
     * @copyright 2017-04-12
     * @param $data = [
                'undispatcher_id' => 操作者用户id,
                'member_ids' => 待操作的用户id集合，数组格式,
                'app_id' => 应用id,
              ];
     * @return array
     */
    public function undispatchApp($data)
    {
        $url = $this->_centerInterfaceUrl . 'un-dispatch-app';

        $result = (new CommonCurl())->sendPostCurl($url, $data);

        return $result;
    }

    /**
     * 获取用户在群里角色类型
     *
     * <AUTHOR>
     * @copyright 2017-04-12
     * @param $data = [
                'user_id' => 用户id,
                'group_id' => 群id,
              ];
     * @return int  1、普通成员 2、管理员 3、群主 0、不在群里（群主和管理员有添加成员权限）
     */
    public function getUserRole($data)
    {
        $url = $this->_centerInterfaceUrl . 'get-user-group-role';

        $result = (new CommonCurl())->sendPostCurl($url, $data);

        return isset($result['data']) ? $result['data'] : 0;
    }

    /**
     * 检查应用授权信息，是否可以分配应用（实际应用需同时判断是否是群主或管理员）
     *
     * <AUTHOR>
     * @copyright 2017-04-12
     * @param $data = [
                'group_id' => 群id,
              ];
     * @return bool
     */
    public function checkApplyGrant($data)
    {
        $url = $this->_centerInterfaceUrl . 'check-apply-grant';

        $result = (new CommonCurl())->sendPostCurl($url, $data);

        return isset($result['data']) ? $result['data']['hasApplyGrant'] : false;
    }

    /**
     * 获取全部群成员信息
     * @param $data = [
                'group_id' => 群id,
              ];
     * @return array
     */
    public function getGroupMembers($data)
    {
        $url = $this->_centerInterfaceUrl . 'list-user-info-by-options';

        $result = (new CommonCurl())->sendPostCurl($url, $data);

        return isset($result['data']) ? $result['data'] : [];
    }

    /**
     * Notes: 获取鹰群成员
     *
     * Author: zhhj
     * Date: 2020/7/20
     * @param  $groupId
     * @param array $params [
     *  'include_history_member' => boolean 是否包含历史成员, 默认false
     *  'search_str' => string 用户信息搜索字符串, 默认空
     * ]
     * @return array|mixed
     */
    public function listGroupMember($groupId, $params = []) {
        $url = $this->_centerInterfaceUrl . 'list-group-member';

        $postParams = [
            'group_id' => $groupId
        ];
        if (!empty($params) && is_array($params)) {
            $postParams = array_merge($postParams, $params);
        }
        $result = (new CommonCurl())->sendPostCurl($url, $postParams);

        if(!empty($result['data'])){
            foreach ($result['data'] as $data){
                if(!empty($data)){
                    $data['real_name'] = !empty($data['real_name']) ? $data['real_name'] : (!empty($data['nick_name']) ? $data['nick_name'] : (!empty($data['name']) ? $data['name'] : ''));
                    $returnData[] = $data;
                }
            }
        }

        return isset($result['data']) ? $result['data'] : [1];
    }


    /**
     * @Notes: 获取对于鹰群的群成员 id, map 形式
     *  'data' => [
            'groupNormalMemberMap' => [],
            'groupOutMemberMap' => [],
         ]
     * @param $groupIds
     * @return array|mixed
     * @author: tianyang
     * @Time: 2023/3/29 18:13
     */
    public function groupMemberMap($groupIds) {
        $url = $this->_centerInterfaceUrl . 'group-member-map';

        $postParams = [
            'group_id' => $groupIds
        ];

        $result = (new CommonCurl())->sendPostCurl($url, $postParams);

        $returnData = [];
        if (1 == $result['status'] && !empty($result['data'])) {
            $returnData = $result['data'];
        }

        return $returnData;
    }

    /**
     * 根据id获取t_group_member表的信息
     */
    public function getGroupMemberInfo($memberIds)
    {
        $url = $this->_centerInterfaceUrl . 'get-group-member-info';

        $result = (new CommonCurl())->sendPostCurl($url, ['member_ids' => $memberIds]);

        return isset($result['data']) ? $result['data'] : [];
    }

    /**
     * 根据用户ID和鹰群ID获取用户是否开通eln应用
     *
     * <AUTHOR> huili
     * @copyright 2017-05-05
     * return true/false
     */
    public function getElnApp($data){

        $url = $this->_centerInterfaceUrl . 'list-apply-group-member';

        $result = (new CommonCurl())->sendPostCurl($url, ['group_id'=>$data['group_id'], 'app_id'=>\Yii::$app->params['eln_apply_id']]);

        $userId =$data['user_id'];

        $result = array_column($result['data'],'user_id');
        if(in_array($userId,$result)){
            return true;
        }

        return false;

    }
    /**
     * 根据输入获取用户id
     *
     * <AUTHOR> huili
     * @copyright 2017-07-04
     * return user_id
     */
    public function getUserIdByInput($param){

        if(empty($param)){
            return [];
        }
        $url = $this->_centerInterfaceUrl . 'get-user-id-by-input';

        $result = (new CommonCurl())->sendPostCurl($url, ['keyword' => $param]);

        return $result;

    }

    /**
     * 根据输入（鹰群名称，鹰群号）获取鹰群信息
     *
     * <AUTHOR> huili
     * @copyright 2017-08-23
     * return array
     */

    public function getGroupByInput($param){

        if(empty($param)){
            return [];
        }
        $url = $this-> _centerInterfaceUrl . 'like-search-group';
        $result = (new CommonCurl())->sendPostCurl($url, ['options' => [ 'group_name' => $param, 'group_code' => $param ]]);
        return $result;
    }


    public function searchUsersAndGroupsByInput($inputValue){
        $url = $this-> _centerInterfaceUrl . 'search-group-and-user-by-input';
        $result = (new CommonCurl())->sendPostCurl($url, [
            'options' => [
                'group' =>[
                    'group_name' => $inputValue,
                    'group_code' => $inputValue
                ],
                'user'=>[
                    'real_name' => $inputValue,
                    'nick_name' => $inputValue
                ]]]);

        if(isset($result['status']) && (1 == $result['status'])){
            return $result['data'];
        }

        return [];
    }


    /**
     * Note: 获取企业全部用户(当包含历史用户时，不进行缓存)
     *
     * @param $companyId int 企业ID
     * @param $includeStatus0 boolean 包含历史用户
     * @param $refreshCache boolean 刷新缓存
     * @return array|mixed
     */
    public function getUserListByCompanyId($companyId=1, $includeStatus0 = false, $refreshCache = false) {
        $redis = \Yii::$app->redis;
        if (!$refreshCache) {
            $cmpUserList = $redis->get('cmp_user_list');
            if (!empty($cmpUserList)) {
                $cmpUserList = json_decode($cmpUserList, TRUE);
                if (!$includeStatus0) {
                    $cmpUserList['list'] = array_filter($cmpUserList['list'], function ($user) {
                        return !empty($user['status']);
                    });
                }
                return $cmpUserList;
            }
        }

        $url = $this->_centerInterfaceUrl . 'list-user-by-company-id';
        $result = (new CommonCurl())->sendPostCurl($url, [
            'company_id' => $companyId,
            'include_status_0' => 1
        ]);

        $cmpUserList = [];
        if (isset($result['status']) && (1 == $result['status'])) {
            $cmpUserList = !empty($result['data']) ? $result['data'] : [];
            $redis->set('cmp_user_list', json_encode($cmpUserList));
        }

        if (!$includeStatus0) {
            $cmpUserList['list'] = array_filter($cmpUserList['list'], function ($user) {
                return !empty($user['status']);
            });
        }

        return $cmpUserList;
    }


    /**
     * Notes: 获取下属成员列表，all=‘1’获取所有下属
     * Author: hkk
     * Date: 2022/7/8 15:42
     * @param $userId
     * @param  $all
     * @return array|mixed
     */
    public function getSubordinateUsers($userId, $all = '1') {

        $url = $this->_centerInterfaceUrl . 'get-subordinate-users';
        $result = (new CommonCurl())->sendPostCurl($url, [
            'user_id' => $userId,
            'all' => $all,
        ]);

        $userList = [];
        if (isset($result['status']) && (1 == $result['status'])) {
            $userList = !empty($result['data']) ? $result['data'] : [];
        }
        return $userList;
    }


    /**
     * Notes: 根据企业ID 获取鹰群列表
     * Author: zhhj
     * Date: 2021/5/17
     * @param $company_id
     * @param string $keyword
     * @param bool $includeStatus0 是否包含已经解散的鹰群
     * @return array|mixed|string
     */
    public function getGroupsListByCompanyId($company_id=1, $keyword = '', $includeStatus0 = false, $roleUser = 0, $refreshCache = false) {
        $allGroupList = [];
        // // add by hkk 2022/6/24 优先读取缓存
        $redis = \Yii::$app->redis;

        if (!$refreshCache) {
            $allGroupList = $redis->get('group_list');
            if (!empty($allGroupList)) {
                $allGroupList = json_decode($allGroupList, TRUE);

                if (!$includeStatus0) {
                    $allGroupList = array_filter($allGroupList, function ($group) {
                        return !empty($group['status']);
                    });
                }

                return $allGroupList;
            }
        }


        $url = $this->_centerInterfaceUrl . 'list-groups-by-company-id';
        $result = (new CommonCurl())->sendPostCurl($url, [
            'company_id' => $company_id,
            'include_status0' => 1,
//            'role_user' => $roleUser,
            'role_user' => 1
        ]);
        //print_r($result);exit;

        if (isset($result['status']) && (1 == $result['status'])) {
            $allGroupList = !empty($result['data']) ? $result['data'] : [];
            $allGroupList = ArrayHelper::index($allGroupList, 'id');
            $redis->set('group_list', json_encode($allGroupList, true));
        }

        if (!$includeStatus0) {
            $allGroupList = array_filter($allGroupList, function ($group) {
                return !empty($group['status']);
            });
        }

        return $allGroupList;
    }

    /**
     * 获取鹰群用户和eln的用户，并且包括role
     *
     * @param int $page
     * @param int $limit
     * @param array $params
     * @return array
     * <AUTHOR> @copyright 2017-4-10
     */
    public function userLoginLogList($page = 1, $limit = 15, $params = []) {
        $query = LoginLogModel::find();
        $where = [];
        $query = $query->where($where);
        if ($params['user_id']) {
            $query->andWhere(['user_id' => $params['user_id']]);
        }
        if (!empty($params['start_time'])) {
            $query->andFilterWhere(['>=', 'create_time', $params['start_time']]);
        }
        if (!empty($params['end_time'])) {
            $query->andFilterWhere(['<=', 'create_time', $params['end_time'] . ' 23:59:59']);
        }

        // 排序
        $sortType = @getVar($params['sort_type'], -1); // -1:倒叙,1:正序
        $orderBy = 'id ' . (($sortType == -1) ? 'desc' : 'asc');
        $query->orderBy($orderBy);

        // 分页条件
        $totalCount = 0;
        if ($page !== NULL && $limit !== NULL) {
            $query->offset(intval($page - 1) * $limit)->limit(intval($limit));
            $totalCount = $query->count();
        }
        $data = $query->asArray()->all();
        return [
            'status' => 1,
            'data' => $data,
            'count' => $totalCount
        ];
    }
    /**
     * 根据用户ID数组查询login_log表，返回每个用户最近的登录时间
     *
     * @param array $user_ids 用户ID数组
     * @return array 返回user_id和最新登录时间的映射数组
     * <AUTHOR> @copyright 2025-06-12
     */
    public function getLastLoginByIds($user_ids = []) {
        if (empty($user_ids)) {
            return [];
        }

        // 查询每个用户的最新登录记录，过滤掉status为0的无效数据
        $query = LoginLogModel::find()
            ->select(['user_id', 'MAX(create_time) as latest_login_time'])
            ->where(['user_id' => $user_ids])
            ->andWhere(['status' => 1])  // 只查询有效的登录记录
            ->groupBy('user_id');

        $data = $query->asArray()->all();

        // 转换为user_id => latest_login_time的映射格式
        $result = [];
        foreach ($data as $row) {
            $result[$row['user_id']] = $row['latest_login_time'];
        }

        return $result;
    }
    /**
     * 根据 userId 获取其可见用户
     * @param $userId
     * @param bool $refreshCache
     * @param array $status
     * @return array|mixed
     * <AUTHOR>
     * @copyright 2022/6/16
     */
    public function getVisibleUsers($userId, $refreshCache = false, $status = [1, 2]){
        $redis = \Yii::$app->redis;
        $cacheKey = 'visible_users_by_' . $userId;
        $visibleUsers = [];

        if (!$refreshCache) {
            $userListJson = $redis->get($cacheKey);
            if (!empty($userListJson)) {
                $visibleUsers = json_decode($userListJson, TRUE);
                $visibleUsers = array_filter($visibleUsers, function ($user) use ($status) {
                    return in_array($user['status'], $status);
                });
                return array_values($visibleUsers);
            }
        }

        $url = $this->_centerInterfaceUrl . 'list-visible-users';
        $result = (new CommonCurl())->sendPostCurl($url, [
            'user_id' => $userId,
            'status' => [0, 1, 2],
        ]);

//        if (isset($result['visibility']) && $result['visibility'] > 0) { //可见范围为全公司才能看到历史用户
//            $status = array_diff($status, [0, '0']);
//        }

        if (isset($result['status']) && (1 == $result['status'])) {
            $visibleUsers = !empty($result['data']) ? $result['data'] : [];
            $redis->set($cacheKey, json_encode($visibleUsers));
        }

        $visibleUsers = array_filter($visibleUsers, function ($user) use ($status) {
            return in_array($user['status'], $status);
        });
        return array_values($visibleUsers);
    }

    /**
     * 根据 userId 获取其可见部门
     * getVisibleDepartments
     * @param $userId
     * @return array|mixed
     * <AUTHOR>
     * @copyright 2022/6/16
     */
    public function getVisibleDepartments($userId, $refreshCache = false, $active_only = false){
        $redis = \Yii::$app->redis;
        $cacheKey = 'visible_departments_by' . $userId;
        $visibleDepartments = [];

        if (!$refreshCache) {
            $visibleDepartments = $redis->get($cacheKey);
            if (!empty($visibleDepartments)) {
                return json_decode($visibleDepartments, true);
            }
        }

        $url = $this->_centerInterfaceUrl . 'list-visible-departments';
        $result = (new CommonCurl())->sendPostCurl($url, ['user_id' => $userId, 'active_only' => $active_only]);

        if (isset($result['status']) && (1 == $result['status'])) {
            $visibleDepartments = !empty($result['data']) ? $result['data'] : [];
            $redis->set($cacheKey, json_encode($visibleDepartments, true));
        }

        return $visibleDepartments;
    }

    /**
     * 获取用户的可见鹰群列表
     * @param int $userId 用户id
     * @param array $filterStatus 鹰群状态筛选
     * @param bool $refreshCache 是否刷新redis
     * @return array
     */
    public function getVisibleGroups($userId, $filterStatus = [], $refreshCache = false)
    {
        $visibleGroups = $this->getVisibleGroupsWithTags($userId, $refreshCache)['groups'];
        if (!empty($filterStatus)) {
            $visibleGroups = array_filter($visibleGroups, function ($visibleGroup) use ($filterStatus) {
                return in_array($visibleGroup['status'], $filterStatus);
            });
        }
        return ArrayHelper::index($visibleGroups, 'id');
    }

    /**
     * 根据 userId 获取其可见鹰群及其鹰群标签
     * getVisibleGroupsWithTags
     * @param $userId
     * @return array|mixed
     * <AUTHOR>
     * @copyright 2022/6/16
     */
    public function getVisibleGroupsWithTags($userId, $refreshCache = false, $status = []){
        $redis = \Yii::$app->redis;
        $cacheKey = 'visible_group_with_tag_by_' . $userId;
        $visibleGroupsWithTags = [];

        if (!$refreshCache) {
            $visibleGroupsWithTags = $redis->get($cacheKey);
            if (!empty($visibleGroupsWithTags)) {
                return json_decode($visibleGroupsWithTags, TRUE);
            }
        }

        $url = $this->_centerInterfaceUrl . 'list-visible-groups';
        $result = (new CommonCurl())->sendPostCurl($url, ['user_id' => $userId, 'status' => $status]);

        if (isset($result['status']) && (1 == $result['status'])) {
            $visibleGroupsWithTags = !empty($result['data']) ? $result['data'] : [];
            $redis->set($cacheKey, json_encode($visibleGroupsWithTags, true));
        }

        return $visibleGroupsWithTags;
    }


    /**
     * 根据鹰群 groupIds 获取标签列表
     * getGroupTagsByIds
     * @param $groupIds array
     * @return array
     * <AUTHOR>
     * @copyright 2022/6/9
     */
    public function getGroupTagsByIds($groupIds) {
        $url = $this->_centerInterfaceUrl . 'list-tags-by-group-ids';

        $groupIdsString = implode(',', $groupIds);
        $result = (new CommonCurl())->sendPostCurl($url, ['group_ids' => $groupIdsString]);

        if(isset($result['status']) && (1 == $result['status'])){
            return !empty($result['data']) ? $result['data'] : [];
        }
        return [];
    }


    /**
     * 根据设置项获取审批 节点数组
     * getUsersNodesBySetting
     * @param $groupId
     * @param $userId
     * @param $setting
     * @return array|mixed
     * <AUTHOR>
     * @copyright 2022/7/8
     */
    public function getUsersNodesBySetting($groupId, $userId, $setting) {
        $url = $this->_centerInterfaceUrl . 'get-users-nodes-by-setting';

        $result = (new CommonCurl())->sendPostCurl($url, [
            'group_id' => $groupId,
            'user_id' => $userId,
            'setting' => json_encode($setting, true),
        ]);


        if(isset($result['status']) && (1 == $result['status'])){
            return !empty($result['data']) ? $result['data'] : [];
        }
        return [];
    }

    /**
     * @Notes: 根据设置获取对应的精确部门和鹰群之类显示
     * @param $groupId
     * @param $userId
     * @param $setting
     * @return array|mixed
     * @author: tianyang
     * @Time: 2023/2/28 10:30
     */
    public function getUsersNodesSettingShowEln($groupId, $userId, $setting)
    {
        $url = $this->_centerInterfaceUrl . 'get-users-nodes-setting-show-eln';

        $result = (new CommonCurl())->sendPostCurl($url, [
            'group_id' => $groupId,
            'user_id' => $userId,
            'setting' => json_encode($setting, true),
        ]);


        if(isset($result['status']) && (1 == $result['status'])){
            return !empty($result['data']) ? $result['data'] : [];
        }
        return [];
    }

    /**
     * 人员范围 setting 获取 userIds
     * getUsersBySetting
     * @param $groupId
     * @param $userId
     * @param $setting
     * @return array|mixed
     * <AUTHOR>
     * @copyright 2022/7/11
     */
    public function getUsersBySetting($groupId, $userId, $setting) {
        $url = $this->_centerInterfaceUrl . 'get-users-by-setting';

        $result = (new CommonCurl())->sendPostCurl($url, [
            'group_id' => $groupId,
            'user_id' => $userId,
            'setting' => json_encode($setting, true),
        ]);

        if(isset($result['status']) && (1 == $result['status'])){
            return !empty($result['data']) ? $result['data'] : [];
        }
        return [];
    }

    /**
     * Notes: 搜索库存领料
     * Author: zhhj
     * Date: 2021/11/9
     * @param $userId
     * @param $key
     * @param $smiles
     * @param int $inventoryId
     * @param int $stockType
     * @return array|mixed
     */
    public function searchWms($userId, $key, $smiles, $inventoryId = 0, $stockType = 0, $batchIds = []) {
        $url = WMS_URL . 'api/material-search';

        $postData = [
            'inventory_id' => $inventoryId,
            'stock_type' => $stockType,
            'user_id' => $userId,
            'key' => $key,
            'smiles' => $smiles,
            'batch_ids' => $batchIds,
        ];
        $result = (new CommonCurl())->sendPostCurl($url, $postData);

        if (!empty($result['status']) && !empty($result['data'])) {
            return $result['data'];
        }

        return [];
    }

    /**
     * @Notes: 根据条件精确查找一个批次物品, 返回字段 -> 值
     * @param $batchId
     * @return array|mixed
     * @author: tianyang
     * @Time: 2023/6/10 14:18
     */
    public function getOneProductInfoById($batchId) {
        $url = WMS_URL . 'api/get-one-product-info-by-params';
        $language = \Yii::$app->language;
        $result = (new CommonCurl())->sendPostCurl($url, [
            'batch_id' => $batchId,
            'language' => $language
        ]);

        if (!empty($result['status']) && !empty($result['data'])) {
            return $result['data'];
        }

        return [];
    }
    /**
     * Notes: 根据条形码批量搜索库存物品
     * Author: jiangdm
     */
    public function searchWmsByCode($inventoryId, $codes) {
        $url = WMS_URL . 'api/search-product-by-all-code';
        $result = (new CommonCurl())->sendPostCurl($url, [
            'inventory_id' => $inventoryId,
            'codes' => $codes,
        ]);
        if (!empty($result['status']) && !empty($result['data'])) {
            return $result['data'];
        }

        return [];
    }

    /**
     * Notes: 根据批次ID搜索库存信息
     * Author: jiangdm
     */
    public function getBatchInfoByIds($batchIds) {
        $url = WMS_URL . '/api/get-batch-info-by-ids';

        $result = (new CommonCurl())->sendPostCurl($url, [
            'batch_ids' => $batchIds,
        ]);

        if (!empty($result['status']) && !empty($result['data'])) {
            return $result['data'];
        }

        return [];
    }

    /**
     * Notes: 获取cms盐型列表接口
     * Author: wangyao
     * Date: 2023/3/8
     * @return array
     */
    public function getCmsSaltTypeList() {
        if (INCMS_URL === '') {
            return [];
        }

        $url = INCMS_URL . 'interface/get-cms-salt-type-list';
        $result = (new CommonCurl()) -> sendGet($url);
        if(!empty($result['status'])) {
            return $result['data'];
        }
        return [];
    }

    /**
     * Notes: 搜索CMS物品
     * Author: jiangdm
     */
    public function searchCms($userId, $conditions) {
        if (INCMS_URL === '') {
            return [];
        }

        $url = INCMS_URL . 'interface/search-chemicals';
        $result = (new CommonCurl())->sendPostCurl($url, [
            'user_id' => $userId,
            'conditions' => $conditions,
        ]);

        if (!empty($result['status']) && !empty($result['data'])) {
            return $result['data'];
        }

        return [];
    }

    /**
     * Notes: 将链接同步到CMS
     * Author: jiangdm
     */
    public function syncChemicalLinks($links) {
        if (INCMS_URL === '') {
            return [];
        }

        $url = INCMS_URL . 'interface/receive-link';
        $result = (new CommonCurl())->sendPostCurl($url, [
            'links' => $links
        ]);

        if (!empty($result['status']) && !empty($result['data'])) {
            return $result['data'];
        }

        return [];
    }

    /**
     * Notes: 库存同步扫码出库接口
     * Author: jiangdm
     * Date: 2022/3/10
     */
    public function syncInventoryOut($postData){
        $url = WMS_URL . 'api/eln-out-storage';
        return (new CommonCurl())->sendPostCurl($url, $postData);
    }

    /**
     * Notes: 库存同步扫码领料接口
     * Author: jiangdm
     * Date: 2022/3/25
     */
    public function syncInventoryCart($postData){
        $url = WMS_URL . 'api/eln-add-cart';
        return (new CommonCurl())->sendPostCurl($url, $postData);
    }

    /**
     * 根据特定日期 获取登录日志
     *
     * <AUTHOR> @copyright 2019-10-10
     * @param unknown $groupId
     * @param string $options
     * @param number $applyId
     */
    public function userLoginLogListByDay($page=1, $limit=15, $params=[]) {
        $url = $this->_centerInterfaceUrl . 'list-user-login-log-by-day';

        $result = (new CommonCurl())->sendPostCurl($url, [
            'limit' => $limit,
            'offset' => $page ,
            'params' =>$params,

        ]);
        return $result['data'];
    }


    /**
     * 根据企业ID 关键词 获取部门列表
     *
     * <AUTHOR> @copyright 2016-11-9
     * @param int $groupId
     * @return array:
     * $company_id 当前企业ID 不可为空
     * $keyword 关键词 可为空
     */
    public function getDepartmentListByCompanyId($company_id,$keyword='', $refreshCache = false, $includeStatus0 = false){
        $redis = \Yii::$app->redis;

        $departmentList = [];
        $departmentListData = [];
        if (!$refreshCache) {
            $departmentListData = $redis->get('department_list');
            if (!empty($departmentListData)) {
                $departmentList['list'] = json_decode($departmentListData, true);
                if (!$includeStatus0) {
                    $departmentList['list'] = array_filter($departmentList['list'], function ($dep) {
                        return !empty($dep['status']);
                    });
                }
                return $departmentList;
            }
        }

        $url = $this->_centerInterfaceUrl . 'list-department-by-company-id';
        $result = (new CommonCurl())->sendPostCurl($url, ['company_id'=>$company_id,'keyword'=>$keyword, 'include_status0'=>1]);

        if(isset($result['status']) && (1 == $result['status'])){
            $departmentList =  !empty($result['data']) ? $result['data'] : [];
            $departmentListData = ArrayHelper::index($departmentList['list'], 'id');
            $redis->set('department_list', json_encode($departmentListData, true));
        }

        $departmentList['list'] = $departmentListData;

        if (!$includeStatus0) {
            $departmentList['list'] = array_filter($departmentList['list'], function ($dep) {
                return !empty($dep['status']);
            });
        }

        return $departmentList;
    }


    /**
     * 根据企业ID 关键词 获取用户列表
     *
     * <AUTHOR> @copyright 2016-11-9
     * @param int $groupId
     * @return string|multitype:
     * $company_id 当前企业ID 不可为空
     * $keyword 关键词 可为空
     */
    public function getRoleListByCompanyId($company_id,$keyword=''){
        $url = $this->_centerInterfaceUrl . 'list-role-by-company-id';


        $result = (new CommonCurl())->sendPostCurl($url, ['company_id'=>$company_id,'keyword'=>$keyword]);

        if(isset($result['status']) && (1 == $result['status'])){
            return !empty($result['data']) ? $result['data'] : '';
        }

        return [];
    }

    /**
     * 根据企业ID  获取所有群主
     *
     * <AUTHOR> @copyright 2016-11-9
     * @param int $groupId
     * @return string|multitype:
     * $company_id 当前企业ID 不可为空
     * $keyword 关键词 可为空
     */
    public function getGroupListByCompanyId($company_id,$keyword=''){
        $url = $this->_centerInterfaceUrl . 'list-group-by-company-id';

        $result = (new CommonCurl())->sendPostCurl($url, ['company_id'=>$company_id]);

        return $result;
    }

    /**
     * 根据企业ID  获取所有群主
     *
     * <AUTHOR> @copyright 2016-11-9
     * @param int $groupId
     * @return string|multitype:
     * $company_id 当前企业ID 不可为空
     * $keyword 关键词 可为空
     */
    public function getCompanyInfoByCompanyId($company_id){
        $url = $this->_centerInterfaceUrl . 'get-company-info-by-company-id';
        $result = (new CommonCurl())->sendPostCurl($url, ['company_id'=>$company_id]);

        $companyInfo = !empty($result['data']) ? $result['data'] : [];
        $paidItems = !empty($companyInfo['paid_items']) ? $companyInfo['paid_items'] : [];
        $inELNPaidItems = !empty($paidItems['inELN']) ? $paidItems['inELN'] : [];
        $companyInfo['elnPaidItems'] = $inELNPaidItems;
        $result['data'] = $companyInfo;

        return $result;
    }


    /**
     * eln 添加平台日程
     *
     * @param $params
     * @copyright 2016-11-8
     * <AUTHOR> 0-站内信 1-鹰群消息 2-订单处理消息 3-物流消息 4-用户消息 5-ELN消息 6-库存消息 7-采购需求消息 8-服务消息 9-InStock消息 10-InCMS消息 11-订单审批消息
     * 5 复核审核 通知
     *
     * 51评论
     * 52点赞
     * 53整本下载
     * 54实验复核
     * 55通过复核
     * 56拒绝复核
     * 57系统消息
     * 58实验分享
     * 59实验取消分享
     * 500模板分享
     * 501模板取消分享
     * 502词条分享
     * 503词条取消分享
     * .
     */
    public function sendSchedule($params) {
        $url = CENTER_URL . 'interface/add-schedule';
        (new CommonCurl())->sendPostCurl($url, $params, true);
    }

    /**
     * Notes: 判断用户是否为登出状态
     * Author: zhu huajun
     * Date: 2020/5/6 10:57
     * @param $userId
     * @return bool
     */
    public function checkIsLogoutByUserId($userId) {
        $url = $this->_centerInterfaceUrl . 'check-is-logout';

        $result = (new CommonCurl())->sendPostCurl($url, [
            'user_id'=>$userId
        ]);

        return $result['data'] === true;
    }

    /**
     * 根据用户id判断是否有wms访问权限
     *
     * <AUTHOR> @copyright 2016-11-8
     * @param int $userId
     */
    public function isWmsByUserId($userId){
        $url = $this->_centerInterfaceUrl . 'check-app-access';
        $result = (new CommonCurl())->sendPostCurl($url, ['user_id'=>$userId, 'apply_id'=>1]);
        if(!isset($result['status'])) {
            return [];
        }
        return $result;
    }

    /**
     * 根据用户id获取对应的用户信息
     * $this->userService('user.get')
     *
     * <AUTHOR> @copyright 2016-11-8
     * @param int $userId
     * @return Ambigous <>|NULL
     */
    public function getUserLoginById($userId){


        $url = $this->_centerInterfaceUrl . 'get-last-login-time';

        $result = (new CommonCurl())->sendPostCurl($url, ['user_id'=>$userId]);


        return $result;
    }

    /**
     * 获取系统邮箱设置
     *
     * <AUTHOR> @copyright 2016-11-8
     */
    public function getSysEmailConfig() {
        $url = $this->_centerInterfaceUrl . 'get-system-email-config';
        $result = (new CommonCurl())->sendPostCurl($url);
        if(!isset($result['status'])) {
            return [];
        }
        return $result;
    }

    /**
     * 获取系统管理员ID
     *
     * <AUTHOR> @copyright 2016-11-8
     */
    public function getUserByRoleId($roleId) {
        $url = $this->_centerInterfaceUrl . 'get-user-by-role-id';
        $result = (new CommonCurl())->sendPostCurl($url, ['role_id'=>$roleId]);
        if(!isset($result['status'])) {
            return [];
        }
        return $result;
    }

    /**
     * 添加鹰群成员
     *
     * <AUTHOR>
     * @copyright 2017-04-11
     * @param $data = [
    'group_id' => 群id,
    'account' => 账号集合，数组格式,
    'user_id' => 操作者用户id,
    ];
     * @return array
     */
    public function addGroupMemberHistory($data)
    {
        $url = $this->_centerInterfaceUrl . 'add-group-member-history';

        $result = (new CommonCurl())->sendPostCurl($url, $data);

        return $result;
    }

    /**
     * Note: 获取Logo
     * @author: szq
     * @date: 2021/5/6 14:32
     *
     * @return string
     */
    public function getLogo() {
        //added by xieyuxiang 2022.8.19 先查找redis
        $redis = \Yii::$app->redis;
        $result=$redis->get('ELN_LOGO_URL');
        if(isset($result))
        {
            return CENTER_URL . $result;
        }
        $url = $this->_centerInterfaceUrl . 'get-logo';
        $result = (new CommonCurl())->sendPostCurl($url, [
            'key' => 'eln_logo_url'
        ]);
        if (empty($result)) {
            return '';
        }
        $redis->set('ELN_LOGO_URL',$result);
        return CENTER_URL . $result;
    }

    /**
     * 根据企业ID获取用户 获取内容 包括 鹰群 鹰群角色 角色 角色权限
     *
     */
    public function getUserAllInfoByCompanyId($companyId, $keyword = '',$userIds=[],$includeStatusZero=1,$updateRedis=0, $userInfos=[])
    {
        $redis = \Yii::$app->redis;
        $userResult = [];
        // 只拿指定的信息, 暂时不走缓存, 防止同个用户的对应的数据不一致
        if (!empty($userInfos) && !$updateRedis) {
            $user_all_info = $redis->get('user-all-info_' . json_encode($userIds));

            if(!empty($user_all_info)){
                $data = json_decode($user_all_info,true);;
                $userResult = $data;
                if (!$includeStatusZero) {
                    $userResult = [];
                    foreach ($data as $user) {
                        if ($user['status'] == 1 || $user['status'] == 2) {
                            $userResult[] = $user;
                        }
                    }
                }
                return $userResult;
            }
        }

        $url = $this->_centerInterfaceUrl . 'list-user-all-info-by-company-id';
        $result = (new CommonCurl())->sendPostCurl($url, [
            'cpmpany_id' => $companyId,
            'keyword' => $keyword,
            'user_ids' => $userIds,
            'include_status_0' => 1,
            'user_infos' => $userInfos,
        ]);

        //写入redis 并返回
        $redis->set('user-all-info_' . json_encode($userIds), json_encode($result['data']['list']));

        $userResult = $result['data']['list'];
        if (!$includeStatusZero) {
            $userResult = [];
            foreach ($result['data']['list'] as $user) {
                if ($user['status'] == 1 || $user['status'] == 2) {
                    $userResult[] = $user;
                }
            }
        }

        return $userResult;
    }

    /**
     * 获取群主 或者 群管理员 或 普通成员
     *
     * <AUTHOR> @copyright 2016-11-8
     */
    public function getUserByGroupFlag($roleFlag, $groupId=0) {
        $url = $this->_centerInterfaceUrl . 'get-user-by-group-flag';
        $result = (new CommonCurl())->sendPostCurl($url, ['role_flag' => $roleFlag, 'group_id' => $groupId]);
        if (isset($result['status']) && (1 == $result['status'])) {
            return $result['data'];
        }
        return [];
    }

    /**
     * 获取用户作为群主的全部鹰群成员列表
     *
     * @param $userId
     * @return array
     */
    public function listMasteredGroupMember($userId) {
        $url = $this->_centerInterfaceUrl . 'list-group-member-ids-by-master-id';
        $result = (new CommonCurl())->sendPostCurl($url, ['user_id' => $userId]);
        if (isset($result['status']) && $result['status'] == 1) {
            return $result['data'];
        }
        return [];
    }

    /**
     * 获取用户作为主管或分管领导的部门下的用户列表
     *
     * @param $userId
     * @return array
     */
    public function listMasteredDepartmentUsers($userId) {
        $url = $this->_centerInterfaceUrl . 'list-department-member-ids-by-manager-id';
        $result = (new CommonCurl())->sendPostCurl($url, ['user_id' => $userId]);
        if (isset($result['status']) && $result['status'] == 1) {
            return $result['data'];
        }
        return [];
    }

    /**
     * Note: 根据SSO_TICKET获取用户账号
     * @param $ssoTicket
     * @return mixed|string[]
     * <AUTHOR>
     * @date 2022/9/13 16:27
     */
    public function getUserInfoBySsoTicket($ssoTicket = '') {
        if (!empty($ssoTicket) && defined('SSO_TICKET_URL') && !empty(SSO_TICKET_URL)) {
            $joiner = strpos(SSO_TICKET_URL, '?') !== false ? '&' : '?';
            $url = SSO_TICKET_URL . $joiner . 'token=' . $ssoTicket;
            $result = (new CommonCurl())->sendPostCurl($url, [
                'sso_ticket' => $ssoTicket
            ]);
            if (isset($result['status']) && $result['status'] == 1) {
                return $result['data'];
            }
        }

        return [];
    }

    /**
     * Note: 根据用户名获取用户信息，用于对接第三方免登录跳转
     * @param $username
     * @return false|mixed
     * <AUTHOR>
     * @date 2022/9/13 17:50
     */
    public function getUserInfoByUsername($username = '') {
        $url = CENTER_URL . 'api/user-info-by-username';
        $params = [
            'username' => $username,
            'apply_id' => \Yii::$app->params['eln_apply_id']
        ];
        $result = (new CommonCurl())->sendPostCurl($url, $params);

        if (isset($result['status']) && (1 == $result['status'])) {
            $data = $result['data'];

            if (!empty($data)) {
                $data['real_name'] = !empty($data['real_name']) ? $data['real_name'] : (!empty($data['nick_name']) ? $data['nick_name'] : (!empty($data['name']) ? $data['name'] : ''));
            }
            if (isset($data['app_access'])) {
                \Yii::$app->view->params['access_eln'] = $data['app_access'];
            }
            return $data;
        }

        return FALSE;
    }

    /**
     * Note: 根据用户名获取用户ID
     * @param $username
     * @return void
     * <AUTHOR>
     * @date 2022/12/6 16:31
     */
    public function getUserIdByUsername($username) {
        $url = CENTER_URL . 'api/user-id-by-username';
        $params = [
            'username' => $username
        ];
        $result = (new CommonCurl())->sendPostCurl($url, $params);

        if (isset($result['status']) && (1 == $result['status'])) {
            return $result['data'];
        }

        return 0;
    }


    /**
     * 将eln服务器的/home/<USER>
     * @param $bindingId
     * @param $bindingInfo
     * @return array
     */
    public function mountFileFolder($bindingId, $bindingInfo = []) {
        if ($bindingId !== null) {
            $bindingInfo = InstrumentBindingFileServerModel::find()->where(['id' => $bindingId])->asArray()->one();
        }
        $password = $bindingInfo['password'];
        $userName = $bindingInfo['username'];
        $serverPath = $bindingInfo['server_path'];
        if (IS_WIN) {
            $destPath = 'W:';
            $unmountCmd = 'net use ' . $destPath . ' /del';
            exec($unmountCmd);
            $mountPath = str_replace('/', '\\', $serverPath);
            $mountCmd = 'net use ' . $destPath . ' "' . $mountPath . '" "' . $password . '" /user:"' . $userName . '"';
            exec($mountCmd, $result, $exitCode);
        } else {
            $destPath = \Yii::getAlias('@filepath') . \Yii::getAlias('@instrumentFilePath') . ($bindingId === null ? '' : ('_' . $bindingId));
            if (!is_dir($destPath)) mkdir($destPath);
            //更新ELN仪器文件挂载目录
            exec('sudo umount '  . $destPath);//取消挂载老文件夹
            $cmd = 'sudo mount -t cifs -o username=' . $userName . ',password=' . $password . ' '. $serverPath . ' ' . $destPath;
            exec($cmd, $result, $exitCode);
        }
        return ['path' => $destPath, 'code' => $exitCode, 'binding_info' => $bindingInfo];

    }

    public function initFileServer() {
        $serverBindings = InstrumentBindingFileServerModel::find()->where(['init_done' => 1])->asArray()->all();
        $bindingPaths = ['id_to_path' => [], 'server_to_path' => []];
        foreach($serverBindings as $binding) {
            $id = $binding['id'];
            $bindingResult = $this->mountFileFolder($id);
            $bindingPaths['id_to_path'][$id] = $bindingResult['path'];
            $bindingPaths['server_to_path'][$binding['server_path']] = $bindingResult['path'];
        }
        return $bindingPaths;
    }

    public function getFileServerPath($bindingId) {
        $mountResult = $this->mountFileFolder($bindingId);
        return $mountResult['path'];
    }

    /**
     * 根据特定日期 获取登录日志
     *
     * <AUTHOR> @copyright 2019-10-10
     * @param unknown $groupId
     * @param string $options
     * @param number $applyId
     */
    public function userLoginLogListByUser() {
        $url = $this->_centerInterfaceUrl . 'list-user-login-log-by-user';
        $result = (new CommonCurl())->sendPostCurl($url, []);
        return $result['data'];
    }


    /**
     * @Notes: 获取 pic 项目图片的实际路径
     * @param $src
     * @return mixed|string
     * @author: tianyang
     * @Time: 2023/5/26 18:16
     */
    public function parsePathForPic($src) {
        if (strpos($src, PIC_URL) !== false) {
            if (strpos($src, '?') !== false) {
                $src .= '&parsePath=1';
            } else {
                $src .= '?parsePath=1';
            }
        }
        return $src;
    }

    /**
     * 用户历史鹰群id
     * <AUTHOR>
     * @date 230629
     */
    public function historicalGroupIds($userId) {
        $url = $this->_centerInterfaceUrl . 'get-user-historical-group-ids';
        $result = (new CommonCurl())->sendPostCurl($url, ['user_id' => $userId]);
        return empty($result['status'])? [] : $result['data'];
    }

    /**
     * Notes: 获取指定用户开通了某个应用的鹰群
     * Author: xie yuxiang
     * Date : 2023/9/20 11:20
     * @param $userId 用户ID
     * @param $applyId 应用ID
     * @param $companyId 公司ID
     */
    public function listUserApplyGroup($userId, $applyId, $companyId = 0)
    {
        // 接口地址
        $url = $this->_centerInterfaceUrl . 'list-user-apply-group';

        // 调用接口
        $result = (new Curl())->sendPostCurl($url, [
            'company_id' => $companyId,
            'user_id' => $userId,
            'app_id' => $applyId
        ]);

        if (isset($result['status']) && (1 == $result['status'])) {
            return $result['data'];
        }
        return [];
    }

    /**
     * Notes: 搜索库存领料
     * Author: zhhj
     * Date: 2021/11/9
     * @param $userId
     * @param $key
     * @param $smiles
     * @param int $inventoryId
     * @param int $stockType
     * @return array|mixed
     */
    public function getInventoryAndTemplate($userId) {
        $url = WMS_URL . 'api/get-inventory-and-template';

        $postData = [
            'user_id' => $userId,
        ];
        $result = (new CommonCurl())->sendPostCurl($url, $postData);

        if (!empty($result['status']) && !empty($result['data'])) {
            return $result['data'];
        }

        return [];
    }
}
